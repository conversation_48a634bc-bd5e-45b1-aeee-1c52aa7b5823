# Project Folder Structure Overview

Welcome to the **wyre-ui** project! This document provides a high-level overview of the main folders and their purposes, with details to help you navigate the codebase.

---

## Root Directory

- **environments/**
  - Environment-specific configuration files (e.g., for development, production).

- **public/**
  - Static assets served directly, such as images and third-party libraries (e.g., PDF viewer files, logos).

- **src/**
  - Main source code for the application. Most development work happens here.

- **package.json / package-lock.json**
  - Node.js project configuration and dependencies.

- **tsconfig\*.json**
  - TypeScript configuration files.

- **vite.config.ts**
  - Vite build tool configuration.

---

## Inside `src/`

- **api/**
  - API client code, models, and controllers for backend communication.

- **assets/**
  - Images and other static assets used within the app.

- **modules/**
  - Core logic and utilities, organized by feature:
    - **scopes/**: Scope-related logic and state.
    - **utils/**: Utility functions/constants.
    - **common/**: Shared UI/layout components.
    - **guards/**: Route guards for permissions/auth.
    - **error/**: Error boundaries and error pages.

- **routes/**
  - Application route definitions and shell components.

- **store/**
  - Global state management (e.g., Zustand stores).

- **theme/**
  - Theme and design tokens for consistent styling.

- **view/**
  - UI components and screens, organized by domain (see below).

- **index.css / vite-env.d.ts / main.tsx**
  - Entry points and global styles for the application.

---

### Inside `src/view/`

- **components/**
  - Reusable UI components, such as custom hooks and file uploaders, used across different screens.

- **screens/**
  - Main application screens, organized by domain. Each domain typically contains subfolders for specific features or pages:
    - **company/**: Company list/details screens.
    - **project/**: Project list/details screens.
    - **user/**: User list/details screens.
    - **login/**: Login page.
    - **admin/**: Admin dashboard and features:
      - **company/**: Company settings modal.
      - **integrations/**: Integration settings page.
      - **projects/**: Admin project management.
      - **reports/**: Admin reports dashboard and user/roles report.
      - **users/**: Admin user management.
    - **project-documents/**: Document list and upload screens for projects.
      - **documents-list/**: List of project documents.
      - **upload-documents/**: Upload documents UI.
    - **wyre-ai-admin/**: WyreAI admin dashboard and features:
      - **company-management/**: Company management for WyreAI admin.
      - **events/**: Event management.
      - **metrics/**: Metrics dashboard.
      - **projects/**: Project management for WyreAI admin.
      - **subscription-settings/**: Subscription and company export settings.
      - **user-management/**: User management for WyreAI admin.

---

## Notes

- Subfolders within each main folder are typically organized by feature or domain.
- PascalCase is used for component files (e.g., `UploadDocuments.tsx`, `UsersRolesReport.tsx`).
- This structure helps keep logic, UI, and domain-specific code organized and maintainable.
