name: wyre-ui Dev Deployment 
on:
  workflow_dispatch
jobs:
  deploy:
    name: Deploying wyre-ui dev
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          fetch-depth: 0
      - name: commands
        env:
         NODE_OPTIONS: --max-old-space-size=4096
        run: |
          cd ./ &&
          npm cache clean --force &&
          npm install &&
          npm run build:dev &&
          cd ./dist &&
          zip -r dist.zip ./
        continue-on-error: true
      - name: Copy files via ssh rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr 
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: /dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.REMOTE_USER }}
          
      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.DEV_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
          cd /home/<USER>
          #sudo rm -rf dist_bkp.zip &&
          sudo zip -r dist_bkp.zip dist && 
          sudo unzip -o -d "/home/<USER>/dist" /home/<USER>/dist.zip &&
          sudo chown -R ubuntu:ubuntu /home/<USER>
          sudo rm -rf /home/<USER>/dist.zip
          ' 
