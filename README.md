# React + Vite + TypeScript

This is a simple React project bootstrapped with [Vite](https://vitejs.dev/) and TypeScript.

## Prerequisites

Make sure you have the following installed:

- [Node.js](https://nodejs.org/) (LTS version recommended)
- [npm](https://www.npmjs.com/)

## Getting Started

### 1. Clone the repository

```sh
git clone https://github.com/WyreAI/wyre-ui.git
cd wyre-ui
```

### 2. Install dependencies

Using npm:

```sh
npm install
```

### 3. Start the development server

Using npm:

```sh
npm start
```

This will start the development server, and you can access the app at `http://localhost:3000/`.

## Building for Production

To create a production build, run:

```sh
npm run build
```

The output will be in the `dist` directory.

## Running the Preview Server

After building, you can preview the production build using:

```sh
npm run preview
```

## Linting & Formatting

To lint the code:

```sh
npm run lint
```

To format the code:

```sh
npm run format
```
