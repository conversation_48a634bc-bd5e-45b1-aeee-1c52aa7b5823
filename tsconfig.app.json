{"extends": "./tsconfig.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "composite": true, "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "isolatedModules": true, "moduleDetection": "force", "jsx": "react-jsx", "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "outDir": "dist", "declarationMap": true, "noImplicitReturns": true, "resolveJsonModule": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": "./", "paths": {"src/*": ["src/*"]}, "downlevelIteration": true, "skipDefaultLibCheck": true, "noImplicitThis": false, "preserveSymlinks": false}, "include": ["src"], "exclude": ["node_modules", "dist", "test", "**/*.spec.ts", "**/*.test.ts"]}