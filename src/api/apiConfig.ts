import axios from 'axios';
import { clearStorage } from 'src/modules/utils/storage';

const apiConfig = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true
});

apiConfig.interceptors.request.use(
  config => config,
  error => Promise.reject(error)
);

apiConfig.interceptors.response.use(
  response => response,
  async error => {
    if (error.response.status === 403 && error.response.data) {
      return Promise.reject(error.response.data);
    }

    if (error.response.status === 401 && error.response.data) {
      clearStorage();
      // Redirect to login page
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
      window.location.href = `${apiBaseUrl}/oauth2/authorization/keycloak`;
      return Promise.reject(error.response.data);
    }

    return Promise.reject(error);
  }
);

export default apiConfig;
