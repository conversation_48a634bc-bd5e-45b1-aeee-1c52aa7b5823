/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { SpecificationDTO } from '../models';
/**
 * SpecificationControllerApi - axios parameter creator
 * @export
 */
export const SpecificationControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSpecification: async (specificationDTO: SpecificationDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'specificationDTO' is not null or undefined
            assertParamExists('createSpecification', 'specificationDTO', specificationDTO)
            const localVarPath = `/api/specifications`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(specificationDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSpecification: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteSpecification', 'id', id)
            const localVarPath = `/api/specifications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getSpecificationById', 'id', id)
            const localVarPath = `/api/specifications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationsByProject: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getSpecificationsByProject', 'projectId', projectId)
            const localVarPath = `/api/specifications/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSpecification: async (id: number, specificationDTO: SpecificationDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateSpecification', 'id', id)
            // verify required parameter 'specificationDTO' is not null or undefined
            assertParamExists('updateSpecification', 'specificationDTO', specificationDTO)
            const localVarPath = `/api/specifications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(specificationDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SpecificationControllerApi - functional programming interface
 * @export
 */
export const SpecificationControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SpecificationControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSpecification(specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSpecification(specificationDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SpecificationControllerApi.createSpecification']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSpecification(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSpecification(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SpecificationControllerApi.deleteSpecification']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSpecificationById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSpecificationById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SpecificationControllerApi.getSpecificationById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSpecificationsByProject(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<SpecificationDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSpecificationsByProject(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SpecificationControllerApi.getSpecificationsByProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSpecification(id: number, specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSpecification(id, specificationDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SpecificationControllerApi.updateSpecification']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SpecificationControllerApi - factory interface
 * @export
 */
export const SpecificationControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SpecificationControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSpecification(specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO> {
            return localVarFp.createSpecification(specificationDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSpecification(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteSpecification(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO> {
            return localVarFp.getSpecificationById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationsByProject(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<SpecificationDTO>> {
            return localVarFp.getSpecificationsByProject(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {SpecificationDTO} specificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSpecification(id: number, specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO> {
            return localVarFp.updateSpecification(id, specificationDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SpecificationControllerApi - interface
 * @export
 * @interface SpecificationControllerApi
 */
export interface SpecificationControllerApiInterface {
    /**
     * 
     * @param {SpecificationDTO} specificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApiInterface
     */
    createSpecification(specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApiInterface
     */
    deleteSpecification(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApiInterface
     */
    getSpecificationById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApiInterface
     */
    getSpecificationsByProject(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<SpecificationDTO>>;

    /**
     * 
     * @param {number} id 
     * @param {SpecificationDTO} specificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApiInterface
     */
    updateSpecification(id: number, specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<SpecificationDTO>;

}

/**
 * SpecificationControllerApi - object-oriented interface
 * @export
 * @class SpecificationControllerApi
 * @extends {BaseAPI}
 */
export class SpecificationControllerApi extends BaseAPI implements SpecificationControllerApiInterface {
    /**
     * 
     * @param {SpecificationDTO} specificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApi
     */
    public createSpecification(specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig) {
        return SpecificationControllerApiFp(this.configuration).createSpecification(specificationDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApi
     */
    public deleteSpecification(id: number, options?: RawAxiosRequestConfig) {
        return SpecificationControllerApiFp(this.configuration).deleteSpecification(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApi
     */
    public getSpecificationById(id: number, options?: RawAxiosRequestConfig) {
        return SpecificationControllerApiFp(this.configuration).getSpecificationById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApi
     */
    public getSpecificationsByProject(projectId: number, options?: RawAxiosRequestConfig) {
        return SpecificationControllerApiFp(this.configuration).getSpecificationsByProject(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {SpecificationDTO} specificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SpecificationControllerApi
     */
    public updateSpecification(id: number, specificationDTO: SpecificationDTO, options?: RawAxiosRequestConfig) {
        return SpecificationControllerApiFp(this.configuration).updateSpecification(id, specificationDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

