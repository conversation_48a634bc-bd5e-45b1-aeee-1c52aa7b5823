/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { Contract } from '../models';
// @ts-ignore
import type { ContractCreateDTO } from '../models';
// @ts-ignore
import type { ContractUpdateDTO } from '../models';
/**
 * ContractControllerApi - axios parameter creator
 * @export
 */
export const ContractControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ContractCreateDTO} contractCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createContract: async (contractCreateDTO: ContractCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contractCreateDTO' is not null or undefined
            assertParamExists('createContract', 'contractCreateDTO', contractCreateDTO)
            const localVarPath = `/api/contracts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(contractCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteContract: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteContract', 'id', id)
            const localVarPath = `/api/contracts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllContracts: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/contracts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContractById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getContractById', 'id', id)
            const localVarPath = `/api/contracts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ContractUpdateDTO} contractUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContract: async (id: number, contractUpdateDTO: ContractUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateContract', 'id', id)
            // verify required parameter 'contractUpdateDTO' is not null or undefined
            assertParamExists('updateContract', 'contractUpdateDTO', contractUpdateDTO)
            const localVarPath = `/api/contracts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(contractUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ContractControllerApi - functional programming interface
 * @export
 */
export const ContractControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ContractControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ContractCreateDTO} contractCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createContract(contractCreateDTO: ContractCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Contract>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createContract(contractCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContractControllerApi.createContract']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteContract(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteContract(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContractControllerApi.deleteContract']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllContracts(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Contract>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllContracts(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContractControllerApi.getAllContracts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContractById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Contract>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContractById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContractControllerApi.getContractById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ContractUpdateDTO} contractUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateContract(id: number, contractUpdateDTO: ContractUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Contract>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateContract(id, contractUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContractControllerApi.updateContract']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ContractControllerApi - factory interface
 * @export
 */
export const ContractControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ContractControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ContractCreateDTO} contractCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createContract(contractCreateDTO: ContractCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<Contract> {
            return localVarFp.createContract(contractCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteContract(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteContract(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllContracts(options?: RawAxiosRequestConfig): AxiosPromise<Array<Contract>> {
            return localVarFp.getAllContracts(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContractById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<Contract> {
            return localVarFp.getContractById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ContractUpdateDTO} contractUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContract(id: number, contractUpdateDTO: ContractUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<Contract> {
            return localVarFp.updateContract(id, contractUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ContractControllerApi - interface
 * @export
 * @interface ContractControllerApi
 */
export interface ContractControllerApiInterface {
    /**
     * 
     * @param {ContractCreateDTO} contractCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApiInterface
     */
    createContract(contractCreateDTO: ContractCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<Contract>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApiInterface
     */
    deleteContract(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApiInterface
     */
    getAllContracts(options?: RawAxiosRequestConfig): AxiosPromise<Array<Contract>>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApiInterface
     */
    getContractById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<Contract>;

    /**
     * 
     * @param {number} id 
     * @param {ContractUpdateDTO} contractUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApiInterface
     */
    updateContract(id: number, contractUpdateDTO: ContractUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<Contract>;

}

/**
 * ContractControllerApi - object-oriented interface
 * @export
 * @class ContractControllerApi
 * @extends {BaseAPI}
 */
export class ContractControllerApi extends BaseAPI implements ContractControllerApiInterface {
    /**
     * 
     * @param {ContractCreateDTO} contractCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApi
     */
    public createContract(contractCreateDTO: ContractCreateDTO, options?: RawAxiosRequestConfig) {
        return ContractControllerApiFp(this.configuration).createContract(contractCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApi
     */
    public deleteContract(id: number, options?: RawAxiosRequestConfig) {
        return ContractControllerApiFp(this.configuration).deleteContract(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApi
     */
    public getAllContracts(options?: RawAxiosRequestConfig) {
        return ContractControllerApiFp(this.configuration).getAllContracts(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApi
     */
    public getContractById(id: number, options?: RawAxiosRequestConfig) {
        return ContractControllerApiFp(this.configuration).getContractById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ContractUpdateDTO} contractUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContractControllerApi
     */
    public updateContract(id: number, contractUpdateDTO: ContractUpdateDTO, options?: RawAxiosRequestConfig) {
        return ContractControllerApiFp(this.configuration).updateContract(id, contractUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

