/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { AuditLogDTO } from '../models';
/**
 * AuditLogControllerApi - axios parameter creator
 * @export
 */
export const AuditLogControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLogsByEntity: async (entityType: string, entityId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'entityType' is not null or undefined
            assertParamExists('getLogsByEntity', 'entityType', entityType)
            // verify required parameter 'entityId' is not null or undefined
            assertParamExists('getLogsByEntity', 'entityId', entityId)
            const localVarPath = `/api/audit-logs/entity/{entityType}/{entityId}`
                .replace(`{${"entityType"}}`, encodeURIComponent(String(entityType)))
                .replace(`{${"entityId"}}`, encodeURIComponent(String(entityId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLogsByUser: async (userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('getLogsByUser', 'userId', userId)
            const localVarPath = `/api/audit-logs/user/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} companyId 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {string} action 
         * @param {string} details 
         * @param {string} severity 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logAction: async (userId: number, companyId: number, entityType: string, entityId: number, action: string, details: string, severity: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('logAction', 'userId', userId)
            // verify required parameter 'companyId' is not null or undefined
            assertParamExists('logAction', 'companyId', companyId)
            // verify required parameter 'entityType' is not null or undefined
            assertParamExists('logAction', 'entityType', entityType)
            // verify required parameter 'entityId' is not null or undefined
            assertParamExists('logAction', 'entityId', entityId)
            // verify required parameter 'action' is not null or undefined
            assertParamExists('logAction', 'action', action)
            // verify required parameter 'details' is not null or undefined
            assertParamExists('logAction', 'details', details)
            // verify required parameter 'severity' is not null or undefined
            assertParamExists('logAction', 'severity', severity)
            const localVarPath = `/api/audit-logs/log`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (userId !== undefined) {
                localVarQueryParameter['userId'] = userId;
            }

            if (companyId !== undefined) {
                localVarQueryParameter['companyId'] = companyId;
            }

            if (entityType !== undefined) {
                localVarQueryParameter['entityType'] = entityType;
            }

            if (entityId !== undefined) {
                localVarQueryParameter['entityId'] = entityId;
            }

            if (action !== undefined) {
                localVarQueryParameter['action'] = action;
            }

            if (details !== undefined) {
                localVarQueryParameter['details'] = details;
            }

            if (severity !== undefined) {
                localVarQueryParameter['severity'] = severity;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuditLogControllerApi - functional programming interface
 * @export
 */
export const AuditLogControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuditLogControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLogsByEntity(entityType: string, entityId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<AuditLogDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getLogsByEntity(entityType, entityId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditLogControllerApi.getLogsByEntity']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLogsByUser(userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<AuditLogDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getLogsByUser(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditLogControllerApi.getLogsByUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} companyId 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {string} action 
         * @param {string} details 
         * @param {string} severity 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async logAction(userId: number, companyId: number, entityType: string, entityId: number, action: string, details: string, severity: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.logAction(userId, companyId, entityType, entityId, action, details, severity, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditLogControllerApi.logAction']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuditLogControllerApi - factory interface
 * @export
 */
export const AuditLogControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuditLogControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLogsByEntity(entityType: string, entityId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<AuditLogDTO>> {
            return localVarFp.getLogsByEntity(entityType, entityId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLogsByUser(userId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<AuditLogDTO>> {
            return localVarFp.getLogsByUser(userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} companyId 
         * @param {string} entityType 
         * @param {number} entityId 
         * @param {string} action 
         * @param {string} details 
         * @param {string} severity 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logAction(userId: number, companyId: number, entityType: string, entityId: number, action: string, details: string, severity: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.logAction(userId, companyId, entityType, entityId, action, details, severity, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AuditLogControllerApi - interface
 * @export
 * @interface AuditLogControllerApi
 */
export interface AuditLogControllerApiInterface {
    /**
     * 
     * @param {string} entityType 
     * @param {number} entityId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApiInterface
     */
    getLogsByEntity(entityType: string, entityId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<AuditLogDTO>>;

    /**
     * 
     * @param {number} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApiInterface
     */
    getLogsByUser(userId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<AuditLogDTO>>;

    /**
     * 
     * @param {number} userId 
     * @param {number} companyId 
     * @param {string} entityType 
     * @param {number} entityId 
     * @param {string} action 
     * @param {string} details 
     * @param {string} severity 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApiInterface
     */
    logAction(userId: number, companyId: number, entityType: string, entityId: number, action: string, details: string, severity: string, options?: RawAxiosRequestConfig): AxiosPromise<void>;

}

/**
 * AuditLogControllerApi - object-oriented interface
 * @export
 * @class AuditLogControllerApi
 * @extends {BaseAPI}
 */
export class AuditLogControllerApi extends BaseAPI implements AuditLogControllerApiInterface {
    /**
     * 
     * @param {string} entityType 
     * @param {number} entityId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApi
     */
    public getLogsByEntity(entityType: string, entityId: number, options?: RawAxiosRequestConfig) {
        return AuditLogControllerApiFp(this.configuration).getLogsByEntity(entityType, entityId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApi
     */
    public getLogsByUser(userId: number, options?: RawAxiosRequestConfig) {
        return AuditLogControllerApiFp(this.configuration).getLogsByUser(userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} userId 
     * @param {number} companyId 
     * @param {string} entityType 
     * @param {number} entityId 
     * @param {string} action 
     * @param {string} details 
     * @param {string} severity 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditLogControllerApi
     */
    public logAction(userId: number, companyId: number, entityType: string, entityId: number, action: string, details: string, severity: string, options?: RawAxiosRequestConfig) {
        return AuditLogControllerApiFp(this.configuration).logAction(userId, companyId, entityType, entityId, action, details, severity, options).then((request) => request(this.axios, this.basePath));
    }
}

