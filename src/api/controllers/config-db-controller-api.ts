/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ConfigDBDTO } from '../models';
/**
 * ConfigDbControllerApi - axios parameter creator
 * @export
 */
export const ConfigDbControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createConfig: async (configDBDTO: ConfigDBDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'configDBDTO' is not null or undefined
            assertParamExists('createConfig', 'configDBDTO', configDBDTO)
            const localVarPath = `/api/config`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(configDBDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllConfigs: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/config`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} configKey 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getConfig: async (configKey: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'configKey' is not null or undefined
            assertParamExists('getConfig', 'configKey', configKey)
            const localVarPath = `/api/config/{configKey}`
                .replace(`{${"configKey"}}`, encodeURIComponent(String(configKey)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateConfig: async (id: number, configDBDTO: ConfigDBDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateConfig', 'id', id)
            // verify required parameter 'configDBDTO' is not null or undefined
            assertParamExists('updateConfig', 'configDBDTO', configDBDTO)
            const localVarPath = `/api/config/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(configDBDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ConfigDbControllerApi - functional programming interface
 * @export
 */
export const ConfigDbControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ConfigDbControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createConfig(configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ConfigDBDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createConfig(configDBDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConfigDbControllerApi.createConfig']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllConfigs(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ConfigDBDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllConfigs(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConfigDbControllerApi.getAllConfigs']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} configKey 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getConfig(configKey: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ConfigDBDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getConfig(configKey, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConfigDbControllerApi.getConfig']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateConfig(id: number, configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ConfigDBDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateConfig(id, configDBDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConfigDbControllerApi.updateConfig']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ConfigDbControllerApi - factory interface
 * @export
 */
export const ConfigDbControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ConfigDbControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createConfig(configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO> {
            return localVarFp.createConfig(configDBDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllConfigs(options?: RawAxiosRequestConfig): AxiosPromise<Array<ConfigDBDTO>> {
            return localVarFp.getAllConfigs(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} configKey 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getConfig(configKey: string, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO> {
            return localVarFp.getConfig(configKey, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ConfigDBDTO} configDBDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateConfig(id: number, configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO> {
            return localVarFp.updateConfig(id, configDBDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ConfigDbControllerApi - interface
 * @export
 * @interface ConfigDbControllerApi
 */
export interface ConfigDbControllerApiInterface {
    /**
     * 
     * @param {ConfigDBDTO} configDBDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApiInterface
     */
    createConfig(configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO>;

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApiInterface
     */
    getAllConfigs(options?: RawAxiosRequestConfig): AxiosPromise<Array<ConfigDBDTO>>;

    /**
     * 
     * @param {string} configKey 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApiInterface
     */
    getConfig(configKey: string, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO>;

    /**
     * 
     * @param {number} id 
     * @param {ConfigDBDTO} configDBDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApiInterface
     */
    updateConfig(id: number, configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig): AxiosPromise<ConfigDBDTO>;

}

/**
 * ConfigDbControllerApi - object-oriented interface
 * @export
 * @class ConfigDbControllerApi
 * @extends {BaseAPI}
 */
export class ConfigDbControllerApi extends BaseAPI implements ConfigDbControllerApiInterface {
    /**
     * 
     * @param {ConfigDBDTO} configDBDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApi
     */
    public createConfig(configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig) {
        return ConfigDbControllerApiFp(this.configuration).createConfig(configDBDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApi
     */
    public getAllConfigs(options?: RawAxiosRequestConfig) {
        return ConfigDbControllerApiFp(this.configuration).getAllConfigs(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} configKey 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApi
     */
    public getConfig(configKey: string, options?: RawAxiosRequestConfig) {
        return ConfigDbControllerApiFp(this.configuration).getConfig(configKey, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ConfigDBDTO} configDBDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigDbControllerApi
     */
    public updateConfig(id: number, configDBDTO: ConfigDBDTO, options?: RawAxiosRequestConfig) {
        return ConfigDbControllerApiFp(this.configuration).updateConfig(id, configDBDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

