/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { NoteDTO } from '../models';
// @ts-ignore
import type { PageNoteDTO } from '../models';
/**
 * NoteControllerApi - axios parameter creator
 * @export
 */
export const NoteControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNote: async (noteDTO: NoteDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'noteDTO' is not null or undefined
            assertParamExists('createNote', 'noteDTO', noteDTO)
            const localVarPath = `/api/notes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(noteDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNote: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteNote', 'id', id)
            const localVarPath = `/api/notes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {number} [documentId] 
         * @param {number} [scopeId] 
         * @param {number} [bidItemId] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {number} [pageNumber] 
         * @param {string} [annotationData] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotes: async (id?: number, projectId?: number, documentId?: number, scopeId?: number, bidItemId?: number, sheetId?: number, specificationId?: number, pageNumber?: number, annotationData?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (documentId !== undefined) {
                localVarQueryParameter['documentId'] = documentId;
            }

            if (scopeId !== undefined) {
                localVarQueryParameter['scopeId'] = scopeId;
            }

            if (bidItemId !== undefined) {
                localVarQueryParameter['bidItemId'] = bidItemId;
            }

            if (sheetId !== undefined) {
                localVarQueryParameter['sheetId'] = sheetId;
            }

            if (specificationId !== undefined) {
                localVarQueryParameter['specificationId'] = specificationId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (annotationData !== undefined) {
                localVarQueryParameter['annotationData'] = annotationData;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNote: async (id: number, noteDTO: NoteDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateNote', 'id', id)
            // verify required parameter 'noteDTO' is not null or undefined
            assertParamExists('updateNote', 'noteDTO', noteDTO)
            const localVarPath = `/api/notes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(noteDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NoteControllerApi - functional programming interface
 * @export
 */
export const NoteControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = NoteControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createNote(noteDTO: NoteDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NoteDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createNote(noteDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NoteControllerApi.createNote']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNote(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteNote(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NoteControllerApi.deleteNote']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {number} [documentId] 
         * @param {number} [scopeId] 
         * @param {number} [bidItemId] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {number} [pageNumber] 
         * @param {string} [annotationData] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotes(id?: number, projectId?: number, documentId?: number, scopeId?: number, bidItemId?: number, sheetId?: number, specificationId?: number, pageNumber?: number, annotationData?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageNoteDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNotes(id, projectId, documentId, scopeId, bidItemId, sheetId, specificationId, pageNumber, annotationData, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NoteControllerApi.getNotes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateNote(id: number, noteDTO: NoteDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NoteDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateNote(id, noteDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NoteControllerApi.updateNote']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * NoteControllerApi - factory interface
 * @export
 */
export const NoteControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = NoteControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNote(noteDTO: NoteDTO, options?: RawAxiosRequestConfig): AxiosPromise<NoteDTO> {
            return localVarFp.createNote(noteDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNote(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteNote(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {number} [documentId] 
         * @param {number} [scopeId] 
         * @param {number} [bidItemId] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {number} [pageNumber] 
         * @param {string} [annotationData] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotes(id?: number, projectId?: number, documentId?: number, scopeId?: number, bidItemId?: number, sheetId?: number, specificationId?: number, pageNumber?: number, annotationData?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageNoteDTO> {
            return localVarFp.getNotes(id, projectId, documentId, scopeId, bidItemId, sheetId, specificationId, pageNumber, annotationData, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {NoteDTO} noteDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNote(id: number, noteDTO: NoteDTO, options?: RawAxiosRequestConfig): AxiosPromise<NoteDTO> {
            return localVarFp.updateNote(id, noteDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * NoteControllerApi - interface
 * @export
 * @interface NoteControllerApi
 */
export interface NoteControllerApiInterface {
    /**
     * 
     * @param {NoteDTO} noteDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApiInterface
     */
    createNote(noteDTO: NoteDTO, options?: RawAxiosRequestConfig): AxiosPromise<NoteDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApiInterface
     */
    deleteNote(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} [id] 
     * @param {number} [projectId] 
     * @param {number} [documentId] 
     * @param {number} [scopeId] 
     * @param {number} [bidItemId] 
     * @param {number} [sheetId] 
     * @param {number} [specificationId] 
     * @param {number} [pageNumber] 
     * @param {string} [annotationData] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApiInterface
     */
    getNotes(id?: number, projectId?: number, documentId?: number, scopeId?: number, bidItemId?: number, sheetId?: number, specificationId?: number, pageNumber?: number, annotationData?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageNoteDTO>;

    /**
     * 
     * @param {number} id 
     * @param {NoteDTO} noteDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApiInterface
     */
    updateNote(id: number, noteDTO: NoteDTO, options?: RawAxiosRequestConfig): AxiosPromise<NoteDTO>;

}

/**
 * NoteControllerApi - object-oriented interface
 * @export
 * @class NoteControllerApi
 * @extends {BaseAPI}
 */
export class NoteControllerApi extends BaseAPI implements NoteControllerApiInterface {
    /**
     * 
     * @param {NoteDTO} noteDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApi
     */
    public createNote(noteDTO: NoteDTO, options?: RawAxiosRequestConfig) {
        return NoteControllerApiFp(this.configuration).createNote(noteDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApi
     */
    public deleteNote(id: number, options?: RawAxiosRequestConfig) {
        return NoteControllerApiFp(this.configuration).deleteNote(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [id] 
     * @param {number} [projectId] 
     * @param {number} [documentId] 
     * @param {number} [scopeId] 
     * @param {number} [bidItemId] 
     * @param {number} [sheetId] 
     * @param {number} [specificationId] 
     * @param {number} [pageNumber] 
     * @param {string} [annotationData] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApi
     */
    public getNotes(id?: number, projectId?: number, documentId?: number, scopeId?: number, bidItemId?: number, sheetId?: number, specificationId?: number, pageNumber?: number, annotationData?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return NoteControllerApiFp(this.configuration).getNotes(id, projectId, documentId, scopeId, bidItemId, sheetId, specificationId, pageNumber, annotationData, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {NoteDTO} noteDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NoteControllerApi
     */
    public updateNote(id: number, noteDTO: NoteDTO, options?: RawAxiosRequestConfig) {
        return NoteControllerApiFp(this.configuration).updateNote(id, noteDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

