/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageScopeDTO } from '../models';
// @ts-ignore
import type { ScopeCreateDTO } from '../models';
// @ts-ignore
import type { ScopeDTO } from '../models';
// @ts-ignore
import type { ScopeUpdateDTO } from '../models';
/**
 * ScopeControllerApi - axios parameter creator
 * @export
 */
export const ScopeControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ScopeCreateDTO} scopeCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createScope: async (scopeCreateDTO: ScopeCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeCreateDTO' is not null or undefined
            assertParamExists('createScope', 'scopeCreateDTO', scopeCreateDTO)
            const localVarPath = `/api/scopes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(scopeCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteScope1: async (scopeId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('deleteScope1', 'scopeId', scopeId)
            const localVarPath = `/api/scopes/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopeById: async (scopeId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('getScopeById', 'scopeId', scopeId)
            const localVarPath = `/api/scopes/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [projectId] 
         * @param {string} [division] 
         * @param {string} [name] 
         * @param {string} [discipline] 
         * @param {string} [documentVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopes: async (projectId?: number, division?: string, name?: string, discipline?: string, documentVersion?: string, page?: number, size?: number, sort?: Array<string>, sheetId?: number, specificationId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/scopes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (division !== undefined) {
                localVarQueryParameter['division'] = division;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (discipline !== undefined) {
                localVarQueryParameter['discipline'] = discipline;
            }

            if (documentVersion !== undefined) {
                localVarQueryParameter['documentVersion'] = documentVersion;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (sheetId !== undefined) {
                localVarQueryParameter['sheetId'] = sheetId;
            }

            if (specificationId !== undefined) {
                localVarQueryParameter['specificationId'] = specificationId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [status] 
         * @param {string} [category] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getScopesByProject: async (projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, status?: string, category?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getScopesByProject', 'projectId', projectId)
            const localVarPath = `/api/scopes/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }

            if (category !== undefined) {
                localVarQueryParameter['category'] = category;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} documentVersion 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopesByProjectAndDocumentVersion: async (projectId: number, documentVersion: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getScopesByProjectAndDocumentVersion', 'projectId', projectId)
            // verify required parameter 'documentVersion' is not null or undefined
            assertParamExists('getScopesByProjectAndDocumentVersion', 'documentVersion', documentVersion)
            const localVarPath = `/api/scopes/version/{projectId}/{documentVersion}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)))
                .replace(`{${"documentVersion"}}`, encodeURIComponent(String(documentVersion)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {ScopeUpdateDTO} scopeUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateScope: async (scopeId: number, scopeUpdateDTO: ScopeUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('updateScope', 'scopeId', scopeId)
            // verify required parameter 'scopeUpdateDTO' is not null or undefined
            assertParamExists('updateScope', 'scopeUpdateDTO', scopeUpdateDTO)
            const localVarPath = `/api/scopes/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(scopeUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ScopeControllerApi - functional programming interface
 * @export
 */
export const ScopeControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ScopeControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ScopeCreateDTO} scopeCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createScope(scopeCreateDTO: ScopeCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createScope(scopeCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.createScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteScope1(scopeId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteScope1(scopeId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.deleteScope1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScopeById(scopeId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopeById(scopeId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.getScopeById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [projectId] 
         * @param {string} [division] 
         * @param {string} [name] 
         * @param {string} [discipline] 
         * @param {string} [documentVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScopes(projectId?: number, division?: string, name?: string, discipline?: string, documentVersion?: string, page?: number, size?: number, sort?: Array<string>, sheetId?: number, specificationId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopes(projectId, division, name, discipline, documentVersion, page, size, sort, sheetId, specificationId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.getScopes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [status] 
         * @param {string} [category] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getScopesByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, status?: string, category?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopesByProject(projectId, name, page, size, sort, status, category, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.getScopesByProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} documentVersion 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScopesByProjectAndDocumentVersion(projectId: number, documentVersion: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopesByProjectAndDocumentVersion(projectId, documentVersion, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.getScopesByProjectAndDocumentVersion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {ScopeUpdateDTO} scopeUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateScope(scopeId: number, scopeUpdateDTO: ScopeUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateScope(scopeId, scopeUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeControllerApi.updateScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ScopeControllerApi - factory interface
 * @export
 */
export const ScopeControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ScopeControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ScopeCreateDTO} scopeCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createScope(scopeCreateDTO: ScopeCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO> {
            return localVarFp.createScope(scopeCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteScope1(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteScope1(scopeId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopeById(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO> {
            return localVarFp.getScopeById(scopeId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [projectId] 
         * @param {string} [division] 
         * @param {string} [name] 
         * @param {string} [discipline] 
         * @param {string} [documentVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {number} [sheetId] 
         * @param {number} [specificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopes(projectId?: number, division?: string, name?: string, discipline?: string, documentVersion?: string, page?: number, size?: number, sort?: Array<string>, sheetId?: number, specificationId?: number, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO> {
            return localVarFp.getScopes(projectId, division, name, discipline, documentVersion, page, size, sort, sheetId, specificationId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [status] 
         * @param {string} [category] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getScopesByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, status?: string, category?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO> {
            return localVarFp.getScopesByProject(projectId, name, page, size, sort, status, category, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} documentVersion 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopesByProjectAndDocumentVersion(projectId: number, documentVersion: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO> {
            return localVarFp.getScopesByProjectAndDocumentVersion(projectId, documentVersion, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {ScopeUpdateDTO} scopeUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateScope(scopeId: number, scopeUpdateDTO: ScopeUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO> {
            return localVarFp.updateScope(scopeId, scopeUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ScopeControllerApi - interface
 * @export
 * @interface ScopeControllerApi
 */
export interface ScopeControllerApiInterface {
    /**
     * 
     * @param {ScopeCreateDTO} scopeCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    createScope(scopeCreateDTO: ScopeCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO>;

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    deleteScope1(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    getScopeById(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO>;

    /**
     * 
     * @param {number} [projectId] 
     * @param {string} [division] 
     * @param {string} [name] 
     * @param {string} [discipline] 
     * @param {string} [documentVersion] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {number} [sheetId] 
     * @param {number} [specificationId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    getScopes(projectId?: number, division?: string, name?: string, discipline?: string, documentVersion?: string, page?: number, size?: number, sort?: Array<string>, sheetId?: number, specificationId?: number, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [status] 
     * @param {string} [category] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    getScopesByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, status?: string, category?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} documentVersion 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    getScopesByProjectAndDocumentVersion(projectId: number, documentVersion: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageScopeDTO>;

    /**
     * 
     * @param {number} scopeId 
     * @param {ScopeUpdateDTO} scopeUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApiInterface
     */
    updateScope(scopeId: number, scopeUpdateDTO: ScopeUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScopeDTO>;

}

/**
 * ScopeControllerApi - object-oriented interface
 * @export
 * @class ScopeControllerApi
 * @extends {BaseAPI}
 */
export class ScopeControllerApi extends BaseAPI implements ScopeControllerApiInterface {
    /**
     * 
     * @param {ScopeCreateDTO} scopeCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public createScope(scopeCreateDTO: ScopeCreateDTO, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).createScope(scopeCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public deleteScope1(scopeId: number, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).deleteScope1(scopeId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public getScopeById(scopeId: number, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).getScopeById(scopeId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [projectId] 
     * @param {string} [division] 
     * @param {string} [name] 
     * @param {string} [discipline] 
     * @param {string} [documentVersion] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {number} [sheetId] 
     * @param {number} [specificationId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public getScopes(projectId?: number, division?: string, name?: string, discipline?: string, documentVersion?: string, page?: number, size?: number, sort?: Array<string>, sheetId?: number, specificationId?: number, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).getScopes(projectId, division, name, discipline, documentVersion, page, size, sort, sheetId, specificationId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [status] 
     * @param {string} [category] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public getScopesByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, status?: string, category?: string, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).getScopesByProject(projectId, name, page, size, sort, status, category, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} documentVersion 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public getScopesByProjectAndDocumentVersion(projectId: number, documentVersion: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).getScopesByProjectAndDocumentVersion(projectId, documentVersion, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {ScopeUpdateDTO} scopeUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeControllerApi
     */
    public updateScope(scopeId: number, scopeUpdateDTO: ScopeUpdateDTO, options?: RawAxiosRequestConfig) {
        return ScopeControllerApiFp(this.configuration).updateScope(scopeId, scopeUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

