/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageSubscriptionPlan } from '../models';
// @ts-ignore
import type { SubscriptionPlanDTO } from '../models';
/**
 * SubscriptionPlanControllerApi - axios parameter creator
 * @export
 */
export const SubscriptionPlanControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPlan: async (subscriptionPlanDTO: SubscriptionPlanDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'subscriptionPlanDTO' is not null or undefined
            assertParamExists('createPlan', 'subscriptionPlanDTO', subscriptionPlanDTO)
            const localVarPath = `/api/subscription-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(subscriptionPlanDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePlan: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deletePlan', 'id', id)
            const localVarPath = `/api/subscription-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPlanById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getPlanById', 'id', id)
            const localVarPath = `/api/subscription-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [name] 
         * @param {boolean} [isBundle] 
         * @param {string} [validityPeriod] 
         * @param {boolean} [isActive] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPlans: async (name?: string, isBundle?: boolean, validityPeriod?: string, isActive?: boolean, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/subscription-plans/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (isBundle !== undefined) {
                localVarQueryParameter['isBundle'] = isBundle;
            }

            if (validityPeriod !== undefined) {
                localVarQueryParameter['validityPeriod'] = validityPeriod;
            }

            if (isActive !== undefined) {
                localVarQueryParameter['isActive'] = isActive;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePlan: async (id: number, subscriptionPlanDTO: SubscriptionPlanDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updatePlan', 'id', id)
            // verify required parameter 'subscriptionPlanDTO' is not null or undefined
            assertParamExists('updatePlan', 'subscriptionPlanDTO', subscriptionPlanDTO)
            const localVarPath = `/api/subscription-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(subscriptionPlanDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SubscriptionPlanControllerApi - functional programming interface
 * @export
 */
export const SubscriptionPlanControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SubscriptionPlanControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createPlan(subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubscriptionPlanDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createPlan(subscriptionPlanDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubscriptionPlanControllerApi.createPlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deletePlan(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deletePlan(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubscriptionPlanControllerApi.deletePlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPlanById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubscriptionPlanDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPlanById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubscriptionPlanControllerApi.getPlanById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [name] 
         * @param {boolean} [isBundle] 
         * @param {string} [validityPeriod] 
         * @param {boolean} [isActive] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPlans(name?: string, isBundle?: boolean, validityPeriod?: string, isActive?: boolean, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageSubscriptionPlan>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPlans(name, isBundle, validityPeriod, isActive, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubscriptionPlanControllerApi.getPlans']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updatePlan(id: number, subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubscriptionPlanDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updatePlan(id, subscriptionPlanDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubscriptionPlanControllerApi.updatePlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SubscriptionPlanControllerApi - factory interface
 * @export
 */
export const SubscriptionPlanControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SubscriptionPlanControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPlan(subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO> {
            return localVarFp.createPlan(subscriptionPlanDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePlan(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deletePlan(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPlanById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO> {
            return localVarFp.getPlanById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [name] 
         * @param {boolean} [isBundle] 
         * @param {string} [validityPeriod] 
         * @param {boolean} [isActive] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPlans(name?: string, isBundle?: boolean, validityPeriod?: string, isActive?: boolean, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSubscriptionPlan> {
            return localVarFp.getPlans(name, isBundle, validityPeriod, isActive, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePlan(id: number, subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO> {
            return localVarFp.updatePlan(id, subscriptionPlanDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SubscriptionPlanControllerApi - interface
 * @export
 * @interface SubscriptionPlanControllerApi
 */
export interface SubscriptionPlanControllerApiInterface {
    /**
     * 
     * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApiInterface
     */
    createPlan(subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApiInterface
     */
    deletePlan(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApiInterface
     */
    getPlanById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO>;

    /**
     * 
     * @param {string} [name] 
     * @param {boolean} [isBundle] 
     * @param {string} [validityPeriod] 
     * @param {boolean} [isActive] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApiInterface
     */
    getPlans(name?: string, isBundle?: boolean, validityPeriod?: string, isActive?: boolean, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSubscriptionPlan>;

    /**
     * 
     * @param {number} id 
     * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApiInterface
     */
    updatePlan(id: number, subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig): AxiosPromise<SubscriptionPlanDTO>;

}

/**
 * SubscriptionPlanControllerApi - object-oriented interface
 * @export
 * @class SubscriptionPlanControllerApi
 * @extends {BaseAPI}
 */
export class SubscriptionPlanControllerApi extends BaseAPI implements SubscriptionPlanControllerApiInterface {
    /**
     * 
     * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApi
     */
    public createPlan(subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig) {
        return SubscriptionPlanControllerApiFp(this.configuration).createPlan(subscriptionPlanDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApi
     */
    public deletePlan(id: number, options?: RawAxiosRequestConfig) {
        return SubscriptionPlanControllerApiFp(this.configuration).deletePlan(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApi
     */
    public getPlanById(id: number, options?: RawAxiosRequestConfig) {
        return SubscriptionPlanControllerApiFp(this.configuration).getPlanById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [name] 
     * @param {boolean} [isBundle] 
     * @param {string} [validityPeriod] 
     * @param {boolean} [isActive] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApi
     */
    public getPlans(name?: string, isBundle?: boolean, validityPeriod?: string, isActive?: boolean, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return SubscriptionPlanControllerApiFp(this.configuration).getPlans(name, isBundle, validityPeriod, isActive, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {SubscriptionPlanDTO} subscriptionPlanDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubscriptionPlanControllerApi
     */
    public updatePlan(id: number, subscriptionPlanDTO: SubscriptionPlanDTO, options?: RawAxiosRequestConfig) {
        return SubscriptionPlanControllerApiFp(this.configuration).updatePlan(id, subscriptionPlanDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

