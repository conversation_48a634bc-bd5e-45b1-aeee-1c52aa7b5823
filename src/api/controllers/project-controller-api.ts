/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { DocumentDTO } from '../models';
// @ts-ignore
import type { DocumentUploadConfirmDTO } from '../models';
// @ts-ignore
import type { PageProjectDTO } from '../models';
// @ts-ignore
import type { ProjectCreateDTO } from '../models';
// @ts-ignore
import type { ProjectDTO } from '../models';
// @ts-ignore
import type { ProjectUpdateDTO } from '../models';
// @ts-ignore
import type { ProjectWithDocumentsDTO } from '../models';
// @ts-ignore
import type { ProjectWithPresignedUrlsDTO } from '../models';
// @ts-ignore
import type { SseEmitter } from '../models';
/**
 * ProjectControllerApi - axios parameter creator
 * @export
 */
export const ProjectControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateProject: async (projectId: number, modifiedByUserId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('activateProject', 'projectId', projectId)
            // verify required parameter 'modifiedByUserId' is not null or undefined
            assertParamExists('activateProject', 'modifiedByUserId', modifiedByUserId)
            const localVarPath = `/api/projects/{projectId}/activate`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (modifiedByUserId !== undefined) {
                localVarQueryParameter['modifiedByUserId'] = modifiedByUserId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        confirmDocumentUpload: async (projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('confirmDocumentUpload', 'projectId', projectId)
            // verify required parameter 'documentSetVersionId' is not null or undefined
            assertParamExists('confirmDocumentUpload', 'documentSetVersionId', documentSetVersionId)
            // verify required parameter 'documentUploadConfirmDTO' is not null or undefined
            assertParamExists('confirmDocumentUpload', 'documentUploadConfirmDTO', documentUploadConfirmDTO)
            const localVarPath = `/api/projects/{projectId}/{documentSetVersionId}/documents/confirm-upload`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)))
                .replace(`{${"documentSetVersionId"}}`, encodeURIComponent(String(documentSetVersionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(documentUploadConfirmDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Create a new project and get pre-signed URLs for document uploads
         * @summary Create a project with pre-signed URLs
         * @param {ProjectCreateDTO} projectCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectWithPresignedUrls: async (projectCreateDTO: ProjectCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectCreateDTO' is not null or undefined
            assertParamExists('createProjectWithPresignedUrls', 'projectCreateDTO', projectCreateDTO)
            const localVarPath = `/api/projects`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deactivateProject: async (projectId: number, modifiedByUserId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('deactivateProject', 'projectId', projectId)
            // verify required parameter 'modifiedByUserId' is not null or undefined
            assertParamExists('deactivateProject', 'modifiedByUserId', modifiedByUserId)
            const localVarPath = `/api/projects/{projectId}/deactivate`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (modifiedByUserId !== undefined) {
                localVarQueryParameter['modifiedByUserId'] = modifiedByUserId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Retrieve a list of all projects with optional filters
         * @summary Get all projects
         * @param {number} [companyId] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {string} [location] 
         * @param {GetAllProjectsStatusEnum} [status] 
         * @param {string} [owner] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllProjects: async (companyId?: number, name?: string, description?: string, location?: string, status?: GetAllProjectsStatusEnum, owner?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/projects`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (companyId !== undefined) {
                localVarQueryParameter['companyId'] = companyId;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (description !== undefined) {
                localVarQueryParameter['description'] = description;
            }

            if (location !== undefined) {
                localVarQueryParameter['location'] = location;
            }

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }

            if (owner !== undefined) {
                localVarQueryParameter['owner'] = owner;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectById: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectById', 'projectId', projectId)
            const localVarPath = `/api/projects/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Retrieve project details along with all associated documents
         * @summary Get project details with documents
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectWithDocuments: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectWithDocuments', 'projectId', projectId)
            const localVarPath = `/api/projects/{projectId}/documents`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        processingStatus: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('processingStatus', 'projectId', projectId)
            const localVarPath = `/api/projects/status/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        softDeleteProject: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('softDeleteProject', 'id', id)
            const localVarPath = `/api/projects/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {ProjectUpdateDTO} projectUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProject: async (projectId: number, projectUpdateDTO: ProjectUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('updateProject', 'projectId', projectId)
            // verify required parameter 'projectUpdateDTO' is not null or undefined
            assertParamExists('updateProject', 'projectUpdateDTO', projectUpdateDTO)
            const localVarPath = `/api/projects/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectControllerApi - functional programming interface
 * @export
 */
export const ProjectControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.activateProject(projectId, modifiedByUserId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.activateProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async confirmDocumentUpload(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DocumentDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.confirmDocumentUpload(projectId, documentSetVersionId, documentUploadConfirmDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.confirmDocumentUpload']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Create a new project and get pre-signed URLs for document uploads
         * @summary Create a project with pre-signed URLs
         * @param {ProjectCreateDTO} projectCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createProjectWithPresignedUrls(projectCreateDTO: ProjectCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectWithPresignedUrlsDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createProjectWithPresignedUrls(projectCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.createProjectWithPresignedUrls']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deactivateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deactivateProject(projectId, modifiedByUserId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.deactivateProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Retrieve a list of all projects with optional filters
         * @summary Get all projects
         * @param {number} [companyId] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {string} [location] 
         * @param {GetAllProjectsStatusEnum} [status] 
         * @param {string} [owner] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllProjects(companyId?: number, name?: string, description?: string, location?: string, status?: GetAllProjectsStatusEnum, owner?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageProjectDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllProjects(companyId, name, description, location, status, owner, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.getAllProjects']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectById(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectById(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.getProjectById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Retrieve project details along with all associated documents
         * @summary Get project details with documents
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectWithDocuments(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectWithDocumentsDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectWithDocuments(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.getProjectWithDocuments']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async processingStatus(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SseEmitter>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.processingStatus(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.processingStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async softDeleteProject(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.softDeleteProject(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.softDeleteProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {ProjectUpdateDTO} projectUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateProject(projectId: number, projectUpdateDTO: ProjectUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateProject(projectId, projectUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectControllerApi.updateProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectControllerApi - factory interface
 * @export
 */
export const ProjectControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.activateProject(projectId, modifiedByUserId, options).then((request) => request(axios, basePath));
        },
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        confirmDocumentUpload(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): AxiosPromise<Array<DocumentDTO>> {
            return localVarFp.confirmDocumentUpload(projectId, documentSetVersionId, documentUploadConfirmDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * Create a new project and get pre-signed URLs for document uploads
         * @summary Create a project with pre-signed URLs
         * @param {ProjectCreateDTO} projectCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectWithPresignedUrls(projectCreateDTO: ProjectCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectWithPresignedUrlsDTO> {
            return localVarFp.createProjectWithPresignedUrls(projectCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} modifiedByUserId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deactivateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deactivateProject(projectId, modifiedByUserId, options).then((request) => request(axios, basePath));
        },
        /**
         * Retrieve a list of all projects with optional filters
         * @summary Get all projects
         * @param {number} [companyId] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {string} [location] 
         * @param {GetAllProjectsStatusEnum} [status] 
         * @param {string} [owner] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllProjects(companyId?: number, name?: string, description?: string, location?: string, status?: GetAllProjectsStatusEnum, owner?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectDTO> {
            return localVarFp.getAllProjects(companyId, name, description, location, status, owner, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectById(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDTO> {
            return localVarFp.getProjectById(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * Retrieve project details along with all associated documents
         * @summary Get project details with documents
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectWithDocuments(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectWithDocumentsDTO> {
            return localVarFp.getProjectWithDocuments(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        processingStatus(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<SseEmitter> {
            return localVarFp.processingStatus(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        softDeleteProject(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.softDeleteProject(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {ProjectUpdateDTO} projectUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProject(projectId: number, projectUpdateDTO: ProjectUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDTO> {
            return localVarFp.updateProject(projectId, projectUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectControllerApi - interface
 * @export
 * @interface ProjectControllerApi
 */
export interface ProjectControllerApiInterface {
    /**
     * 
     * @param {number} projectId 
     * @param {number} modifiedByUserId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    activateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * After successfully uploading documents to S3, save their metadata in the database
     * @summary Confirm document upload
     * @param {number} projectId 
     * @param {number} documentSetVersionId 
     * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    confirmDocumentUpload(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): AxiosPromise<Array<DocumentDTO>>;

    /**
     * Create a new project and get pre-signed URLs for document uploads
     * @summary Create a project with pre-signed URLs
     * @param {ProjectCreateDTO} projectCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    createProjectWithPresignedUrls(projectCreateDTO: ProjectCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectWithPresignedUrlsDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {number} modifiedByUserId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    deactivateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * Retrieve a list of all projects with optional filters
     * @summary Get all projects
     * @param {number} [companyId] 
     * @param {string} [name] 
     * @param {string} [description] 
     * @param {string} [location] 
     * @param {GetAllProjectsStatusEnum} [status] 
     * @param {string} [owner] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    getAllProjects(companyId?: number, name?: string, description?: string, location?: string, status?: GetAllProjectsStatusEnum, owner?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    getProjectById(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDTO>;

    /**
     * Retrieve project details along with all associated documents
     * @summary Get project details with documents
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    getProjectWithDocuments(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectWithDocumentsDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    processingStatus(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<SseEmitter>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    softDeleteProject(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} projectId 
     * @param {ProjectUpdateDTO} projectUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApiInterface
     */
    updateProject(projectId: number, projectUpdateDTO: ProjectUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDTO>;

}

/**
 * ProjectControllerApi - object-oriented interface
 * @export
 * @class ProjectControllerApi
 * @extends {BaseAPI}
 */
export class ProjectControllerApi extends BaseAPI implements ProjectControllerApiInterface {
    /**
     * 
     * @param {number} projectId 
     * @param {number} modifiedByUserId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public activateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).activateProject(projectId, modifiedByUserId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * After successfully uploading documents to S3, save their metadata in the database
     * @summary Confirm document upload
     * @param {number} projectId 
     * @param {number} documentSetVersionId 
     * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public confirmDocumentUpload(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).confirmDocumentUpload(projectId, documentSetVersionId, documentUploadConfirmDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Create a new project and get pre-signed URLs for document uploads
     * @summary Create a project with pre-signed URLs
     * @param {ProjectCreateDTO} projectCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public createProjectWithPresignedUrls(projectCreateDTO: ProjectCreateDTO, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).createProjectWithPresignedUrls(projectCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {number} modifiedByUserId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public deactivateProject(projectId: number, modifiedByUserId: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).deactivateProject(projectId, modifiedByUserId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Retrieve a list of all projects with optional filters
     * @summary Get all projects
     * @param {number} [companyId] 
     * @param {string} [name] 
     * @param {string} [description] 
     * @param {string} [location] 
     * @param {GetAllProjectsStatusEnum} [status] 
     * @param {string} [owner] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public getAllProjects(companyId?: number, name?: string, description?: string, location?: string, status?: GetAllProjectsStatusEnum, owner?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).getAllProjects(companyId, name, description, location, status, owner, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public getProjectById(projectId: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).getProjectById(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Retrieve project details along with all associated documents
     * @summary Get project details with documents
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public getProjectWithDocuments(projectId: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).getProjectWithDocuments(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public processingStatus(projectId: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).processingStatus(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public softDeleteProject(id: number, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).softDeleteProject(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {ProjectUpdateDTO} projectUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectControllerApi
     */
    public updateProject(projectId: number, projectUpdateDTO: ProjectUpdateDTO, options?: RawAxiosRequestConfig) {
        return ProjectControllerApiFp(this.configuration).updateProject(projectId, projectUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAllProjectsStatusEnum = {
    Initialized: 'INITIALIZED',
    DocumentsUploaded: 'DOCUMENTS_UPLOADED',
    Processing: 'PROCESSING',
    Completed: 'COMPLETED',
    Failed: 'FAILED'
} as const;
export type GetAllProjectsStatusEnum = typeof GetAllProjectsStatusEnum[keyof typeof GetAllProjectsStatusEnum];
