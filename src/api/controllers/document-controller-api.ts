/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { DocumentDTO } from '../models';
// @ts-ignore
import type { DocumentUploadConfirmDTO } from '../models';
// @ts-ignore
import type { PageDocumentVersionDTO } from '../models';
// @ts-ignore
import type { PageDocumentWithPresignedUrlDTO } from '../models';
// @ts-ignore
import type { PresignedUrlResponse } from '../models';
/**
 * DocumentControllerApi - axios parameter creator
 * @export
 */
export const DocumentControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        confirmDocumentUpload1: async (projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('confirmDocumentUpload1', 'projectId', projectId)
            // verify required parameter 'documentSetVersionId' is not null or undefined
            assertParamExists('confirmDocumentUpload1', 'documentSetVersionId', documentSetVersionId)
            // verify required parameter 'documentUploadConfirmDTO' is not null or undefined
            assertParamExists('confirmDocumentUpload1', 'documentUploadConfirmDTO', documentUploadConfirmDTO)
            const localVarPath = `/api/documents/confirm-upload`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (documentSetVersionId !== undefined) {
                localVarQueryParameter['documentSetVersionId'] = documentSetVersionId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(documentUploadConfirmDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<{ [key: string]: string; }>} requestBody 
         * @param {number} [documentSetId] 
         * @param {string} [documentSetVersion] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateUrlsToUpload: async (projectId: number, requestBody: Array<{ [key: string]: string; }>, documentSetId?: number, documentSetVersion?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('generateUrlsToUpload', 'projectId', projectId)
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('generateUrlsToUpload', 'requestBody', requestBody)
            const localVarPath = `/api/documents/generateUrlsToUpload`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (documentSetId !== undefined) {
                localVarQueryParameter['documentSetId'] = documentSetId;
            }

            if (documentSetVersion !== undefined) {
                localVarQueryParameter['documentSetVersion'] = documentSetVersion;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [version] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentVersions: async (projectId: number, version?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectDocumentVersions', 'projectId', projectId)
            const localVarPath = `/api/documents/projects/{projectId}/versions`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (version !== undefined) {
                localVarQueryParameter['version'] = version;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<string>} [version] 
         * @param {Array<string>} [title] 
         * @param {Array<string>} [createdBy] 
         * @param {Array<number>} [createdBetween] 
         * @param {Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>} [inputDocumentType] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentsWithVersions: async (projectId: number, version?: Array<string>, title?: Array<string>, createdBy?: Array<string>, createdBetween?: Array<number>, inputDocumentType?: Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectDocumentsWithVersions', 'projectId', projectId)
            const localVarPath = `/api/documents`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (version) {
                localVarQueryParameter['version'] = version;
            }

            if (title) {
                localVarQueryParameter['title'] = title;
            }

            if (createdBy) {
                localVarQueryParameter['createdBy'] = createdBy;
            }

            if (createdBetween) {
                localVarQueryParameter['createdBetween'] = createdBetween;
            }

            if (inputDocumentType) {
                localVarQueryParameter['inputDocumentType'] = inputDocumentType;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DocumentControllerApi - functional programming interface
 * @export
 */
export const DocumentControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DocumentControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async confirmDocumentUpload1(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DocumentDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.confirmDocumentUpload1(projectId, documentSetVersionId, documentUploadConfirmDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DocumentControllerApi.confirmDocumentUpload1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<{ [key: string]: string; }>} requestBody 
         * @param {number} [documentSetId] 
         * @param {string} [documentSetVersion] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateUrlsToUpload(projectId: number, requestBody: Array<{ [key: string]: string; }>, documentSetId?: number, documentSetVersion?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<PresignedUrlResponse>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.generateUrlsToUpload(projectId, requestBody, documentSetId, documentSetVersion, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DocumentControllerApi.generateUrlsToUpload']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [version] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectDocumentVersions(projectId: number, version?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageDocumentVersionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectDocumentVersions(projectId, version, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DocumentControllerApi.getProjectDocumentVersions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<string>} [version] 
         * @param {Array<string>} [title] 
         * @param {Array<string>} [createdBy] 
         * @param {Array<number>} [createdBetween] 
         * @param {Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>} [inputDocumentType] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectDocumentsWithVersions(projectId: number, version?: Array<string>, title?: Array<string>, createdBy?: Array<string>, createdBetween?: Array<number>, inputDocumentType?: Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageDocumentWithPresignedUrlDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectDocumentsWithVersions(projectId, version, title, createdBy, createdBetween, inputDocumentType, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DocumentControllerApi.getProjectDocumentsWithVersions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DocumentControllerApi - factory interface
 * @export
 */
export const DocumentControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DocumentControllerApiFp(configuration)
    return {
        /**
         * After successfully uploading documents to S3, save their metadata in the database
         * @summary Confirm document upload
         * @param {number} projectId 
         * @param {number} documentSetVersionId 
         * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        confirmDocumentUpload1(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): AxiosPromise<Array<DocumentDTO>> {
            return localVarFp.confirmDocumentUpload1(projectId, documentSetVersionId, documentUploadConfirmDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<{ [key: string]: string; }>} requestBody 
         * @param {number} [documentSetId] 
         * @param {string} [documentSetVersion] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateUrlsToUpload(projectId: number, requestBody: Array<{ [key: string]: string; }>, documentSetId?: number, documentSetVersion?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<PresignedUrlResponse>> {
            return localVarFp.generateUrlsToUpload(projectId, requestBody, documentSetId, documentSetVersion, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [version] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentVersions(projectId: number, version?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageDocumentVersionDTO> {
            return localVarFp.getProjectDocumentVersions(projectId, version, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {Array<string>} [version] 
         * @param {Array<string>} [title] 
         * @param {Array<string>} [createdBy] 
         * @param {Array<number>} [createdBetween] 
         * @param {Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>} [inputDocumentType] 
         * @param {number} [page] Zero-based page index (0..N)
         * @param {number} [size] The size of the page to be returned
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentsWithVersions(projectId: number, version?: Array<string>, title?: Array<string>, createdBy?: Array<string>, createdBetween?: Array<number>, inputDocumentType?: Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageDocumentWithPresignedUrlDTO> {
            return localVarFp.getProjectDocumentsWithVersions(projectId, version, title, createdBy, createdBetween, inputDocumentType, page, size, sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DocumentControllerApi - interface
 * @export
 * @interface DocumentControllerApi
 */
export interface DocumentControllerApiInterface {
    /**
     * After successfully uploading documents to S3, save their metadata in the database
     * @summary Confirm document upload
     * @param {number} projectId 
     * @param {number} documentSetVersionId 
     * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApiInterface
     */
    confirmDocumentUpload1(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig): AxiosPromise<Array<DocumentDTO>>;

    /**
     * 
     * @param {number} projectId 
     * @param {Array<{ [key: string]: string; }>} requestBody 
     * @param {number} [documentSetId] 
     * @param {string} [documentSetVersion] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApiInterface
     */
    generateUrlsToUpload(projectId: number, requestBody: Array<{ [key: string]: string; }>, documentSetId?: number, documentSetVersion?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<PresignedUrlResponse>>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} [version] 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApiInterface
     */
    getProjectDocumentVersions(projectId: number, version?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageDocumentVersionDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {Array<string>} [version] 
     * @param {Array<string>} [title] 
     * @param {Array<string>} [createdBy] 
     * @param {Array<number>} [createdBetween] 
     * @param {Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>} [inputDocumentType] 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApiInterface
     */
    getProjectDocumentsWithVersions(projectId: number, version?: Array<string>, title?: Array<string>, createdBy?: Array<string>, createdBetween?: Array<number>, inputDocumentType?: Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageDocumentWithPresignedUrlDTO>;

}

/**
 * DocumentControllerApi - object-oriented interface
 * @export
 * @class DocumentControllerApi
 * @extends {BaseAPI}
 */
export class DocumentControllerApi extends BaseAPI implements DocumentControllerApiInterface {
    /**
     * After successfully uploading documents to S3, save their metadata in the database
     * @summary Confirm document upload
     * @param {number} projectId 
     * @param {number} documentSetVersionId 
     * @param {Array<DocumentUploadConfirmDTO>} documentUploadConfirmDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApi
     */
    public confirmDocumentUpload1(projectId: number, documentSetVersionId: number, documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>, options?: RawAxiosRequestConfig) {
        return DocumentControllerApiFp(this.configuration).confirmDocumentUpload1(projectId, documentSetVersionId, documentUploadConfirmDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {Array<{ [key: string]: string; }>} requestBody 
     * @param {number} [documentSetId] 
     * @param {string} [documentSetVersion] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApi
     */
    public generateUrlsToUpload(projectId: number, requestBody: Array<{ [key: string]: string; }>, documentSetId?: number, documentSetVersion?: string, options?: RawAxiosRequestConfig) {
        return DocumentControllerApiFp(this.configuration).generateUrlsToUpload(projectId, requestBody, documentSetId, documentSetVersion, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} [version] 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApi
     */
    public getProjectDocumentVersions(projectId: number, version?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return DocumentControllerApiFp(this.configuration).getProjectDocumentVersions(projectId, version, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {Array<string>} [version] 
     * @param {Array<string>} [title] 
     * @param {Array<string>} [createdBy] 
     * @param {Array<number>} [createdBetween] 
     * @param {Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>} [inputDocumentType] 
     * @param {number} [page] Zero-based page index (0..N)
     * @param {number} [size] The size of the page to be returned
     * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DocumentControllerApi
     */
    public getProjectDocumentsWithVersions(projectId: number, version?: Array<string>, title?: Array<string>, createdBy?: Array<string>, createdBetween?: Array<number>, inputDocumentType?: Array<GetProjectDocumentsWithVersionsInputDocumentTypeEnum>, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return DocumentControllerApiFp(this.configuration).getProjectDocumentsWithVersions(projectId, version, title, createdBy, createdBetween, inputDocumentType, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetProjectDocumentsWithVersionsInputDocumentTypeEnum = {
    Drawing: 'DRAWING',
    Specification: 'SPECIFICATION'
} as const;
export type GetProjectDocumentsWithVersionsInputDocumentTypeEnum = typeof GetProjectDocumentsWithVersionsInputDocumentTypeEnum[keyof typeof GetProjectDocumentsWithVersionsInputDocumentTypeEnum];
