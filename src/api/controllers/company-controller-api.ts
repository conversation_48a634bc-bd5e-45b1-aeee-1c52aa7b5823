/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CompanyDTO } from '../models';
// @ts-ignore
import type { PageCompanyDTO } from '../models';
/**
 * CompanyControllerApi - axios parameter creator
 * @export
 */
export const CompanyControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCompany: async (companyDTO: CompanyDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'companyDTO' is not null or undefined
            assertParamExists('createCompany', 'companyDTO', companyDTO)
            const localVarPath = `/api/companies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(companyDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCompany: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteCompany', 'id', id)
            const localVarPath = `/api/companies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetAllCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getAllCompanies: async (page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetAllCompaniesSubscriptionStatusEnum, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/companies/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (industry !== undefined) {
                localVarQueryParameter['industry'] = industry;
            }

            if (subscriptionStatus !== undefined) {
                localVarQueryParameter['subscriptionStatus'] = subscriptionStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanies: async (page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetCompaniesSubscriptionStatusEnum, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/companies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (industry !== undefined) {
                localVarQueryParameter['industry'] = industry;
            }

            if (subscriptionStatus !== undefined) {
                localVarQueryParameter['subscriptionStatus'] = subscriptionStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanyById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getCompanyById', 'id', id)
            const localVarPath = `/api/companies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCompany: async (id: number, companyDTO: CompanyDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateCompany', 'id', id)
            // verify required parameter 'companyDTO' is not null or undefined
            assertParamExists('updateCompany', 'companyDTO', companyDTO)
            const localVarPath = `/api/companies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(companyDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CompanyControllerApi - functional programming interface
 * @export
 */
export const CompanyControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CompanyControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCompany(companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanyDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCompany(companyDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.createCompany']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCompany(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCompany(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.deleteCompany']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetAllCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getAllCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetAllCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageCompanyDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllCompanies(page, size, sort, name, industry, subscriptionStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.getAllCompanies']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCompanies(page, size, sort, name, industry, subscriptionStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.getCompanies']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCompanyById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanyDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCompanyById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.getCompanyById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCompany(id: number, companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanyDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCompany(id, companyDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanyControllerApi.updateCompany']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CompanyControllerApi - factory interface
 * @export
 */
export const CompanyControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CompanyControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCompany(companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO> {
            return localVarFp.createCompany(companyDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCompany(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteCompany(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetAllCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getAllCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetAllCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): AxiosPromise<PageCompanyDTO> {
            return localVarFp.getAllCompanies(page, size, sort, name, industry, subscriptionStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [industry] 
         * @param {GetCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.getCompanies(page, size, sort, name, industry, subscriptionStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanyById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO> {
            return localVarFp.getCompanyById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanyDTO} companyDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCompany(id: number, companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO> {
            return localVarFp.updateCompany(id, companyDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CompanyControllerApi - interface
 * @export
 * @interface CompanyControllerApi
 */
export interface CompanyControllerApiInterface {
    /**
     * 
     * @param {CompanyDTO} companyDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    createCompany(companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    deleteCompany(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [industry] 
     * @param {GetAllCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    getAllCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetAllCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): AxiosPromise<PageCompanyDTO>;

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [industry] 
     * @param {GetCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    getCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig): AxiosPromise<object>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    getCompanyById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO>;

    /**
     * 
     * @param {number} id 
     * @param {CompanyDTO} companyDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApiInterface
     */
    updateCompany(id: number, companyDTO: CompanyDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanyDTO>;

}

/**
 * CompanyControllerApi - object-oriented interface
 * @export
 * @class CompanyControllerApi
 * @extends {BaseAPI}
 */
export class CompanyControllerApi extends BaseAPI implements CompanyControllerApiInterface {
    /**
     * 
     * @param {CompanyDTO} companyDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public createCompany(companyDTO: CompanyDTO, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).createCompany(companyDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public deleteCompany(id: number, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).deleteCompany(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [industry] 
     * @param {GetAllCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public getAllCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetAllCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).getAllCompanies(page, size, sort, name, industry, subscriptionStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [industry] 
     * @param {GetCompaniesSubscriptionStatusEnum} [subscriptionStatus] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public getCompanies(page?: number, size?: number, sort?: Array<string>, name?: string, industry?: string, subscriptionStatus?: GetCompaniesSubscriptionStatusEnum, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).getCompanies(page, size, sort, name, industry, subscriptionStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public getCompanyById(id: number, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).getCompanyById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {CompanyDTO} companyDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanyControllerApi
     */
    public updateCompany(id: number, companyDTO: CompanyDTO, options?: RawAxiosRequestConfig) {
        return CompanyControllerApiFp(this.configuration).updateCompany(id, companyDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAllCompaniesSubscriptionStatusEnum = {
    Active: 'ACTIVE',
    Trial: 'TRIAL',
    Suspended: 'SUSPENDED'
} as const;
export type GetAllCompaniesSubscriptionStatusEnum = typeof GetAllCompaniesSubscriptionStatusEnum[keyof typeof GetAllCompaniesSubscriptionStatusEnum];
/**
 * @export
 */
export const GetCompaniesSubscriptionStatusEnum = {
    Active: 'ACTIVE',
    Trial: 'TRIAL',
    Suspended: 'SUSPENDED'
} as const;
export type GetCompaniesSubscriptionStatusEnum = typeof GetCompaniesSubscriptionStatusEnum[keyof typeof GetCompaniesSubscriptionStatusEnum];
