/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { OpenSearchDTO } from '../models';
/**
 * OpenSearchControllerApi - axios parameter creator
 * @export
 */
export const OpenSearchControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} query 
         * @param {Array<number>} embedding 
         * @param {string} index 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        hybridSearch: async (query: string, embedding: Array<number>, index: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'query' is not null or undefined
            assertParamExists('hybridSearch', 'query', query)
            // verify required parameter 'embedding' is not null or undefined
            assertParamExists('hybridSearch', 'embedding', embedding)
            // verify required parameter 'index' is not null or undefined
            assertParamExists('hybridSearch', 'index', index)
            const localVarPath = `/api/search/hybrid`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (query !== undefined) {
                localVarQueryParameter['query'] = query;
            }

            if (embedding) {
                localVarQueryParameter['embedding'] = embedding;
            }

            if (index !== undefined) {
                localVarQueryParameter['index'] = index;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} embedding 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchByEmbedding: async (embedding: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'embedding' is not null or undefined
            assertParamExists('searchByEmbedding', 'embedding', embedding)
            const localVarPath = `/api/search/semantic`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (embedding !== undefined) {
                localVarQueryParameter['embedding'] = embedding;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [searchText] 
         * @param {number} [scopeId] 
         * @param {number} [bidsheetId] 
         * @param {string} [scopedDrawingId] 
         * @param {string} [scopedSpecificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchByText: async (projectId: number, searchText?: string, scopeId?: number, bidsheetId?: number, scopedDrawingId?: string, scopedSpecificationId?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('searchByText', 'projectId', projectId)
            const localVarPath = `/api/search/text`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (searchText !== undefined) {
                localVarQueryParameter['searchText'] = searchText;
            }

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (scopeId !== undefined) {
                localVarQueryParameter['scopeId'] = scopeId;
            }

            if (bidsheetId !== undefined) {
                localVarQueryParameter['bidsheetId'] = bidsheetId;
            }

            if (scopedDrawingId !== undefined) {
                localVarQueryParameter['scopedDrawingId'] = scopedDrawingId;
            }

            if (scopedSpecificationId !== undefined) {
                localVarQueryParameter['scopedSpecificationId'] = scopedSpecificationId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * OpenSearchControllerApi - functional programming interface
 * @export
 */
export const OpenSearchControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = OpenSearchControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} query 
         * @param {Array<number>} embedding 
         * @param {string} index 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async hybridSearch(query: string, embedding: Array<number>, index: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.hybridSearch(query, embedding, index, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OpenSearchControllerApi.hybridSearch']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} embedding 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchByEmbedding(embedding: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchByEmbedding(embedding, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OpenSearchControllerApi.searchByEmbedding']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [searchText] 
         * @param {number} [scopeId] 
         * @param {number} [bidsheetId] 
         * @param {string} [scopedDrawingId] 
         * @param {string} [scopedSpecificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchByText(projectId: number, searchText?: string, scopeId?: number, bidsheetId?: number, scopedDrawingId?: string, scopedSpecificationId?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OpenSearchDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchByText(projectId, searchText, scopeId, bidsheetId, scopedDrawingId, scopedSpecificationId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OpenSearchControllerApi.searchByText']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * OpenSearchControllerApi - factory interface
 * @export
 */
export const OpenSearchControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = OpenSearchControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {string} query 
         * @param {Array<number>} embedding 
         * @param {string} index 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        hybridSearch(query: string, embedding: Array<number>, index: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>> {
            return localVarFp.hybridSearch(query, embedding, index, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} embedding 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchByEmbedding(embedding: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>> {
            return localVarFp.searchByEmbedding(embedding, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [searchText] 
         * @param {number} [scopeId] 
         * @param {number} [bidsheetId] 
         * @param {string} [scopedDrawingId] 
         * @param {string} [scopedSpecificationId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchByText(projectId: number, searchText?: string, scopeId?: number, bidsheetId?: number, scopedDrawingId?: string, scopedSpecificationId?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<OpenSearchDTO>> {
            return localVarFp.searchByText(projectId, searchText, scopeId, bidsheetId, scopedDrawingId, scopedSpecificationId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * OpenSearchControllerApi - interface
 * @export
 * @interface OpenSearchControllerApi
 */
export interface OpenSearchControllerApiInterface {
    /**
     * 
     * @param {string} query 
     * @param {Array<number>} embedding 
     * @param {string} index 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApiInterface
     */
    hybridSearch(query: string, embedding: Array<number>, index: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>>;

    /**
     * 
     * @param {string} embedding 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApiInterface
     */
    searchByEmbedding(embedding: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} [searchText] 
     * @param {number} [scopeId] 
     * @param {number} [bidsheetId] 
     * @param {string} [scopedDrawingId] 
     * @param {string} [scopedSpecificationId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApiInterface
     */
    searchByText(projectId: number, searchText?: string, scopeId?: number, bidsheetId?: number, scopedDrawingId?: string, scopedSpecificationId?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<OpenSearchDTO>>;

}

/**
 * OpenSearchControllerApi - object-oriented interface
 * @export
 * @class OpenSearchControllerApi
 * @extends {BaseAPI}
 */
export class OpenSearchControllerApi extends BaseAPI implements OpenSearchControllerApiInterface {
    /**
     * 
     * @param {string} query 
     * @param {Array<number>} embedding 
     * @param {string} index 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApi
     */
    public hybridSearch(query: string, embedding: Array<number>, index: string, options?: RawAxiosRequestConfig) {
        return OpenSearchControllerApiFp(this.configuration).hybridSearch(query, embedding, index, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} embedding 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApi
     */
    public searchByEmbedding(embedding: string, options?: RawAxiosRequestConfig) {
        return OpenSearchControllerApiFp(this.configuration).searchByEmbedding(embedding, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} [searchText] 
     * @param {number} [scopeId] 
     * @param {number} [bidsheetId] 
     * @param {string} [scopedDrawingId] 
     * @param {string} [scopedSpecificationId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenSearchControllerApi
     */
    public searchByText(projectId: number, searchText?: string, scopeId?: number, bidsheetId?: number, scopedDrawingId?: string, scopedSpecificationId?: string, options?: RawAxiosRequestConfig) {
        return OpenSearchControllerApiFp(this.configuration).searchByText(projectId, searchText, scopeId, bidsheetId, scopedDrawingId, scopedSpecificationId, options).then((request) => request(this.axios, this.basePath));
    }
}

