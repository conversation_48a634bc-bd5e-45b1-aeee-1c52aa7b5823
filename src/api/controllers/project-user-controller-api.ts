/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageProjectUserDTO } from '../models';
// @ts-ignore
import type { ProjectUserCreateDTO } from '../models';
// @ts-ignore
import type { ProjectUserDTO } from '../models';
// @ts-ignore
import type { ProjectUserUpdateDTO } from '../models';
/**
 * ProjectUserControllerApi - axios parameter creator
 * @export
 */
export const ProjectUserControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ProjectUserCreateDTO} projectUserCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectUser: async (projectUserCreateDTO: ProjectUserCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectUserCreateDTO' is not null or undefined
            assertParamExists('createProjectUser', 'projectUserCreateDTO', projectUserCreateDTO)
            const localVarPath = `/api/project-users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectUserCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProjectUser: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteProjectUser', 'id', id)
            const localVarPath = `/api/project-users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUserById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getProjectUserById', 'id', id)
            const localVarPath = `/api/project-users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUsersByProject: async (projectId: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectUsersByProject', 'projectId', projectId)
            const localVarPath = `/api/project-users/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sortBy] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUsersByUser: async (userId: number, page?: number, size?: number, sortBy?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('getProjectUsersByUser', 'userId', userId)
            const localVarPath = `/api/project-users/user/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sortBy) {
                localVarQueryParameter['sortBy'] = sortBy;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectUserUpdateDTO} projectUserUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProjectUser: async (id: number, projectUserUpdateDTO: ProjectUserUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateProjectUser', 'id', id)
            // verify required parameter 'projectUserUpdateDTO' is not null or undefined
            assertParamExists('updateProjectUser', 'projectUserUpdateDTO', projectUserUpdateDTO)
            const localVarPath = `/api/project-users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectUserUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectUserControllerApi - functional programming interface
 * @export
 */
export const ProjectUserControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectUserControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ProjectUserCreateDTO} projectUserCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createProjectUser(projectUserCreateDTO: ProjectUserCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectUserDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createProjectUser(projectUserCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.createProjectUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteProjectUser(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteProjectUser(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.deleteProjectUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectUserById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectUserDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectUserById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.getProjectUserById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectUsersByProject(projectId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageProjectUserDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectUsersByProject(projectId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.getProjectUsersByProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sortBy] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectUsersByUser(userId: number, page?: number, size?: number, sortBy?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageProjectUserDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectUsersByUser(userId, page, size, sortBy, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.getProjectUsersByUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectUserUpdateDTO} projectUserUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateProjectUser(id: number, projectUserUpdateDTO: ProjectUserUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectUserDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateProjectUser(id, projectUserUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectUserControllerApi.updateProjectUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectUserControllerApi - factory interface
 * @export
 */
export const ProjectUserControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectUserControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ProjectUserCreateDTO} projectUserCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectUser(projectUserCreateDTO: ProjectUserCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO> {
            return localVarFp.createProjectUser(projectUserCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProjectUser(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteProjectUser(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUserById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO> {
            return localVarFp.getProjectUserById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUsersByProject(projectId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectUserDTO> {
            return localVarFp.getProjectUsersByProject(projectId, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} userId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sortBy] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectUsersByUser(userId: number, page?: number, size?: number, sortBy?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectUserDTO> {
            return localVarFp.getProjectUsersByUser(userId, page, size, sortBy, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectUserUpdateDTO} projectUserUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProjectUser(id: number, projectUserUpdateDTO: ProjectUserUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO> {
            return localVarFp.updateProjectUser(id, projectUserUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectUserControllerApi - interface
 * @export
 * @interface ProjectUserControllerApi
 */
export interface ProjectUserControllerApiInterface {
    /**
     * 
     * @param {ProjectUserCreateDTO} projectUserCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    createProjectUser(projectUserCreateDTO: ProjectUserCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    deleteProjectUser(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    getProjectUserById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    getProjectUsersByProject(projectId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectUserDTO>;

    /**
     * 
     * @param {number} userId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sortBy] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    getProjectUsersByUser(userId: number, page?: number, size?: number, sortBy?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectUserDTO>;

    /**
     * 
     * @param {number} id 
     * @param {ProjectUserUpdateDTO} projectUserUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApiInterface
     */
    updateProjectUser(id: number, projectUserUpdateDTO: ProjectUserUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectUserDTO>;

}

/**
 * ProjectUserControllerApi - object-oriented interface
 * @export
 * @class ProjectUserControllerApi
 * @extends {BaseAPI}
 */
export class ProjectUserControllerApi extends BaseAPI implements ProjectUserControllerApiInterface {
    /**
     * 
     * @param {ProjectUserCreateDTO} projectUserCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public createProjectUser(projectUserCreateDTO: ProjectUserCreateDTO, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).createProjectUser(projectUserCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public deleteProjectUser(id: number, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).deleteProjectUser(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public getProjectUserById(id: number, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).getProjectUserById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public getProjectUsersByProject(projectId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).getProjectUsersByProject(projectId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} userId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sortBy] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public getProjectUsersByUser(userId: number, page?: number, size?: number, sortBy?: Array<string>, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).getProjectUsersByUser(userId, page, size, sortBy, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ProjectUserUpdateDTO} projectUserUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectUserControllerApi
     */
    public updateProjectUser(id: number, projectUserUpdateDTO: ProjectUserUpdateDTO, options?: RawAxiosRequestConfig) {
        return ProjectUserControllerApiFp(this.configuration).updateProjectUser(id, projectUserUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

