/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { DocumentSetVersionDTO } from '../models';
// @ts-ignore
import type { PageProjectDocumentSetDTO } from '../models';
// @ts-ignore
import type { ProjectDocumentSetDTO } from '../models';
/**
 * ProjectDocumentSetControllerApi - axios parameter creator
 * @export
 */
export const ProjectDocumentSetControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ProjectDocumentSetDTO} projectDocumentSetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDocumentSet: async (projectDocumentSetDTO: ProjectDocumentSetDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectDocumentSetDTO' is not null or undefined
            assertParamExists('createDocumentSet', 'projectDocumentSetDTO', projectDocumentSetDTO)
            const localVarPath = `/api/project-document-sets`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectDocumentSetDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {DocumentSetVersionDTO} documentSetVersionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDocumentSetVersion: async (projectId: number, documentSetVersionDTO: DocumentSetVersionDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('createDocumentSetVersion', 'projectId', projectId)
            // verify required parameter 'documentSetVersionDTO' is not null or undefined
            assertParamExists('createDocumentSetVersion', 'documentSetVersionDTO', documentSetVersionDTO)
            const localVarPath = `/api/project-document-sets/{projectId}/version`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(documentSetVersionDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDocumentSet: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteDocumentSet', 'id', id)
            const localVarPath = `/api/project-document-sets/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDocumentSetByProjectAndVersion: async (projectId: number, version: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getDocumentSetByProjectAndVersion', 'projectId', projectId)
            // verify required parameter 'version' is not null or undefined
            assertParamExists('getDocumentSetByProjectAndVersion', 'version', version)
            const localVarPath = `/api/project-document-sets/{projectId}/{version}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)))
                .replace(`{${"version"}}`, encodeURIComponent(String(version)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [documentSetVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentSetsByProjectId: async (projectId: number, documentSetVersion?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getProjectDocumentSetsByProjectId', 'projectId', projectId)
            const localVarPath = `/api/project-document-sets/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (documentSetVersion !== undefined) {
                localVarQueryParameter['documentSetVersion'] = documentSetVersion;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectDocumentSetControllerApi - functional programming interface
 * @export
 */
export const ProjectDocumentSetControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectDocumentSetControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ProjectDocumentSetDTO} projectDocumentSetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDocumentSet(projectDocumentSetDTO: ProjectDocumentSetDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectDocumentSetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDocumentSet(projectDocumentSetDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectDocumentSetControllerApi.createDocumentSet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {DocumentSetVersionDTO} documentSetVersionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDocumentSetVersion(projectId: number, documentSetVersionDTO: DocumentSetVersionDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectDocumentSetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDocumentSetVersion(projectId, documentSetVersionDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectDocumentSetControllerApi.createDocumentSetVersion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDocumentSet(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDocumentSet(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectDocumentSetControllerApi.deleteDocumentSet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDocumentSetByProjectAndVersion(projectId: number, version: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectDocumentSetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDocumentSetByProjectAndVersion(projectId, version, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectDocumentSetControllerApi.getDocumentSetByProjectAndVersion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [documentSetVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectDocumentSetsByProjectId(projectId: number, documentSetVersion?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageProjectDocumentSetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectDocumentSetsByProjectId(projectId, documentSetVersion, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectDocumentSetControllerApi.getProjectDocumentSetsByProjectId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectDocumentSetControllerApi - factory interface
 * @export
 */
export const ProjectDocumentSetControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectDocumentSetControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ProjectDocumentSetDTO} projectDocumentSetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDocumentSet(projectDocumentSetDTO: ProjectDocumentSetDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO> {
            return localVarFp.createDocumentSet(projectDocumentSetDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {DocumentSetVersionDTO} documentSetVersionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDocumentSetVersion(projectId: number, documentSetVersionDTO: DocumentSetVersionDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO> {
            return localVarFp.createDocumentSetVersion(projectId, documentSetVersionDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDocumentSet(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDocumentSet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDocumentSetByProjectAndVersion(projectId: number, version: string, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO> {
            return localVarFp.getDocumentSetByProjectAndVersion(projectId, version, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [documentSetVersion] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectDocumentSetsByProjectId(projectId: number, documentSetVersion?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectDocumentSetDTO> {
            return localVarFp.getProjectDocumentSetsByProjectId(projectId, documentSetVersion, page, size, sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectDocumentSetControllerApi - interface
 * @export
 * @interface ProjectDocumentSetControllerApi
 */
export interface ProjectDocumentSetControllerApiInterface {
    /**
     * 
     * @param {ProjectDocumentSetDTO} projectDocumentSetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApiInterface
     */
    createDocumentSet(projectDocumentSetDTO: ProjectDocumentSetDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {DocumentSetVersionDTO} documentSetVersionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApiInterface
     */
    createDocumentSetVersion(projectId: number, documentSetVersionDTO: DocumentSetVersionDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApiInterface
     */
    deleteDocumentSet(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} version 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApiInterface
     */
    getDocumentSetByProjectAndVersion(projectId: number, version: string, options?: RawAxiosRequestConfig): AxiosPromise<ProjectDocumentSetDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} [documentSetVersion] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApiInterface
     */
    getProjectDocumentSetsByProjectId(projectId: number, documentSetVersion?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectDocumentSetDTO>;

}

/**
 * ProjectDocumentSetControllerApi - object-oriented interface
 * @export
 * @class ProjectDocumentSetControllerApi
 * @extends {BaseAPI}
 */
export class ProjectDocumentSetControllerApi extends BaseAPI implements ProjectDocumentSetControllerApiInterface {
    /**
     * 
     * @param {ProjectDocumentSetDTO} projectDocumentSetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApi
     */
    public createDocumentSet(projectDocumentSetDTO: ProjectDocumentSetDTO, options?: RawAxiosRequestConfig) {
        return ProjectDocumentSetControllerApiFp(this.configuration).createDocumentSet(projectDocumentSetDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {DocumentSetVersionDTO} documentSetVersionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApi
     */
    public createDocumentSetVersion(projectId: number, documentSetVersionDTO: DocumentSetVersionDTO, options?: RawAxiosRequestConfig) {
        return ProjectDocumentSetControllerApiFp(this.configuration).createDocumentSetVersion(projectId, documentSetVersionDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApi
     */
    public deleteDocumentSet(id: number, options?: RawAxiosRequestConfig) {
        return ProjectDocumentSetControllerApiFp(this.configuration).deleteDocumentSet(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} version 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApi
     */
    public getDocumentSetByProjectAndVersion(projectId: number, version: string, options?: RawAxiosRequestConfig) {
        return ProjectDocumentSetControllerApiFp(this.configuration).getDocumentSetByProjectAndVersion(projectId, version, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} [documentSetVersion] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectDocumentSetControllerApi
     */
    public getProjectDocumentSetsByProjectId(projectId: number, documentSetVersion?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return ProjectDocumentSetControllerApiFp(this.configuration).getProjectDocumentSetsByProjectId(projectId, documentSetVersion, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }
}

