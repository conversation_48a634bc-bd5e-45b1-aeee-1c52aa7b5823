/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ManufacturerDTO } from '../models';
/**
 * ManufacturerControllerApi - axios parameter creator
 * @export
 */
export const ManufacturerControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getManufacturerById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getManufacturerById', 'id', id)
            const localVarPath = `/api/manufacturers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ManufacturerDTO} manufacturerDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateManufacturer: async (id: number, manufacturerDTO: ManufacturerDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateManufacturer', 'id', id)
            // verify required parameter 'manufacturerDTO' is not null or undefined
            assertParamExists('updateManufacturer', 'manufacturerDTO', manufacturerDTO)
            const localVarPath = `/api/manufacturers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(manufacturerDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ManufacturerControllerApi - functional programming interface
 * @export
 */
export const ManufacturerControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ManufacturerControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getManufacturerById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ManufacturerDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getManufacturerById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ManufacturerControllerApi.getManufacturerById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ManufacturerDTO} manufacturerDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateManufacturer(id: number, manufacturerDTO: ManufacturerDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ManufacturerDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateManufacturer(id, manufacturerDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ManufacturerControllerApi.updateManufacturer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ManufacturerControllerApi - factory interface
 * @export
 */
export const ManufacturerControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ManufacturerControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getManufacturerById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ManufacturerDTO> {
            return localVarFp.getManufacturerById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ManufacturerDTO} manufacturerDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateManufacturer(id: number, manufacturerDTO: ManufacturerDTO, options?: RawAxiosRequestConfig): AxiosPromise<ManufacturerDTO> {
            return localVarFp.updateManufacturer(id, manufacturerDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ManufacturerControllerApi - interface
 * @export
 * @interface ManufacturerControllerApi
 */
export interface ManufacturerControllerApiInterface {
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ManufacturerControllerApiInterface
     */
    getManufacturerById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ManufacturerDTO>;

    /**
     * 
     * @param {number} id 
     * @param {ManufacturerDTO} manufacturerDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ManufacturerControllerApiInterface
     */
    updateManufacturer(id: number, manufacturerDTO: ManufacturerDTO, options?: RawAxiosRequestConfig): AxiosPromise<ManufacturerDTO>;

}

/**
 * ManufacturerControllerApi - object-oriented interface
 * @export
 * @class ManufacturerControllerApi
 * @extends {BaseAPI}
 */
export class ManufacturerControllerApi extends BaseAPI implements ManufacturerControllerApiInterface {
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ManufacturerControllerApi
     */
    public getManufacturerById(id: number, options?: RawAxiosRequestConfig) {
        return ManufacturerControllerApiFp(this.configuration).getManufacturerById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ManufacturerDTO} manufacturerDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ManufacturerControllerApi
     */
    public updateManufacturer(id: number, manufacturerDTO: ManufacturerDTO, options?: RawAxiosRequestConfig) {
        return ManufacturerControllerApiFp(this.configuration).updateManufacturer(id, manufacturerDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

