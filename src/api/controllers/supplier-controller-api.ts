/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { SupplierDTO } from '../models';
/**
 * SupplierControllerApi - axios parameter creator
 * @export
 */
export const SupplierControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        approveSupplier: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('approveSupplier', 'id', id)
            const localVarPath = `/api/suppliers/{id}/approve`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSupplier: async (supplierDTO: SupplierDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'supplierDTO' is not null or undefined
            assertParamExists('createSupplier', 'supplierDTO', supplierDTO)
            const localVarPath = `/api/suppliers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(supplierDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSupplier: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteSupplier', 'id', id)
            const localVarPath = `/api/suppliers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSuppliers: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/suppliers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} email 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSupplierByEmail: async (email: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'email' is not null or undefined
            assertParamExists('getSupplierByEmail', 'email', email)
            const localVarPath = `/api/suppliers/email/{email}`
                .replace(`{${"email"}}`, encodeURIComponent(String(email)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSupplierById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getSupplierById', 'id', id)
            const localVarPath = `/api/suppliers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSupplier: async (id: number, supplierDTO: SupplierDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateSupplier', 'id', id)
            // verify required parameter 'supplierDTO' is not null or undefined
            assertParamExists('updateSupplier', 'supplierDTO', supplierDTO)
            const localVarPath = `/api/suppliers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(supplierDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SupplierControllerApi - functional programming interface
 * @export
 */
export const SupplierControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SupplierControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async approveSupplier(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SupplierDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.approveSupplier(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.approveSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSupplier(supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SupplierDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSupplier(supplierDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.createSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSupplier(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSupplier(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.deleteSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllSuppliers(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<SupplierDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllSuppliers(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.getAllSuppliers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} email 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSupplierByEmail(email: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SupplierDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSupplierByEmail(email, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.getSupplierByEmail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSupplierById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SupplierDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSupplierById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.getSupplierById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSupplier(id: number, supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SupplierDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSupplier(id, supplierDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SupplierControllerApi.updateSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SupplierControllerApi - factory interface
 * @export
 */
export const SupplierControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SupplierControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        approveSupplier(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO> {
            return localVarFp.approveSupplier(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSupplier(supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO> {
            return localVarFp.createSupplier(supplierDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSupplier(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteSupplier(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSuppliers(options?: RawAxiosRequestConfig): AxiosPromise<Array<SupplierDTO>> {
            return localVarFp.getAllSuppliers(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} email 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSupplierByEmail(email: string, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO> {
            return localVarFp.getSupplierByEmail(email, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSupplierById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO> {
            return localVarFp.getSupplierById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {SupplierDTO} supplierDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSupplier(id: number, supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO> {
            return localVarFp.updateSupplier(id, supplierDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SupplierControllerApi - interface
 * @export
 * @interface SupplierControllerApi
 */
export interface SupplierControllerApiInterface {
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    approveSupplier(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO>;

    /**
     * 
     * @param {SupplierDTO} supplierDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    createSupplier(supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    deleteSupplier(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    getAllSuppliers(options?: RawAxiosRequestConfig): AxiosPromise<Array<SupplierDTO>>;

    /**
     * 
     * @param {string} email 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    getSupplierByEmail(email: string, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    getSupplierById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO>;

    /**
     * 
     * @param {number} id 
     * @param {SupplierDTO} supplierDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApiInterface
     */
    updateSupplier(id: number, supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig): AxiosPromise<SupplierDTO>;

}

/**
 * SupplierControllerApi - object-oriented interface
 * @export
 * @class SupplierControllerApi
 * @extends {BaseAPI}
 */
export class SupplierControllerApi extends BaseAPI implements SupplierControllerApiInterface {
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public approveSupplier(id: number, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).approveSupplier(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SupplierDTO} supplierDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public createSupplier(supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).createSupplier(supplierDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public deleteSupplier(id: number, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).deleteSupplier(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public getAllSuppliers(options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).getAllSuppliers(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} email 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public getSupplierByEmail(email: string, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).getSupplierByEmail(email, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public getSupplierById(id: number, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).getSupplierById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {SupplierDTO} supplierDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SupplierControllerApi
     */
    public updateSupplier(id: number, supplierDTO: SupplierDTO, options?: RawAxiosRequestConfig) {
        return SupplierControllerApiFp(this.configuration).updateSupplier(id, supplierDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

