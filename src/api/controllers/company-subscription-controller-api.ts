/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CompanySubscriptionDTO } from '../models';
// @ts-ignore
import type { PageCompanySubscriptionDTO } from '../models';
/**
 * CompanySubscriptionControllerApi - axios parameter creator
 * @export
 */
export const CompanySubscriptionControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCompanySubscription: async (companySubscriptionDTO: CompanySubscriptionDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'companySubscriptionDTO' is not null or undefined
            assertParamExists('createCompanySubscription', 'companySubscriptionDTO', companySubscriptionDTO)
            const localVarPath = `/api/companysubscriptions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(companySubscriptionDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCompanySubscription: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteCompanySubscription', 'id', id)
            const localVarPath = `/api/companysubscriptions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {GetAllCompanySubscriptionsStatusEnum} [status] 
         * @param {string} [companyName] 
         * @param {string} [startAfter] 
         * @param {string} [endBefore] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllCompanySubscriptions: async (status?: GetAllCompanySubscriptionsStatusEnum, companyName?: string, startAfter?: string, endBefore?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/companysubscriptions/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }

            if (companyName !== undefined) {
                localVarQueryParameter['companyName'] = companyName;
            }

            if (startAfter !== undefined) {
                localVarQueryParameter['startAfter'] = (startAfter as any instanceof Date) ?
                    (startAfter as any).toISOString() :
                    startAfter;
            }

            if (endBefore !== undefined) {
                localVarQueryParameter['endBefore'] = (endBefore as any instanceof Date) ?
                    (endBefore as any).toISOString() :
                    endBefore;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanySubscriptionById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getCompanySubscriptionById', 'id', id)
            const localVarPath = `/api/companysubscriptions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCompanySubscription: async (id: number, companySubscriptionDTO: CompanySubscriptionDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateCompanySubscription', 'id', id)
            // verify required parameter 'companySubscriptionDTO' is not null or undefined
            assertParamExists('updateCompanySubscription', 'companySubscriptionDTO', companySubscriptionDTO)
            const localVarPath = `/api/companysubscriptions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(companySubscriptionDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CompanySubscriptionControllerApi - functional programming interface
 * @export
 */
export const CompanySubscriptionControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CompanySubscriptionControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCompanySubscription(companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanySubscriptionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCompanySubscription(companySubscriptionDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanySubscriptionControllerApi.createCompanySubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCompanySubscription(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCompanySubscription(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanySubscriptionControllerApi.deleteCompanySubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {GetAllCompanySubscriptionsStatusEnum} [status] 
         * @param {string} [companyName] 
         * @param {string} [startAfter] 
         * @param {string} [endBefore] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllCompanySubscriptions(status?: GetAllCompanySubscriptionsStatusEnum, companyName?: string, startAfter?: string, endBefore?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageCompanySubscriptionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllCompanySubscriptions(status, companyName, startAfter, endBefore, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanySubscriptionControllerApi.getAllCompanySubscriptions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCompanySubscriptionById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanySubscriptionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCompanySubscriptionById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanySubscriptionControllerApi.getCompanySubscriptionById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCompanySubscription(id: number, companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CompanySubscriptionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCompanySubscription(id, companySubscriptionDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CompanySubscriptionControllerApi.updateCompanySubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CompanySubscriptionControllerApi - factory interface
 * @export
 */
export const CompanySubscriptionControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CompanySubscriptionControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCompanySubscription(companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO> {
            return localVarFp.createCompanySubscription(companySubscriptionDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCompanySubscription(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteCompanySubscription(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {GetAllCompanySubscriptionsStatusEnum} [status] 
         * @param {string} [companyName] 
         * @param {string} [startAfter] 
         * @param {string} [endBefore] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllCompanySubscriptions(status?: GetAllCompanySubscriptionsStatusEnum, companyName?: string, startAfter?: string, endBefore?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageCompanySubscriptionDTO> {
            return localVarFp.getAllCompanySubscriptions(status, companyName, startAfter, endBefore, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCompanySubscriptionById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO> {
            return localVarFp.getCompanySubscriptionById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {CompanySubscriptionDTO} companySubscriptionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCompanySubscription(id: number, companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO> {
            return localVarFp.updateCompanySubscription(id, companySubscriptionDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CompanySubscriptionControllerApi - interface
 * @export
 * @interface CompanySubscriptionControllerApi
 */
export interface CompanySubscriptionControllerApiInterface {
    /**
     * 
     * @param {CompanySubscriptionDTO} companySubscriptionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApiInterface
     */
    createCompanySubscription(companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApiInterface
     */
    deleteCompanySubscription(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {GetAllCompanySubscriptionsStatusEnum} [status] 
     * @param {string} [companyName] 
     * @param {string} [startAfter] 
     * @param {string} [endBefore] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApiInterface
     */
    getAllCompanySubscriptions(status?: GetAllCompanySubscriptionsStatusEnum, companyName?: string, startAfter?: string, endBefore?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageCompanySubscriptionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApiInterface
     */
    getCompanySubscriptionById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {CompanySubscriptionDTO} companySubscriptionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApiInterface
     */
    updateCompanySubscription(id: number, companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig): AxiosPromise<CompanySubscriptionDTO>;

}

/**
 * CompanySubscriptionControllerApi - object-oriented interface
 * @export
 * @class CompanySubscriptionControllerApi
 * @extends {BaseAPI}
 */
export class CompanySubscriptionControllerApi extends BaseAPI implements CompanySubscriptionControllerApiInterface {
    /**
     * 
     * @param {CompanySubscriptionDTO} companySubscriptionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApi
     */
    public createCompanySubscription(companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig) {
        return CompanySubscriptionControllerApiFp(this.configuration).createCompanySubscription(companySubscriptionDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApi
     */
    public deleteCompanySubscription(id: number, options?: RawAxiosRequestConfig) {
        return CompanySubscriptionControllerApiFp(this.configuration).deleteCompanySubscription(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {GetAllCompanySubscriptionsStatusEnum} [status] 
     * @param {string} [companyName] 
     * @param {string} [startAfter] 
     * @param {string} [endBefore] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApi
     */
    public getAllCompanySubscriptions(status?: GetAllCompanySubscriptionsStatusEnum, companyName?: string, startAfter?: string, endBefore?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return CompanySubscriptionControllerApiFp(this.configuration).getAllCompanySubscriptions(status, companyName, startAfter, endBefore, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApi
     */
    public getCompanySubscriptionById(id: number, options?: RawAxiosRequestConfig) {
        return CompanySubscriptionControllerApiFp(this.configuration).getCompanySubscriptionById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {CompanySubscriptionDTO} companySubscriptionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CompanySubscriptionControllerApi
     */
    public updateCompanySubscription(id: number, companySubscriptionDTO: CompanySubscriptionDTO, options?: RawAxiosRequestConfig) {
        return CompanySubscriptionControllerApiFp(this.configuration).updateCompanySubscription(id, companySubscriptionDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAllCompanySubscriptionsStatusEnum = {
    Active: 'ACTIVE',
    Expired: 'EXPIRED',
    Cancelled: 'CANCELLED'
} as const;
export type GetAllCompanySubscriptionsStatusEnum = typeof GetAllCompanySubscriptionsStatusEnum[keyof typeof GetAllCompanySubscriptionsStatusEnum];
