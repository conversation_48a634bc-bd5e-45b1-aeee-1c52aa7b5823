/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PagePermissionDTO } from '../models';
// @ts-ignore
import type { PermissionDTO } from '../models';
/**
 * PermissionControllerApi - axios parameter creator
 * @export
 */
export const PermissionControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPermission: async (permissionDTO: PermissionDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'permissionDTO' is not null or undefined
            assertParamExists('createPermission', 'permissionDTO', permissionDTO)
            const localVarPath = `/api/permissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(permissionDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePermission: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deletePermission', 'id', id)
            const localVarPath = `/api/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllPermissions: async (page?: number, size?: number, sort?: Array<string>, name?: string, description?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/permissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (description !== undefined) {
                localVarQueryParameter['description'] = description;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPermissionById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getPermissionById', 'id', id)
            const localVarPath = `/api/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePermission: async (id: number, permissionDTO: PermissionDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updatePermission', 'id', id)
            // verify required parameter 'permissionDTO' is not null or undefined
            assertParamExists('updatePermission', 'permissionDTO', permissionDTO)
            const localVarPath = `/api/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(permissionDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PermissionControllerApi - functional programming interface
 * @export
 */
export const PermissionControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PermissionControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createPermission(permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PermissionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createPermission(permissionDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionControllerApi.createPermission']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deletePermission(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deletePermission(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionControllerApi.deletePermission']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllPermissions(page?: number, size?: number, sort?: Array<string>, name?: string, description?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagePermissionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllPermissions(page, size, sort, name, description, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionControllerApi.getAllPermissions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPermissionById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PermissionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPermissionById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionControllerApi.getPermissionById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updatePermission(id: number, permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PermissionDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updatePermission(id, permissionDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionControllerApi.updatePermission']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PermissionControllerApi - factory interface
 * @export
 */
export const PermissionControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PermissionControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPermission(permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO> {
            return localVarFp.createPermission(permissionDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePermission(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deletePermission(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllPermissions(page?: number, size?: number, sort?: Array<string>, name?: string, description?: string, options?: RawAxiosRequestConfig): AxiosPromise<PagePermissionDTO> {
            return localVarFp.getAllPermissions(page, size, sort, name, description, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPermissionById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO> {
            return localVarFp.getPermissionById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {PermissionDTO} permissionDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePermission(id: number, permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO> {
            return localVarFp.updatePermission(id, permissionDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PermissionControllerApi - interface
 * @export
 * @interface PermissionControllerApi
 */
export interface PermissionControllerApiInterface {
    /**
     * 
     * @param {PermissionDTO} permissionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApiInterface
     */
    createPermission(permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApiInterface
     */
    deletePermission(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [description] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApiInterface
     */
    getAllPermissions(page?: number, size?: number, sort?: Array<string>, name?: string, description?: string, options?: RawAxiosRequestConfig): AxiosPromise<PagePermissionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApiInterface
     */
    getPermissionById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO>;

    /**
     * 
     * @param {number} id 
     * @param {PermissionDTO} permissionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApiInterface
     */
    updatePermission(id: number, permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig): AxiosPromise<PermissionDTO>;

}

/**
 * PermissionControllerApi - object-oriented interface
 * @export
 * @class PermissionControllerApi
 * @extends {BaseAPI}
 */
export class PermissionControllerApi extends BaseAPI implements PermissionControllerApiInterface {
    /**
     * 
     * @param {PermissionDTO} permissionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApi
     */
    public createPermission(permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig) {
        return PermissionControllerApiFp(this.configuration).createPermission(permissionDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApi
     */
    public deletePermission(id: number, options?: RawAxiosRequestConfig) {
        return PermissionControllerApiFp(this.configuration).deletePermission(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {string} [description] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApi
     */
    public getAllPermissions(page?: number, size?: number, sort?: Array<string>, name?: string, description?: string, options?: RawAxiosRequestConfig) {
        return PermissionControllerApiFp(this.configuration).getAllPermissions(page, size, sort, name, description, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApi
     */
    public getPermissionById(id: number, options?: RawAxiosRequestConfig) {
        return PermissionControllerApiFp(this.configuration).getPermissionById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {PermissionDTO} permissionDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionControllerApi
     */
    public updatePermission(id: number, permissionDTO: PermissionDTO, options?: RawAxiosRequestConfig) {
        return PermissionControllerApiFp(this.configuration).updatePermission(id, permissionDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

