/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageSheetScopeDTO } from '../models';
// @ts-ignore
import type { SheetScopeDTO } from '../models';
/**
 * SheetScopeControllerApi - axios parameter creator
 * @export
 */
export const SheetScopeControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SheetScopeDTO} sheetScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSheetScope: async (sheetScopeDTO: SheetScopeDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sheetScopeDTO' is not null or undefined
            assertParamExists('createSheetScope', 'sheetScopeDTO', sheetScopeDTO)
            const localVarPath = `/api/sheet-scopes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(sheetScopeDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSheetScope: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteSheetScope', 'id', id)
            const localVarPath = `/api/sheet-scopes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByScope: async (scopeId: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('getByScope', 'scopeId', scopeId)
            const localVarPath = `/api/sheet-scopes/scope/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} sheetId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBySheet: async (sheetId: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sheetId' is not null or undefined
            assertParamExists('getBySheet', 'sheetId', sheetId)
            const localVarPath = `/api/sheet-scopes/sheet/{sheetId}`
                .replace(`{${"sheetId"}}`, encodeURIComponent(String(sheetId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SheetScopeControllerApi - functional programming interface
 * @export
 */
export const SheetScopeControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SheetScopeControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SheetScopeDTO} sheetScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSheetScope(sheetScopeDTO: SheetScopeDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SheetScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSheetScope(sheetScopeDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetScopeControllerApi.createSheetScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSheetScope(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSheetScope(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetScopeControllerApi.deleteSheetScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getByScope(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageSheetScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getByScope(scopeId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetScopeControllerApi.getByScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} sheetId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBySheet(sheetId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageSheetScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBySheet(sheetId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetScopeControllerApi.getBySheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SheetScopeControllerApi - factory interface
 * @export
 */
export const SheetScopeControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SheetScopeControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {SheetScopeDTO} sheetScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSheetScope(sheetScopeDTO: SheetScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetScopeDTO> {
            return localVarFp.createSheetScope(sheetScopeDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSheetScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteSheetScope(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByScope(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSheetScopeDTO> {
            return localVarFp.getByScope(scopeId, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} sheetId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBySheet(sheetId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSheetScopeDTO> {
            return localVarFp.getBySheet(sheetId, page, size, sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SheetScopeControllerApi - interface
 * @export
 * @interface SheetScopeControllerApi
 */
export interface SheetScopeControllerApiInterface {
    /**
     * 
     * @param {SheetScopeDTO} sheetScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApiInterface
     */
    createSheetScope(sheetScopeDTO: SheetScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetScopeDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApiInterface
     */
    deleteSheetScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} scopeId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApiInterface
     */
    getByScope(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSheetScopeDTO>;

    /**
     * 
     * @param {number} sheetId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApiInterface
     */
    getBySheet(sheetId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageSheetScopeDTO>;

}

/**
 * SheetScopeControllerApi - object-oriented interface
 * @export
 * @class SheetScopeControllerApi
 * @extends {BaseAPI}
 */
export class SheetScopeControllerApi extends BaseAPI implements SheetScopeControllerApiInterface {
    /**
     * 
     * @param {SheetScopeDTO} sheetScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApi
     */
    public createSheetScope(sheetScopeDTO: SheetScopeDTO, options?: RawAxiosRequestConfig) {
        return SheetScopeControllerApiFp(this.configuration).createSheetScope(sheetScopeDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApi
     */
    public deleteSheetScope(id: number, options?: RawAxiosRequestConfig) {
        return SheetScopeControllerApiFp(this.configuration).deleteSheetScope(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApi
     */
    public getByScope(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return SheetScopeControllerApiFp(this.configuration).getByScope(scopeId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} sheetId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetScopeControllerApi
     */
    public getBySheet(sheetId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return SheetScopeControllerApiFp(this.configuration).getBySheet(sheetId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }
}

