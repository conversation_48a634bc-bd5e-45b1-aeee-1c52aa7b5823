/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageProjectSpecificationDTO } from '../models';
// @ts-ignore
import type { ProjectSpecificationDTO } from '../models';
/**
 * ProjectSpecificationControllerApi - axios parameter creator
 * @export
 */
export const ProjectSpecificationControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ProjectSpecificationDTO} projectSpecificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSpecification: async (projectSpecificationDTO: ProjectSpecificationDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectSpecificationDTO' is not null or undefined
            assertParamExists('createSpecification', 'projectSpecificationDTO', projectSpecificationDTO)
            const localVarPath = `/api/specifications`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectSpecificationDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteScope: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteScope', 'id', id)
            const localVarPath = `/api/specifications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [title] 
         * @param {number} [projectId] 
         * @param {number} [scopeId] 
         * @param {number} [pageNumber] 
         * @param {string} [code] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectSpecifications: async (title?: string, projectId?: number, scopeId?: number, pageNumber?: number, code?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/specifications`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (title !== undefined) {
                localVarQueryParameter['title'] = title;
            }

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (scopeId !== undefined) {
                localVarQueryParameter['scopeId'] = scopeId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (code !== undefined) {
                localVarQueryParameter['code'] = code;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getSpecificationById', 'id', id)
            const localVarPath = `/api/specifications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectSpecificationControllerApi - functional programming interface
 * @export
 */
export const ProjectSpecificationControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectSpecificationControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ProjectSpecificationDTO} projectSpecificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSpecification(projectSpecificationDTO: ProjectSpecificationDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectSpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSpecification(projectSpecificationDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectSpecificationControllerApi.createSpecification']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteScope(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteScope(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectSpecificationControllerApi.deleteScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [title] 
         * @param {number} [projectId] 
         * @param {number} [scopeId] 
         * @param {number} [pageNumber] 
         * @param {string} [code] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectSpecifications(title?: string, projectId?: number, scopeId?: number, pageNumber?: number, code?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageProjectSpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectSpecifications(title, projectId, scopeId, pageNumber, code, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectSpecificationControllerApi.getProjectSpecifications']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSpecificationById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectSpecificationDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSpecificationById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectSpecificationControllerApi.getSpecificationById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectSpecificationControllerApi - factory interface
 * @export
 */
export const ProjectSpecificationControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectSpecificationControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ProjectSpecificationDTO} projectSpecificationDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSpecification(projectSpecificationDTO: ProjectSpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectSpecificationDTO> {
            return localVarFp.createSpecification(projectSpecificationDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteScope(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [title] 
         * @param {number} [projectId] 
         * @param {number} [scopeId] 
         * @param {number} [pageNumber] 
         * @param {string} [code] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectSpecifications(title?: string, projectId?: number, scopeId?: number, pageNumber?: number, code?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectSpecificationDTO> {
            return localVarFp.getProjectSpecifications(title, projectId, scopeId, pageNumber, code, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSpecificationById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectSpecificationDTO> {
            return localVarFp.getSpecificationById(id, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectSpecificationControllerApi - interface
 * @export
 * @interface ProjectSpecificationControllerApi
 */
export interface ProjectSpecificationControllerApiInterface {
    /**
     * 
     * @param {ProjectSpecificationDTO} projectSpecificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApiInterface
     */
    createSpecification(projectSpecificationDTO: ProjectSpecificationDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectSpecificationDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApiInterface
     */
    deleteScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {string} [title] 
     * @param {number} [projectId] 
     * @param {number} [scopeId] 
     * @param {number} [pageNumber] 
     * @param {string} [code] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApiInterface
     */
    getProjectSpecifications(title?: string, projectId?: number, scopeId?: number, pageNumber?: number, code?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageProjectSpecificationDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApiInterface
     */
    getSpecificationById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectSpecificationDTO>;

}

/**
 * ProjectSpecificationControllerApi - object-oriented interface
 * @export
 * @class ProjectSpecificationControllerApi
 * @extends {BaseAPI}
 */
export class ProjectSpecificationControllerApi extends BaseAPI implements ProjectSpecificationControllerApiInterface {
    /**
     * 
     * @param {ProjectSpecificationDTO} projectSpecificationDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApi
     */
    public createSpecification(projectSpecificationDTO: ProjectSpecificationDTO, options?: RawAxiosRequestConfig) {
        return ProjectSpecificationControllerApiFp(this.configuration).createSpecification(projectSpecificationDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApi
     */
    public deleteScope(id: number, options?: RawAxiosRequestConfig) {
        return ProjectSpecificationControllerApiFp(this.configuration).deleteScope(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [title] 
     * @param {number} [projectId] 
     * @param {number} [scopeId] 
     * @param {number} [pageNumber] 
     * @param {string} [code] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApi
     */
    public getProjectSpecifications(title?: string, projectId?: number, scopeId?: number, pageNumber?: number, code?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return ProjectSpecificationControllerApiFp(this.configuration).getProjectSpecifications(title, projectId, scopeId, pageNumber, code, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectSpecificationControllerApi
     */
    public getSpecificationById(id: number, options?: RawAxiosRequestConfig) {
        return ProjectSpecificationControllerApiFp(this.configuration).getSpecificationById(id, options).then((request) => request(this.axios, this.basePath));
    }
}

