/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageRoleDTO } from '../models';
// @ts-ignore
import type { RoleDTO } from '../models';
/**
 * RoleControllerApi - axios parameter creator
 * @export
 */
export const RoleControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRole: async (roleDTO: RoleDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleDTO' is not null or undefined
            assertParamExists('createRole', 'roleDTO', roleDTO)
            const localVarPath = `/api/roles`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRole: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteRole', 'id', id)
            const localVarPath = `/api/roles/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [search] 
         * @param {number} [companyId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllRoles: async (search?: string, companyId?: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/roles/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (companyId !== undefined) {
                localVarQueryParameter['companyId'] = companyId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getRoleById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getRoleById', 'id', id)
            const localVarPath = `/api/roles/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRole: async (id: number, roleDTO: RoleDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateRole', 'id', id)
            // verify required parameter 'roleDTO' is not null or undefined
            assertParamExists('updateRole', 'roleDTO', roleDTO)
            const localVarPath = `/api/roles/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RoleControllerApi - functional programming interface
 * @export
 */
export const RoleControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = RoleControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createRole(roleDTO: RoleDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createRole(roleDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleControllerApi.createRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteRole(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteRole(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleControllerApi.deleteRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [search] 
         * @param {number} [companyId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllRoles(search?: string, companyId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageRoleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllRoles(search, companyId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleControllerApi.getAllRoles']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getRoleById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getRoleById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleControllerApi.getRoleById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateRole(id: number, roleDTO: RoleDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateRole(id, roleDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleControllerApi.updateRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * RoleControllerApi - factory interface
 * @export
 */
export const RoleControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = RoleControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRole(roleDTO: RoleDTO, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO> {
            return localVarFp.createRole(roleDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRole(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteRole(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [search] 
         * @param {number} [companyId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllRoles(search?: string, companyId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageRoleDTO> {
            return localVarFp.getAllRoles(search, companyId, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getRoleById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO> {
            return localVarFp.getRoleById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {RoleDTO} roleDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRole(id: number, roleDTO: RoleDTO, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO> {
            return localVarFp.updateRole(id, roleDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RoleControllerApi - interface
 * @export
 * @interface RoleControllerApi
 */
export interface RoleControllerApiInterface {
    /**
     * 
     * @param {RoleDTO} roleDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApiInterface
     */
    createRole(roleDTO: RoleDTO, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApiInterface
     */
    deleteRole(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {string} [search] 
     * @param {number} [companyId] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApiInterface
     */
    getAllRoles(search?: string, companyId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageRoleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApiInterface
     */
    getRoleById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {RoleDTO} roleDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApiInterface
     */
    updateRole(id: number, roleDTO: RoleDTO, options?: RawAxiosRequestConfig): AxiosPromise<RoleDTO>;

}

/**
 * RoleControllerApi - object-oriented interface
 * @export
 * @class RoleControllerApi
 * @extends {BaseAPI}
 */
export class RoleControllerApi extends BaseAPI implements RoleControllerApiInterface {
    /**
     * 
     * @param {RoleDTO} roleDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApi
     */
    public createRole(roleDTO: RoleDTO, options?: RawAxiosRequestConfig) {
        return RoleControllerApiFp(this.configuration).createRole(roleDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApi
     */
    public deleteRole(id: number, options?: RawAxiosRequestConfig) {
        return RoleControllerApiFp(this.configuration).deleteRole(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [search] 
     * @param {number} [companyId] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApi
     */
    public getAllRoles(search?: string, companyId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return RoleControllerApiFp(this.configuration).getAllRoles(search, companyId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApi
     */
    public getRoleById(id: number, options?: RawAxiosRequestConfig) {
        return RoleControllerApiFp(this.configuration).getRoleById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {RoleDTO} roleDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleControllerApi
     */
    public updateRole(id: number, roleDTO: RoleDTO, options?: RawAxiosRequestConfig) {
        return RoleControllerApiFp(this.configuration).updateRole(id, roleDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

