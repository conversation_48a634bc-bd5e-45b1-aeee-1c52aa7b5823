/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PageScheduleDTO } from '../models';
// @ts-ignore
import type { ScheduleCreateDTO } from '../models';
// @ts-ignore
import type { ScheduleDTO } from '../models';
// @ts-ignore
import type { ScheduleUpdateDTO } from '../models';
/**
 * ScheduleControllerApi - axios parameter creator
 * @export
 */
export const ScheduleControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ScheduleCreateDTO} scheduleCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSchedule: async (scheduleCreateDTO: ScheduleCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scheduleCreateDTO' is not null or undefined
            assertParamExists('createSchedule', 'scheduleCreateDTO', scheduleCreateDTO)
            const localVarPath = `/api/schedules`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(scheduleCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSchedule: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteSchedule', 'id', id)
            const localVarPath = `/api/schedules/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSchedules: async (page?: number, size?: number, sort?: Array<string>, search?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedules`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScheduleById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getScheduleById', 'id', id)
            const localVarPath = `/api/schedules/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} documentId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSchedulesByDocumentId: async (documentId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'documentId' is not null or undefined
            assertParamExists('getSchedulesByDocumentId', 'documentId', documentId)
            const localVarPath = `/api/schedules/document/{documentId}`
                .replace(`{${"documentId"}}`, encodeURIComponent(String(documentId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSchedulesByProjectId: async (projectId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getSchedulesByProjectId', 'projectId', projectId)
            const localVarPath = `/api/schedules/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ScheduleUpdateDTO} scheduleUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSchedule: async (id: number, scheduleUpdateDTO: ScheduleUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateSchedule', 'id', id)
            // verify required parameter 'scheduleUpdateDTO' is not null or undefined
            assertParamExists('updateSchedule', 'scheduleUpdateDTO', scheduleUpdateDTO)
            const localVarPath = `/api/schedules/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(scheduleUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ScheduleControllerApi - functional programming interface
 * @export
 */
export const ScheduleControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ScheduleControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ScheduleCreateDTO} scheduleCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSchedule(scheduleCreateDTO: ScheduleCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSchedule(scheduleCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.createSchedule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSchedule(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSchedule(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.deleteSchedule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllSchedules(page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllSchedules(page, size, sort, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.getAllSchedules']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScheduleById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScheduleById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.getScheduleById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} documentId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSchedulesByDocumentId(documentId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSchedulesByDocumentId(documentId, page, size, sort, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.getSchedulesByDocumentId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSchedulesByProjectId(projectId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSchedulesByProjectId(projectId, page, size, sort, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.getSchedulesByProjectId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ScheduleUpdateDTO} scheduleUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSchedule(id: number, scheduleUpdateDTO: ScheduleUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScheduleDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSchedule(id, scheduleUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScheduleControllerApi.updateSchedule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ScheduleControllerApi - factory interface
 * @export
 */
export const ScheduleControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ScheduleControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ScheduleCreateDTO} scheduleCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSchedule(scheduleCreateDTO: ScheduleCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO> {
            return localVarFp.createSchedule(scheduleCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSchedule(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteSchedule(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSchedules(page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO> {
            return localVarFp.getAllSchedules(page, size, sort, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScheduleById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO> {
            return localVarFp.getScheduleById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} documentId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSchedulesByDocumentId(documentId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO> {
            return localVarFp.getSchedulesByDocumentId(documentId, page, size, sort, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [search] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSchedulesByProjectId(projectId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO> {
            return localVarFp.getSchedulesByProjectId(projectId, page, size, sort, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ScheduleUpdateDTO} scheduleUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSchedule(id: number, scheduleUpdateDTO: ScheduleUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO> {
            return localVarFp.updateSchedule(id, scheduleUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ScheduleControllerApi - interface
 * @export
 * @interface ScheduleControllerApi
 */
export interface ScheduleControllerApiInterface {
    /**
     * 
     * @param {ScheduleCreateDTO} scheduleCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    createSchedule(scheduleCreateDTO: ScheduleCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    deleteSchedule(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void>;

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    getAllSchedules(page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    getScheduleById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO>;

    /**
     * 
     * @param {number} documentId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    getSchedulesByDocumentId(documentId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO>;

    /**
     * 
     * @param {number} projectId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    getSchedulesByProjectId(projectId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageScheduleDTO>;

    /**
     * 
     * @param {number} id 
     * @param {ScheduleUpdateDTO} scheduleUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApiInterface
     */
    updateSchedule(id: number, scheduleUpdateDTO: ScheduleUpdateDTO, options?: RawAxiosRequestConfig): AxiosPromise<ScheduleDTO>;

}

/**
 * ScheduleControllerApi - object-oriented interface
 * @export
 * @class ScheduleControllerApi
 * @extends {BaseAPI}
 */
export class ScheduleControllerApi extends BaseAPI implements ScheduleControllerApiInterface {
    /**
     * 
     * @param {ScheduleCreateDTO} scheduleCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public createSchedule(scheduleCreateDTO: ScheduleCreateDTO, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).createSchedule(scheduleCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public deleteSchedule(id: number, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).deleteSchedule(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public getAllSchedules(page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).getAllSchedules(page, size, sort, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public getScheduleById(id: number, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).getScheduleById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} documentId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public getSchedulesByDocumentId(documentId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).getSchedulesByDocumentId(documentId, page, size, sort, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [search] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public getSchedulesByProjectId(projectId: number, page?: number, size?: number, sort?: Array<string>, search?: string, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).getSchedulesByProjectId(projectId, page, size, sort, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ScheduleUpdateDTO} scheduleUpdateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScheduleControllerApi
     */
    public updateSchedule(id: number, scheduleUpdateDTO: ScheduleUpdateDTO, options?: RawAxiosRequestConfig) {
        return ScheduleControllerApiFp(this.configuration).updateSchedule(id, scheduleUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

