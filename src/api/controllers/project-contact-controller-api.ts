/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ProjectContactDTO } from '../models';
/**
 * ProjectContactControllerApi - axios parameter creator
 * @export
 */
export const ProjectContactControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectContact: async (projectContactDTO: ProjectContactDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectContactDTO' is not null or undefined
            assertParamExists('createProjectContact', 'projectContactDTO', projectContactDTO)
            const localVarPath = `/api/project-contacts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectContactDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProjectContact: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteProjectContact', 'id', id)
            const localVarPath = `/api/project-contacts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} companyId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContactsByCompany: async (companyId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'companyId' is not null or undefined
            assertParamExists('getContactsByCompany', 'companyId', companyId)
            const localVarPath = `/api/project-contacts/company/{companyId}`
                .replace(`{${"companyId"}}`, encodeURIComponent(String(companyId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContactsByProject: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getContactsByProject', 'projectId', projectId)
            const localVarPath = `/api/project-contacts/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectContactById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getProjectContactById', 'id', id)
            const localVarPath = `/api/project-contacts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProjectContact: async (id: number, projectContactDTO: ProjectContactDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateProjectContact', 'id', id)
            // verify required parameter 'projectContactDTO' is not null or undefined
            assertParamExists('updateProjectContact', 'projectContactDTO', projectContactDTO)
            const localVarPath = `/api/project-contacts/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(projectContactDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectContactControllerApi - functional programming interface
 * @export
 */
export const ProjectContactControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectContactControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createProjectContact(projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectContactDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createProjectContact(projectContactDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.createProjectContact']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteProjectContact(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteProjectContact(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.deleteProjectContact']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} companyId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContactsByCompany(companyId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ProjectContactDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContactsByCompany(companyId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.getContactsByCompany']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContactsByProject(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ProjectContactDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContactsByProject(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.getContactsByProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProjectContactById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectContactDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProjectContactById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.getProjectContactById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateProjectContact(id: number, projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProjectContactDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateProjectContact(id, projectContactDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectContactControllerApi.updateProjectContact']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectContactControllerApi - factory interface
 * @export
 */
export const ProjectContactControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectContactControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProjectContact(projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO> {
            return localVarFp.createProjectContact(projectContactDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProjectContact(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteProjectContact(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} companyId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContactsByCompany(companyId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<ProjectContactDTO>> {
            return localVarFp.getContactsByCompany(companyId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContactsByProject(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<ProjectContactDTO>> {
            return localVarFp.getContactsByProject(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProjectContactById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO> {
            return localVarFp.getProjectContactById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {ProjectContactDTO} projectContactDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProjectContact(id: number, projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO> {
            return localVarFp.updateProjectContact(id, projectContactDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectContactControllerApi - interface
 * @export
 * @interface ProjectContactControllerApi
 */
export interface ProjectContactControllerApiInterface {
    /**
     * 
     * @param {ProjectContactDTO} projectContactDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    createProjectContact(projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    deleteProjectContact(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} companyId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    getContactsByCompany(companyId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<ProjectContactDTO>>;

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    getContactsByProject(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<ProjectContactDTO>>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    getProjectContactById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO>;

    /**
     * 
     * @param {number} id 
     * @param {ProjectContactDTO} projectContactDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApiInterface
     */
    updateProjectContact(id: number, projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig): AxiosPromise<ProjectContactDTO>;

}

/**
 * ProjectContactControllerApi - object-oriented interface
 * @export
 * @class ProjectContactControllerApi
 * @extends {BaseAPI}
 */
export class ProjectContactControllerApi extends BaseAPI implements ProjectContactControllerApiInterface {
    /**
     * 
     * @param {ProjectContactDTO} projectContactDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public createProjectContact(projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).createProjectContact(projectContactDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public deleteProjectContact(id: number, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).deleteProjectContact(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} companyId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public getContactsByCompany(companyId: number, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).getContactsByCompany(companyId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public getContactsByProject(projectId: number, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).getContactsByProject(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public getProjectContactById(id: number, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).getProjectContactById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {ProjectContactDTO} projectContactDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectContactControllerApi
     */
    public updateProjectContact(id: number, projectContactDTO: ProjectContactDTO, options?: RawAxiosRequestConfig) {
        return ProjectContactControllerApiFp(this.configuration).updateProjectContact(id, projectContactDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

