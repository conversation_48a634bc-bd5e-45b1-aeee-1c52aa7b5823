/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface DocumentUploadConfirmDTO
 */
export interface DocumentUploadConfirmDTO {
    /**
     * 
     * @type {string}
     * @memberof DocumentUploadConfirmDTO
     */
    'fileKey'?: string;
    /**
     * 
     * @type {number}
     * @memberof DocumentUploadConfirmDTO
     */
    'size'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentUploadConfirmDTO
     */
    'mimeType'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentUploadConfirmDTO
     */
    'filePath'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentUploadConfirmDTO
     */
    'inputDocumentType'?: DocumentUploadConfirmDTOInputDocumentTypeEnum;
    /**
     * 
     * @type {number}
     * @memberof DocumentUploadConfirmDTO
     */
    'documentId'?: number;
}

export const DocumentUploadConfirmDTOInputDocumentTypeEnum = {
    Drawing: 'DRAWING',
    Specification: 'SPECIFICATION'
} as const;

export type DocumentUploadConfirmDTOInputDocumentTypeEnum = typeof DocumentUploadConfirmDTOInputDocumentTypeEnum[keyof typeof DocumentUploadConfirmDTOInputDocumentTypeEnum];


