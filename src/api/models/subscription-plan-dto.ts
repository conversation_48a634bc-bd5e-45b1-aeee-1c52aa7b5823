/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PlanProductDTO } from './plan-product-dto';

/**
 * 
 * @export
 * @interface SubscriptionPlanDTO
 */
export interface SubscriptionPlanDTO {
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlanDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlanDTO
     */
    'name'?: string;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlanDTO
     */
    'price'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlanDTO
     */
    'validityPeriod'?: SubscriptionPlanDTOValidityPeriodEnum;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlanDTO
     */
    'maxUsers'?: number;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlanDTO
     */
    'maxProjects'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlanDTO
     */
    'description'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SubscriptionPlanDTO
     */
    'isBundle'?: boolean;
    /**
     * 
     * @type {Array<PlanProductDTO>}
     * @memberof SubscriptionPlanDTO
     */
    'planProducts'?: Array<PlanProductDTO>;
}

export const SubscriptionPlanDTOValidityPeriodEnum = {
    Monthly: 'Monthly',
    Yearly: 'Yearly'
} as const;

export type SubscriptionPlanDTOValidityPeriodEnum = typeof SubscriptionPlanDTOValidityPeriodEnum[keyof typeof SubscriptionPlanDTOValidityPeriodEnum];


