/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface ProductCreateDTO
 */
export interface ProductCreateDTO {
    /**
     * 
     * @type {string}
     * @memberof ProductCreateDTO
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof ProductCreateDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductCreateDTO
     */
    'pricingModel': ProductCreateDTOPricingModelEnum;
    /**
     * 
     * @type {string}
     * @memberof ProductCreateDTO
     */
    'featuresIncluded'?: string;
    /**
     * 
     * @type {User}
     * @memberof ProductCreateDTO
     */
    'createdBy': User;
}

export const ProductCreateDTOPricingModelEnum = {
    MonthlySubscription: 'Monthly Subscription',
    YearlySubscription: 'Yearly Subscription',
    OneTimePurchase: 'One-Time Purchase'
} as const;

export type ProductCreateDTOPricingModelEnum = typeof ProductCreateDTOPricingModelEnum[keyof typeof ProductCreateDTOPricingModelEnum];


