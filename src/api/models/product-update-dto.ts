/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProductUpdateDTO
 */
export interface ProductUpdateDTO {
    /**
     * 
     * @type {string}
     * @memberof ProductUpdateDTO
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof ProductUpdateDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductUpdateDTO
     */
    'pricingModel': ProductUpdateDTOPricingModelEnum;
    /**
     * 
     * @type {string}
     * @memberof ProductUpdateDTO
     */
    'featuresIncluded'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProductUpdateDTO
     */
    'updatedBy': number;
}

export const ProductUpdateDTOPricingModelEnum = {
    MonthlySubscription: 'Monthly Subscription',
    YearlySubscription: 'Yearly Subscription',
    OneTimePurchase: 'One-Time Purchase'
} as const;

export type ProductUpdateDTOPricingModelEnum = typeof ProductUpdateDTOPricingModelEnum[keyof typeof ProductUpdateDTOPricingModelEnum];


