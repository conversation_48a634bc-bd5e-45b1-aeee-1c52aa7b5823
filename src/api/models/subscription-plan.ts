/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface SubscriptionPlan
 */
export interface SubscriptionPlan {
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlan
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof SubscriptionPlan
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof SubscriptionPlan
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SubscriptionPlan
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlan
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'name': string;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlan
     */
    'price': number;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'description'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SubscriptionPlan
     */
    'isBundle'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof SubscriptionPlan
     */
    'validityPeriod': SubscriptionPlanValidityPeriodEnum;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlan
     */
    'maxUsers': number;
    /**
     * 
     * @type {number}
     * @memberof SubscriptionPlan
     */
    'maxProjects': number;
}

export const SubscriptionPlanValidityPeriodEnum = {
    Monthly: 'Monthly',
    Yearly: 'Yearly'
} as const;

export type SubscriptionPlanValidityPeriodEnum = typeof SubscriptionPlanValidityPeriodEnum[keyof typeof SubscriptionPlanValidityPeriodEnum];


