/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface ProductDTO
 */
export interface ProductDTO {
    /**
     * 
     * @type {number}
     * @memberof ProductDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'pricingModel': ProductDTOPricingModelEnum;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'featuresIncluded'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductDTO
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof ProductDTO
     */
    'createdBy': User;
    /**
     * 
     * @type {number}
     * @memberof ProductDTO
     */
    'updatedBy'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof ProductDTO
     */
    'isActive'?: boolean;
}

export const ProductDTOPricingModelEnum = {
    MonthlySubscription: 'Monthly Subscription',
    YearlySubscription: 'Yearly Subscription',
    OneTimePurchase: 'One-Time Purchase'
} as const;

export type ProductDTOPricingModelEnum = typeof ProductDTOPricingModelEnum[keyof typeof ProductDTOPricingModelEnum];


