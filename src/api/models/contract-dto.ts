/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * List of contracts related to the project
 * @export
 * @interface ContractDTO
 */
export interface ContractDTO {
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'contractId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'type'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'contractNumber'?: string;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'value'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'parties'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'documentPath'?: string;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'riskScore'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'riskIssues'?: string;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'approvalStatus'?: string;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'approvedBy'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'approvedDate'?: string;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'createdBy'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'createdDate'?: string;
    /**
     * 
     * @type {number}
     * @memberof ContractDTO
     */
    'modifiedBy'?: number;
    /**
     * 
     * @type {string}
     * @memberof ContractDTO
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ContractDTO
     */
    'isActive'?: boolean;
}

