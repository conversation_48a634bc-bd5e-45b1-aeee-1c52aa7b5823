/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DocumentDTO } from './document-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PageableObject } from './pageable-object';
// May contain unused imports in some cases
// @ts-ignore
import type { SortObject } from './sort-object';

/**
 * 
 * @export
 * @interface PageDocumentDTO
 */
export interface PageDocumentDTO {
    /**
     * 
     * @type {number}
     * @memberof PageDocumentDTO
     */
    'totalPages'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageDocumentDTO
     */
    'totalElements'?: number;
    /**
     * 
     * @type {PageableObject}
     * @memberof PageDocumentDTO
     */
    'pageable'?: PageableObject;
    /**
     * 
     * @type {boolean}
     * @memberof PageDocumentDTO
     */
    'first'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PageDocumentDTO
     */
    'last'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof PageDocumentDTO
     */
    'size'?: number;
    /**
     * 
     * @type {Array<DocumentDTO>}
     * @memberof PageDocumentDTO
     */
    'content'?: Array<DocumentDTO>;
    /**
     * 
     * @type {number}
     * @memberof PageDocumentDTO
     */
    'number'?: number;
    /**
     * 
     * @type {SortObject}
     * @memberof PageDocumentDTO
     */
    'sort'?: SortObject;
    /**
     * 
     * @type {number}
     * @memberof PageDocumentDTO
     */
    'numberOfElements'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof PageDocumentDTO
     */
    'empty'?: boolean;
}

