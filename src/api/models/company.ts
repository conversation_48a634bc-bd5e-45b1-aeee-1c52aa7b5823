/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CompanySubscription } from './company-subscription';
// May contain unused imports in some cases
// @ts-ignore
import type { SubscriptionPlan } from './subscription-plan';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Company
 */
export interface Company {
    /**
     * 
     * @type {number}
     * @memberof Company
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof Company
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Company
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Company
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Company
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'industry'?: string;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'subscriptionStatus'?: CompanySubscriptionStatusEnum;
    /**
     * 
     * @type {Array<SubscriptionPlan>}
     * @memberof Company
     */
    'subscriptionPlans'?: Array<SubscriptionPlan>;
    /**
     * 
     * @type {Set<CompanySubscription>}
     * @memberof Company
     */
    'companySubscriptions'?: Set<CompanySubscription>;
    /**
     * 
     * @type {string}
     * @memberof Company
     */
    'primaryDomain'?: string;
    /**
     * 
     * @type {Set<string>}
     * @memberof Company
     */
    'secondaryDomains'?: Set<string>;
}

export const CompanySubscriptionStatusEnum = {
    Active: 'ACTIVE',
    Trial: 'TRIAL',
    Suspended: 'SUSPENDED'
} as const;

export type CompanySubscriptionStatusEnum = typeof CompanySubscriptionStatusEnum[keyof typeof CompanySubscriptionStatusEnum];


