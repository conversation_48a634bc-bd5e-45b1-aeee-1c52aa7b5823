/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface ProjectUser
 */
export interface ProjectUser {
    /**
     * 
     * @type {string}
     * @memberof ProjectUser
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof ProjectUser
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof ProjectUser
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof ProjectUser
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ProjectUser
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof ProjectUser
     */
    'projectUserId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof ProjectUser
     */
    'project'?: Project;
    /**
     * 
     * @type {User}
     * @memberof ProjectUser
     */
    'user'?: User;
    /**
     * 
     * @type {string}
     * @memberof ProjectUser
     */
    'role'?: ProjectUserRoleEnum;
}

export const ProjectUserRoleEnum = {
    Manager: 'Manager',
    Contributor: 'Contributor',
    Viewer: 'Viewer'
} as const;

export type ProjectUserRoleEnum = typeof ProjectUserRoleEnum[keyof typeof ProjectUserRoleEnum];


