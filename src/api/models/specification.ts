/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Specification
 */
export interface Specification {
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof Specification
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Specification
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Specification
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Specification
     */
    'specId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof Specification
     */
    'project'?: Project;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'division'?: string;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'section'?: string;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'material'?: string;
    /**
     * 
     * @type {string}
     * @memberof Specification
     */
    'manufacturer'?: string;
    /**
     * 
     * @type {number}
     * @memberof Specification
     */
    'specDocumentId'?: number;
}

