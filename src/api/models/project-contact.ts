/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Company } from './company';
// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface ProjectContact
 */
export interface ProjectContact {
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof ProjectContact
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof ProjectContact
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ProjectContact
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof ProjectContact
     */
    'contactId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof ProjectContact
     */
    'project'?: Project;
    /**
     * 
     * @type {Company}
     * @memberof ProjectContact
     */
    'company'?: Company;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'contactType'?: ProjectContactContactTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'phone'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectContact
     */
    'address'?: string;
}

export const ProjectContactContactTypeEnum = {
    Owner: 'Owner',
    Subcontractor: 'Subcontractor',
    Vendor: 'Vendor'
} as const;

export type ProjectContactContactTypeEnum = typeof ProjectContactContactTypeEnum[keyof typeof ProjectContactContactTypeEnum];


