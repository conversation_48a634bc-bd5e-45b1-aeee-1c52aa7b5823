/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Permission
 */
export interface Permission {
    /**
     * 
     * @type {number}
     * @memberof Permission
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof Permission
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Permission
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Permission
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Permission
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'description'?: string;
}

