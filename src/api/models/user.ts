/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Company } from './company';
// May contain unused imports in some cases
// @ts-ignore
import type { Role } from './role';

/**
 * 
 * @export
 * @interface User
 */
export interface User {
    /**
     * 
     * @type {number}
     * @memberof User
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof User
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof User
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof User
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof User
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {Company}
     * @memberof User
     */
    'company'?: Company;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'email'?: string;
    /**
     * 
     * @type {Role}
     * @memberof User
     */
    'role'?: Role;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'lastLoginDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'username'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'profilePictureUrl'?: string;
}

