/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Contract
 */
export interface Contract {
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof Contract
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Contract
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Contract
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Contract
     */
    'contractId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof Contract
     */
    'project'?: Project;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'type'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'contractNumber'?: string;
    /**
     * 
     * @type {number}
     * @memberof Contract
     */
    'value'?: number;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'parties'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'documentPath'?: string;
    /**
     * 
     * @type {number}
     * @memberof Contract
     */
    'riskScore'?: number;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'riskIssues'?: string;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'approvalStatus'?: string;
    /**
     * 
     * @type {User}
     * @memberof Contract
     */
    'approvedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Contract
     */
    'approvedDate'?: string;
}

