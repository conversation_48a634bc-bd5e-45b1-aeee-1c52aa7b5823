/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProjectUpdateDTO
 */
export interface ProjectUpdateDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectUpdateDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'status'?: ProjectUpdateDTOStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectUpdateDTO
     */
    'bidCaptainId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectUpdateDTO
     */
    'ownerByName'?: string;
}

export const ProjectUpdateDTOStatusEnum = {
    Initialized: 'INITIALIZED',
    DocumentsUploaded: 'DOCUMENTS_UPLOADED',
    Processing: 'PROCESSING',
    Completed: 'COMPLETED',
    Failed: 'FAILED'
} as const;

export type ProjectUpdateDTOStatusEnum = typeof ProjectUpdateDTOStatusEnum[keyof typeof ProjectUpdateDTOStatusEnum];


