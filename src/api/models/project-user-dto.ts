/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProjectUserDTO
 */
export interface ProjectUserDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectUserDTO
     */
    'projectUserId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectUserDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectUserDTO
     */
    'userId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'projectRole'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'modifiedByName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'createdAt'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectUserDTO
     */
    'modifiedById'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ProjectUserDTO
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectUserDTO
     */
    'name'?: string;
}

