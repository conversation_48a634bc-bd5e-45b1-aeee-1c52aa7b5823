/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DocumentWithPresignedUrlDTO } from './document-with-presigned-url-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ProjectDocumentSetDTO } from './project-document-set-dto';

/**
 * 
 * @export
 * @interface ProjectWithDocumentsDTO
 */
export interface ProjectWithDocumentsDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectWithDocumentsDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithDocumentsDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithDocumentsDTO
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithDocumentsDTO
     */
    'externalProjectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithDocumentsDTO
     */
    'documentSetVersion'?: string;
    /**
     * 
     * @type {Array<ProjectDocumentSetDTO>}
     * @memberof ProjectWithDocumentsDTO
     */
    'documentSets'?: Array<ProjectDocumentSetDTO>;
    /**
     * 
     * @type {Array<DocumentWithPresignedUrlDTO>}
     * @memberof ProjectWithDocumentsDTO
     */
    'documents'?: Array<DocumentWithPresignedUrlDTO>;
}

