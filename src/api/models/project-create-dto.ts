/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProjectCreateDTO
 */
export interface ProjectCreateDTO {
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'externalProjectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'documentSetVersion'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'createdById'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectCreateDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'owner'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectCreateDTO
     */
    'bidCaptainId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectCreateDTO
     */
    'description'?: string;
    /**
     * 
     * @type {Array<{ [key: string]: string; }>}
     * @memberof ProjectCreateDTO
     */
    'documents'?: Array<{ [key: string]: string; }>;
}

