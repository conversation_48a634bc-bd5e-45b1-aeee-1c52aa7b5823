/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CompanySubscriptionDTO
 */
export interface CompanySubscriptionDTO {
    /**
     * 
     * @type {number}
     * @memberof CompanySubscriptionDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof CompanySubscriptionDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {number}
     * @memberof CompanySubscriptionDTO
     */
    'subscriptionPlanId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscriptionDTO
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscriptionDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscriptionDTO
     */
    'status'?: CompanySubscriptionDTOStatusEnum;
}

export const CompanySubscriptionDTOStatusEnum = {
    Active: 'ACTIVE',
    Expired: 'EXPIRED',
    Cancelled: 'CANCELLED'
} as const;

export type CompanySubscriptionDTOStatusEnum = typeof CompanySubscriptionDTOStatusEnum[keyof typeof CompanySubscriptionDTOStatusEnum];


