/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Company } from './company';
// May contain unused imports in some cases
// @ts-ignore
import type { Permission } from './permission';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Role
 */
export interface Role {
    /**
     * 
     * @type {number}
     * @memberof Role
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof Role
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof Role
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Role
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Role
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Role
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Role
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof Role
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Role
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Role
     */
    'description'?: string;
    /**
     * 
     * @type {Company}
     * @memberof Role
     */
    'company'?: Company;
    /**
     * 
     * @type {Set<Permission>}
     * @memberof Role
     */
    'permissions'?: Set<Permission>;
}

