/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Company } from './company';
// May contain unused imports in some cases
// @ts-ignore
import type { SubscriptionPlan } from './subscription-plan';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface CompanySubscription
 */
export interface CompanySubscription {
    /**
     * 
     * @type {number}
     * @memberof CompanySubscription
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof CompanySubscription
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof CompanySubscription
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CompanySubscription
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof CompanySubscription
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {Company}
     * @memberof CompanySubscription
     */
    'company'?: Company;
    /**
     * 
     * @type {SubscriptionPlan}
     * @memberof CompanySubscription
     */
    'subscriptionPlan'?: SubscriptionPlan;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanySubscription
     */
    'status'?: CompanySubscriptionStatusEnum;
}

export const CompanySubscriptionStatusEnum = {
    Active: 'ACTIVE',
    Expired: 'EXPIRED',
    Cancelled: 'CANCELLED'
} as const;

export type CompanySubscriptionStatusEnum = typeof CompanySubscriptionStatusEnum[keyof typeof CompanySubscriptionStatusEnum];


