/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProjectDTO
 */
export interface ProjectDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'status'?: ProjectDTOStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'externalProjectId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'createdById'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'bidCaptainId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'createdByName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'modifiedByName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'owner'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'bidCaptainName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'createdAt'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'modifiedById'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ProjectDTO
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'currentUserProjectRole'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'scopeCount'?: number;
    /**
     * 
     * @type {Array<string>}
     * @memberof ProjectDTO
     */
    'currentUserProjectPermissions'?: Array<string>;
    /**
     * 
     * @type {number}
     * @memberof ProjectDTO
     */
    'latestDocumentSetId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'latestDocumentSetVersion'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'latestDocumentSetDisplayName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectDTO
     */
    'latestDocumentSetCreatedAt'?: string;
}

export const ProjectDTOStatusEnum = {
    Initialized: 'INITIALIZED',
    DocumentsUploaded: 'DOCUMENTS_UPLOADED',
    Processing: 'PROCESSING',
    Completed: 'COMPLETED',
    Failed: 'FAILED'
} as const;

export type ProjectDTOStatusEnum = typeof ProjectDTOStatusEnum[keyof typeof ProjectDTOStatusEnum];


