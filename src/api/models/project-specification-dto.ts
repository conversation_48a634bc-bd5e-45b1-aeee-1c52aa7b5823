/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProjectSpecificationDTO
 */
export interface ProjectSpecificationDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'scopeId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'division'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'section'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'material'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'manufacturer'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'documentId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectSpecificationDTO
     */
    'code'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'pageNumber'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectSpecificationDTO
     */
    'bidItemId'?: number;
}

