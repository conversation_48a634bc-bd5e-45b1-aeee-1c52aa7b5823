/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { Sheet } from './sheet';
// May contain unused imports in some cases
// @ts-ignore
import type { Specification } from './specification';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Scope
 */
export interface Scope {
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof Scope
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Scope
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Scope
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Scope
     */
    'scopeId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof Scope
     */
    'project'?: Project;
    /**
     * 
     * @type {Sheet}
     * @memberof Scope
     */
    'sheet'?: Sheet;
    /**
     * 
     * @type {Specification}
     * @memberof Scope
     */
    'specification'?: Specification;
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof Scope
     */
    'quantity'?: number;
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'unit'?: string;
    /**
     * 
     * @type {number}
     * @memberof Scope
     */
    'estimatedCost'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Scope
     */
    'createdByAI'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Scope
     */
    'confidenceScore'?: number;
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'status'?: string;
    /**
     * 
     * @type {string}
     * @memberof Scope
     */
    'category'?: string;
    /**
     * 
     * @type {number}
     * @memberof Scope
     */
    'bidSheetId'?: number;
}

