/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BidItemSheetDTO } from './bid-item-sheet-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { BidItemSpecificationDTO } from './bid-item-specification-dto';

/**
 * 
 * @export
 * @interface BidItemDTO
 */
export interface BidItemDTO {
    /**
     * 
     * @type {number}
     * @memberof BidItemDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof BidItemDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof BidItemDTO
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof BidItemDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof BidItemDTO
     */
    'scopeId'?: number;
    /**
     * 
     * @type {Array<BidItemSheetDTO>}
     * @memberof BidItemDTO
     */
    'bidItemSheets'?: Array<BidItemSheetDTO>;
    /**
     * 
     * @type {Array<BidItemSpecificationDTO>}
     * @memberof BidItemDTO
     */
    'bidItemSpecificationDTOList'?: Array<BidItemSpecificationDTO>;
}

