/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface DocumentDTO
 */
export interface DocumentDTO {
    /**
     * 
     * @type {number}
     * @memberof DocumentDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentDTO
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentDTO
     */
    'fileType'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentDTO
     */
    'filePath'?: string;
    /**
     * 
     * @type {number}
     * @memberof DocumentDTO
     */
    'size'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentDTO
     */
    'mimeType'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof DocumentDTO
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DocumentDTO
     */
    'inputDocumentType'?: DocumentDTOInputDocumentTypeEnum;
}

export const DocumentDTOInputDocumentTypeEnum = {
    Drawing: 'DRAWING',
    Specification: 'SPECIFICATION'
} as const;

export type DocumentDTOInputDocumentTypeEnum = typeof DocumentDTOInputDocumentTypeEnum[keyof typeof DocumentDTOInputDocumentTypeEnum];


