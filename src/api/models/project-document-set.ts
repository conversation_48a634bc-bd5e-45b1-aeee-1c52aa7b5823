/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface ProjectDocumentSet
 */
export interface ProjectDocumentSet {
    /**
     * 
     * @type {number}
     * @memberof ProjectDocumentSet
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDocumentSet
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof ProjectDocumentSet
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof ProjectDocumentSet
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof ProjectDocumentSet
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ProjectDocumentSet
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof ProjectDocumentSet
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectDocumentSet
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {Project}
     * @memberof ProjectDocumentSet
     */
    'project'?: Project;
    /**
     * 
     * @type {string}
     * @memberof ProjectDocumentSet
     */
    'documentSetVersion'?: string;
}

