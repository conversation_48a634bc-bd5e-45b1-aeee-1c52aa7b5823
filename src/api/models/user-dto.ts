/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { RoleDTO } from './role-dto';

/**
 * 
 * @export
 * @interface UserDTO
 */
export interface UserDTO {
    /**
     * 
     * @type {number}
     * @memberof UserDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof UserDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'lastLoginDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof UserDTO
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'companyName'?: string;
    /**
     * 
     * @type {RoleDTO}
     * @memberof UserDTO
     */
    'role'?: RoleDTO;
    /**
     * 
     * @type {string}
     * @memberof UserDTO
     */
    'profilePictureFileName'?: string;
}

