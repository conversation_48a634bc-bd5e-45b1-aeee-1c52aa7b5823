/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface BidSheetDTO
 */
export interface BidSheetDTO {
    /**
     * 
     * @type {number}
     * @memberof BidSheetDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof BidSheetDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof BidSheetDTO
     */
    'scopeId'?: number;
    /**
     * 
     * @type {string}
     * @memberof BidSheetDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof BidSheetDTO
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof BidSheetDTO
     */
    'totalEstimatedCost'?: number;
    /**
     * 
     * @type {string}
     * @memberof BidSheetDTO
     */
    'status'?: string;
    /**
     * 
     * @type {string}
     * @memberof BidSheetDTO
     */
    'type'?: BidSheetDTOTypeEnum;
}

export const BidSheetDTOTypeEnum = {
    BidItem: 'BidItem',
    Schedule: 'Schedule'
} as const;

export type BidSheetDTOTypeEnum = typeof BidSheetDTOTypeEnum[keyof typeof BidSheetDTOTypeEnum];


