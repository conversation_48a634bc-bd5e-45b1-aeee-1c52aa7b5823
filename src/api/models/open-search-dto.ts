/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BidItemSheetDTO } from './bid-item-sheet-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { BidItemSpecificationDTO } from './bid-item-specification-dto';

/**
 * 
 * @export
 * @interface OpenSearchDTO
 */
export interface OpenSearchDTO {
    /**
     * 
     * @type {string}
     * @memberof OpenSearchDTO
     */
    'id'?: string;
    /**
     * 
     * @type {number}
     * @memberof OpenSearchDTO
     */
    'documentId'?: number;
    /**
     * 
     * @type {number}
     * @memberof OpenSearchDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof OpenSearchDTO
     */
    'pageNumber'?: number;
    /**
     * 
     * @type {string}
     * @memberof OpenSearchDTO
     */
    'pageText'?: string;
    /**
     * 
     * @type {Array<BidItemSheetDTO>}
     * @memberof OpenSearchDTO
     */
    'sheets'?: Array<BidItemSheetDTO>;
    /**
     * 
     * @type {Array<BidItemSpecificationDTO>}
     * @memberof OpenSearchDTO
     */
    'specifications'?: Array<BidItemSpecificationDTO>;
}

