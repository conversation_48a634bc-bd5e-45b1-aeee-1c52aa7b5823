/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ScopeUpdateDTO
 */
export interface ScopeUpdateDTO {
    /**
     * 
     * @type {number}
     * @memberof ScopeUpdateDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'discipline'?: ScopeUpdateDTODisciplineEnum;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'division'?: string;
    /**
     * 
     * @type {number}
     * @memberof ScopeUpdateDTO
     */
    'projectDocumentSetId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'scopeNameWithSpecCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof ScopeUpdateDTO
     */
    'scopeSpecCode'?: string;
}

export const ScopeUpdateDTODisciplineEnum = {
    All: 'ALL',
    ProcurementAndContractingRequirements: 'PROCUREMENT_AND_CONTRACTING_REQUIREMENTS',
    GeneralRequirements: 'GENERAL_REQUIREMENTS',
    Architectural: 'ARCHITECTURAL',
    Structural: 'STRUCTURAL',
    FireProtection: 'FIRE_PROTECTION',
    Plumbing: 'PLUMBING',
    Mechanical: 'MECHANICAL',
    IntegratedAutomation: 'INTEGRATED_AUTOMATION',
    Electrical: 'ELECTRICAL',
    Communications: 'COMMUNICATIONS',
    ElectronicSafetyAndSecurity: 'ELECTRONIC_SAFETY_AND_SECURITY',
    Site: 'SITE',
    Landscape: 'LANDSCAPE',
    Transportation: 'TRANSPORTATION',
    WaterwayMarineAndCoastal: 'WATERWAY_MARINE_AND_COASTAL',
    Process: 'PROCESS',
    MaterialHandling: 'MATERIAL_HANDLING',
    ProcessHeating: 'PROCESS_HEATING',
    ProcessGasAndLiquidHandlingPurificationAndStorageEquipment: 'PROCESS_GAS_AND_LIQUID_HANDLING_PURIFICATION_AND_STORAGE_EQUIPMENT',
    PollutionAndWasteControlEquipment: 'POLLUTION_AND_WASTE_CONTROL_EQUIPMENT',
    IndustrySpecificManufacturingEquipment: 'INDUSTRY_SPECIFIC_MANUFACTURING_EQUIPMENT',
    WaterAndWastewaterEquipment: 'WATER_AND_WASTEWATER_EQUIPMENT',
    ElectricalPowerGeneration: 'ELECTRICAL_POWER_GENERATION',
    Site2: 'Site',
    Landscape2: 'Landscape',
    Architectural2: 'Architectural',
    Structural2: 'Structural',
    Mechanical2: 'Mechanical',
    FireProtection2: 'FireProtection',
    FireAlarm: 'FireAlarm',
    Plumbing2: 'Plumbing',
    Electrical2: 'Electrical',
    Civil: 'Civil'
} as const;

export type ScopeUpdateDTODisciplineEnum = typeof ScopeUpdateDTODisciplineEnum[keyof typeof ScopeUpdateDTODisciplineEnum];


