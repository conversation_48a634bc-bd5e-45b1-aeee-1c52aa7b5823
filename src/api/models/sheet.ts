/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Sheet
 */
export interface Sheet {
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof Sheet
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Sheet
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Sheet
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Sheet
     */
    'sheetId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof Sheet
     */
    'project'?: Project;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'sheetNumber'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'fileUrl'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'discipline'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sheet
     */
    'uploadedDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof Sheet
     */
    'uploadedBy'?: User;
    /**
     * 
     * @type {number}
     * @memberof Sheet
     */
    'externalSheetId'?: number;
}

