/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BidItemSheetSpecDTO } from './bid-item-sheet-spec-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PageableObject } from './pageable-object';
// May contain unused imports in some cases
// @ts-ignore
import type { SortObject } from './sort-object';

/**
 * 
 * @export
 * @interface PageBidItemSheetSpecDTO
 */
export interface PageBidItemSheetSpecDTO {
    /**
     * 
     * @type {number}
     * @memberof PageBidItemSheetSpecDTO
     */
    'totalElements'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageBidItemSheetSpecDTO
     */
    'totalPages'?: number;
    /**
     * 
     * @type {PageableObject}
     * @memberof PageBidItemSheetSpecDTO
     */
    'pageable'?: PageableObject;
    /**
     * 
     * @type {boolean}
     * @memberof PageBidItemSheetSpecDTO
     */
    'first'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PageBidItemSheetSpecDTO
     */
    'last'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof PageBidItemSheetSpecDTO
     */
    'size'?: number;
    /**
     * 
     * @type {Array<BidItemSheetSpecDTO>}
     * @memberof PageBidItemSheetSpecDTO
     */
    'content'?: Array<BidItemSheetSpecDTO>;
    /**
     * 
     * @type {number}
     * @memberof PageBidItemSheetSpecDTO
     */
    'number'?: number;
    /**
     * 
     * @type {SortObject}
     * @memberof PageBidItemSheetSpecDTO
     */
    'sort'?: SortObject;
    /**
     * 
     * @type {number}
     * @memberof PageBidItemSheetSpecDTO
     */
    'numberOfElements'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof PageBidItemSheetSpecDTO
     */
    'empty'?: boolean;
}

