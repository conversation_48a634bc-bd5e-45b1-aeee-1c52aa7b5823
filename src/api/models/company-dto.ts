/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CompanySubscriptionDTO } from './company-subscription-dto';

/**
 * 
 * @export
 * @interface CompanyDTO
 */
export interface CompanyDTO {
    /**
     * 
     * @type {number}
     * @memberof CompanyDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CompanyDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanyDTO
     */
    'industry'?: string;
    /**
     * 
     * @type {string}
     * @memberof CompanyDTO
     */
    'subscriptionStatus'?: CompanyDTOSubscriptionStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof CompanyDTO
     */
    'createdAt'?: string;
    /**
     * 
     * @type {Array<CompanySubscriptionDTO>}
     * @memberof CompanyDTO
     */
    'subscriptionPlans'?: Array<CompanySubscriptionDTO>;
    /**
     * 
     * @type {string}
     * @memberof CompanyDTO
     */
    'primaryDomain'?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CompanyDTO
     */
    'secondaryDomains'?: Array<string>;
}

export const CompanyDTOSubscriptionStatusEnum = {
    Active: 'ACTIVE',
    Trial: 'TRIAL',
    Suspended: 'SUSPENDED'
} as const;

export type CompanyDTOSubscriptionStatusEnum = typeof CompanyDTOSubscriptionStatusEnum[keyof typeof CompanyDTOSubscriptionStatusEnum];


