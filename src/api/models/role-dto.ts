/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PermissionDTO } from './permission-dto';

/**
 * 
 * @export
 * @interface RoleDTO
 */
export interface RoleDTO {
    /**
     * 
     * @type {number}
     * @memberof RoleDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof RoleDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof RoleDTO
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof RoleDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {Set<PermissionDTO>}
     * @memberof RoleDTO
     */
    'permissions'?: Set<PermissionDTO>;
}

