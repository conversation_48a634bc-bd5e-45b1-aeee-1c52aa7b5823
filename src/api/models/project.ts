/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Company } from './company';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface Project
 */
export interface Project {
    /**
     * 
     * @type {number}
     * @memberof Project
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'createdAt'?: string;
    /**
     * 
     * @type {User}
     * @memberof Project
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof Project
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'modifiedAt'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Project
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Project
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'deleteAt'?: string;
    /**
     * 
     * @type {Company}
     * @memberof Project
     */
    'company'?: Company;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'status'?: ProjectStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'externalProjectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'owner'?: string;
    /**
     * 
     * @type {User}
     * @memberof Project
     */
    'bidCaptain'?: User;
}

export const ProjectStatusEnum = {
    Initialized: 'INITIALIZED',
    DocumentsUploaded: 'DOCUMENTS_UPLOADED',
    Processing: 'PROCESSING',
    Completed: 'COMPLETED',
    Failed: 'FAILED'
} as const;

export type ProjectStatusEnum = typeof ProjectStatusEnum[keyof typeof ProjectStatusEnum];


