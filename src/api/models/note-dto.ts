/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface NoteDTO
 */
export interface NoteDTO {
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'projectId': number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'documentId': number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'scopeId'?: number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'bidItemId'?: number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'sheetId'?: number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'specificationId'?: number;
    /**
     * 
     * @type {number}
     * @memberof NoteDTO
     */
    'pageNumber'?: number;
    /**
     * 
     * @type {string}
     * @memberof NoteDTO
     */
    'annotationData'?: string;
    /**
     * 
     * @type {string}
     * @memberof NoteDTO
     */
    'createdByName'?: string;
    /**
     * 
     * @type {string}
     * @memberof NoteDTO
     */
    'modifiedByName'?: string;
}

