/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for WyreAI Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Project } from './project';
// May contain unused imports in some cases
// @ts-ignore
import type { User } from './user';

/**
 * 
 * @export
 * @interface BidSheet
 */
export interface BidSheet {
    /**
     * 
     * @type {string}
     * @memberof BidSheet
     */
    'createdDate'?: string;
    /**
     * 
     * @type {User}
     * @memberof BidSheet
     */
    'createdBy'?: User;
    /**
     * 
     * @type {User}
     * @memberof BidSheet
     */
    'modifiedBy'?: User;
    /**
     * 
     * @type {string}
     * @memberof BidSheet
     */
    'modifiedDate'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof BidSheet
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof BidSheet
     */
    'bidSheetId'?: number;
    /**
     * 
     * @type {Project}
     * @memberof BidSheet
     */
    'project'?: Project;
    /**
     * 
     * @type {string}
     * @memberof BidSheet
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof BidSheet
     */
    'description'?: string;
    /**
     * 
     * @type {number}
     * @memberof BidSheet
     */
    'totalEstimatedCost'?: number;
    /**
     * 
     * @type {string}
     * @memberof BidSheet
     */
    'status'?: string;
}

