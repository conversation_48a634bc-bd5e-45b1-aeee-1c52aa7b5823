/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { RegionDTO } from './region-dto';

/**
 * 
 * @export
 * @interface SheetDTO
 */
export interface SheetDTO {
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SheetDTO
     */
    'sheetNumber'?: string;
    /**
     * 
     * @type {string}
     * @memberof SheetDTO
     */
    'title'?: string;
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'documentId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SheetDTO
     */
    'discipline'?: string;
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'pageNumber'?: number;
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'pageWidth'?: number;
    /**
     * 
     * @type {number}
     * @memberof SheetDTO
     */
    'pageHeight'?: number;
    /**
     * 
     * @type {Array<RegionDTO>}
     * @memberof SheetDTO
     */
    'regionDetail'?: Array<RegionDTO>;
}

