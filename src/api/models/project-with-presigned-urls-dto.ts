/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PresignedUrlResponse } from './presigned-url-response';

/**
 * 
 * @export
 * @interface ProjectWithPresignedUrlsDTO
 */
export interface ProjectWithPresignedUrlsDTO {
    /**
     * 
     * @type {number}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'projectId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'companyId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'location'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'startDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'endDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'externalProjectId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'createdById'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'documentSetVersionId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'bidCaptainId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'createdByName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'owner'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'bidCaptainName'?: string;
    /**
     * 
     * @type {Array<PresignedUrlResponse>}
     * @memberof ProjectWithPresignedUrlsDTO
     */
    'responses'?: Array<PresignedUrlResponse>;
}

