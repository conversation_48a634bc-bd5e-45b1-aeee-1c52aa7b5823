import { registerLicense } from '@syncfusion/ej2-base';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { notification } from 'antd';
import { useEffect } from 'react';
import { Route, Routes } from 'react-router-dom';
import { ProjectShell } from './projectShell';
import { LoginPage } from '../view/screens/login/loginPage';

const AppRoutes = () => {
  // Global notification config
  useEffect(() => {
    notification.config({
      placement: 'bottomRight',
      duration: 3
    });
  }, []);
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false
      }
    }
  });
  registerLicense(import.meta.env.VITE_SYNC_FUSION_KEY);
  return (
    <QueryClientProvider client={queryClient}>
      <Routes>
        <Route path='/' element={<LoginPage />} />
        <Route path='/*' element={<ProjectShell />} />
      </Routes>
    </QueryClientProvider>
  );
};

export { AppRoutes };
