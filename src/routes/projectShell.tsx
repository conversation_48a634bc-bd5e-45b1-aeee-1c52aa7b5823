import { Route, Routes } from 'react-router-dom';
import Admin from 'src/modules/admin';
import IntegrationsSettings from 'src/modules/admin/integrations/IntegrationSettings';
import AdminProjects from 'src/modules/admin/projects/projects';
import Reports from 'src/modules/admin/reports/reports';
import UsersRolesReport from 'src/modules/admin/reports/usersRolesReport/usersRolesReport';
import AdminUserManagement from 'src/modules/admin/users/userManagement';
import AdminGuard from 'src/modules/guards/AdminGuard';
import CompanyManagement from 'src/modules/wyreAIAdmin/companyManagement/companyManagement';
import SubscriptionSettings from 'src/modules/wyreAIAdmin/subscriptionSettings/subscriptionSettings';
import Layout from '../modules/common/layout';
import CompaniesList from '../modules/company/companiesList';
import CompanyDetail from '../modules/company/companyDetails';
import NotFound from '../modules/error/NotFound';
import AuthGuard from '../modules/guards/authGuard';
import HasPermission from '../modules/guards/HasPermission';
import WyreAIAdminGuard from '../modules/guards/WyreAIAdminGuard';
import DocumentsList from '../modules/projectDocuments/documentsList';
import ProjectDetail from '../modules/projects/projectDetail';
import ProjectsList from '../modules/projects/projectsList';
import Scopes from '../modules/scopes';
import ScopeInfo from '../modules/scopes/scopeInfo';
import UserDetail from '../modules/user/userDetails';
import UsersList from '../modules/user/usersList';
import { appRoutes } from '../modules/utils/constant';
import { UserPermission } from '../modules/utils/permissions';
import WyreAIAdmin from '../modules/wyreAIAdmin';
import Events from '../modules/wyreAIAdmin/events/events';
import Metrics from '../modules/wyreAIAdmin/metrics/metrics';
import Projects from '../modules/wyreAIAdmin/projects/projects';
import UserManagement from '../modules/wyreAIAdmin/userManagement/userManagement';

export const ProjectShell: React.FC = () => {
  return (
    <Routes>
      <Route
        element={
          <AuthGuard>
            <Layout />
          </AuthGuard>
        }
      >
        <Route path={appRoutes.projects}>
          <Route index element={<ProjectsList />} />
          <Route path=':projectId/'>
            <Route index element={<ProjectDetail />} />
            <Route path={appRoutes.scopes} element={<Scopes />}>
              <Route path=':scopeId/' element={<ScopeInfo />} />
            </Route>
            <Route path={appRoutes.documents} element={<DocumentsList />} />
            <Route path={appRoutes.bidSheets} element={<div>Bid Sheets</div>} />
            <Route path={appRoutes.dashboard} element={<div>Dashboard</div>} />
          </Route>
        </Route>
        <Route path={appRoutes.enterprise} element={<div> Enterprise </div>} />
        <Route path={appRoutes.user}>
          <Route index element={<UsersList />} />
          <Route path=':userId/' element={<UserDetail />} />
        </Route>
        <Route path={appRoutes.company}>
          <Route
            index
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompaniesList />
              </HasPermission>
            }
          />
          <Route
            path=':companyId/'
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompanyDetail />
              </HasPermission>
            }
          />
        </Route>
        <Route path={appRoutes.admin}>
          <Route
            index
            element={
              <AdminGuard>
                <Admin />
              </AdminGuard>
            }
          />
          <Route
            path='user-management'
            element={
              <AdminGuard>
                <AdminUserManagement />
              </AdminGuard>
            }
          />
          <Route
            path='projects'
            element={
              <AdminGuard>
                <AdminProjects />
              </AdminGuard>
            }
          />
          <Route
            path='reports'
            element={
              <AdminGuard>
                <Reports />
              </AdminGuard>
            }
          />
          <Route
            path='reports/users-roles'
            element={
              <AdminGuard>
                <UsersRolesReport />
              </AdminGuard>
            }
          />
          <Route
            path='integrations'
            element={
              <AdminGuard>
                <IntegrationsSettings />
              </AdminGuard>
            }
          />
        </Route>
        <Route path={appRoutes.wyreAIAdmin}>
          <Route
            index
            element={
              <WyreAIAdminGuard>
                <WyreAIAdmin />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='user-management'
            element={
              <WyreAIAdminGuard>
                <UserManagement />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='company-management'
            element={
              <WyreAIAdminGuard>
                <CompanyManagement />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='projects'
            element={
              <WyreAIAdminGuard>
                <Projects />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='metrics'
            element={
              <WyreAIAdminGuard>
                <Metrics />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='events'
            element={
              <WyreAIAdminGuard>
                <Events />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='subscription-settings'
            element={
              <WyreAIAdminGuard>
                <SubscriptionSettings />
              </WyreAIAdminGuard>
            }
          />
        </Route>
      </Route>
      <Route path='*' element={<NotFound />} />
    </Routes>
  );
};
