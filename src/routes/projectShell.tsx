import { Route, Routes } from 'react-router-dom';
import AdminGuard from 'src/modules/guards/AdminGuard';
import Admin from 'src/view/screens/admin';
import IntegrationsSettings from 'src/view/screens/admin/integrations/IntegrationSettings';
import AdminProjects from 'src/view/screens/admin/projects/Projects';
import Reports from 'src/view/screens/admin/reports/Reports';
import UsersRolesReport from 'src/view/screens/admin/reports/usersRolesReport/UsersRolesReport';
import AdminTemplatesScreen from 'src/view/screens/admin/templates/Templates';
import AdminUserManagement from 'src/view/screens/admin/users/UserManagement';
import CompanyManagement from 'src/view/screens/wyre-ai-admin/company-management/CompanyManagement';
import SubscriptionSettings from 'src/view/screens/wyre-ai-admin/subscription-settings/SubscriptionSettings';
import Layout from '../modules/common/layout';
import NotFound from '../modules/error/NotFound';
import AuthGuard from '../modules/guards/authGuard';
import HasPermission from '../modules/guards/HasPermission';
import WyreAIAdminGuard from '../modules/guards/WyreAIAdminGuard';
import { appRoutes } from '../modules/utils/constant';
import { UserPermission } from '../modules/utils/permissions';
import {
  ProjectsList,
  ProjectDetails,
  CompanyList,
  CompanyDetails,
  UserList,
  UserDetails,
  DocumentsList
} from '../view/screens';
import DocumentInfo from '../view/screens/doc-view/document-info/DocumentInfo';
import DocumentViewer from '../view/screens/doc-view/index';
import Scopes from '../view/screens/scopes';
import ScopeInfo from '../view/screens/scopes/scope-table/ScopeInfo';
import WyreAIAdmin from '../view/screens/wyre-ai-admin';
import Events from '../view/screens/wyre-ai-admin/events/Events';
import Metrics from '../view/screens/wyre-ai-admin/metrics/Metrics';
import Projects from '../view/screens/wyre-ai-admin/projects/Projects';
import UserManagement from '../view/screens/wyre-ai-admin/user-management/UserManagement';

export const ProjectShell: React.FC = () => {
  return (
    <Routes>
      <Route
        element={
          <AuthGuard>
            <Layout />
          </AuthGuard>
        }
      >
        <Route path={appRoutes.projects}>
          <Route index element={<ProjectsList />} />
          <Route path=':projectId/'>
            <Route index element={<ProjectDetails />} />
            <Route path={appRoutes.scopes} element={<Scopes />}>
              <Route path=':scopeId/' element={<ScopeInfo />} />
            </Route>
            <Route path={appRoutes.docView} element={<DocumentViewer />}>
              <Route path=':scopeId/' element={<DocumentInfo />} />
            </Route>
            <Route path={appRoutes.documents} element={<DocumentsList />} />
            <Route path={appRoutes.bidSheets} element={<div>Bid Sheets</div>} />
            <Route path={appRoutes.dashboard} element={<div>Dashboard</div>} />
          </Route>
        </Route>
        <Route path={appRoutes.enterprise} element={<div> Enterprise </div>} />
        <Route path={appRoutes.user}>
          <Route index element={<UserList />} />
          <Route path=':userId/' element={<UserDetails />} />
        </Route>
        <Route path={appRoutes.company}>
          <Route
            index
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompanyList />
              </HasPermission>
            }
          />
          <Route
            path=':companyId/'
            element={
              <HasPermission requiredPermissions={[UserPermission.CREATE_COMPANY]} redirectTo='/'>
                <CompanyDetails />
              </HasPermission>
            }
          />
        </Route>
        <Route path={appRoutes.admin}>
          <Route
            index
            element={
              <AdminGuard>
                <Admin />
              </AdminGuard>
            }
          />
          <Route
            path='user-management'
            element={
              <AdminGuard>
                <AdminUserManagement />
              </AdminGuard>
            }
          />
          <Route
            path='projects'
            element={
              <AdminGuard>
                <AdminProjects />
              </AdminGuard>
            }
          />
          <Route
            path='reports'
            element={
              <AdminGuard>
                <Reports />
              </AdminGuard>
            }
          />
          <Route
            path='reports/users-roles'
            element={
              <AdminGuard>
                <UsersRolesReport />
              </AdminGuard>
            }
          />
          <Route
            path='integrations'
            element={
              <AdminGuard>
                <IntegrationsSettings />
              </AdminGuard>
            }
          />
          <Route
            path='templates'
            element={
              <AdminGuard>
                <AdminTemplatesScreen />
              </AdminGuard>
            }
          />
        </Route>
        <Route path={appRoutes.wyreAIAdmin}>
          <Route
            index
            element={
              <WyreAIAdminGuard>
                <WyreAIAdmin />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='user-management'
            element={
              <WyreAIAdminGuard>
                <UserManagement />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='company-management'
            element={
              <WyreAIAdminGuard>
                <CompanyManagement />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='projects'
            element={
              <WyreAIAdminGuard>
                <Projects />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='metrics'
            element={
              <WyreAIAdminGuard>
                <Metrics />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='events'
            element={
              <WyreAIAdminGuard>
                <Events />
              </WyreAIAdminGuard>
            }
          />
          <Route
            path='subscription-settings'
            element={
              <WyreAIAdminGuard>
                <SubscriptionSettings />
              </WyreAIAdminGuard>
            }
          />
        </Route>
      </Route>
      <Route path='*' element={<NotFound />} />
    </Routes>
  );
};
