import { PlusOutlined } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { App, Form, GetProp, Modal, Select, UploadFile, UploadProps, Image } from 'antd';
import {
  StyledFormAndProfile,
  StyledFormInputs,
  StyledInput,
  StyledUpload,
  StyledUploadWrapper,
  UploadIcon
} from './AddUser.style';
import { UploadChangeParam } from 'antd/es/upload';
import { isEqual } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { UserDTO } from 'src/api';
import { userAPI, roleAPI, companyAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import CreatableSearchSelect from 'src/modules/common/creatableSearchSelect';
import { APIMutationStatus, jobRoles, queryKeys } from 'src/modules/utils/constant';
import { extractErrorMessage } from 'src/modules/utils/errorHandler';
import { UserRole } from 'src/modules/utils/permissions';

type AddUserProps = {
  open: boolean;
  onClose: () => void;
  editMode: boolean;
  selectedUser?: UserDTO;
};

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const AddUser: React.FC<AddUserProps> = ({ open, onClose, editMode, selectedUser }) => {
  const { Option } = Select;
  const { useForm } = Form;
  const [form] = useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();
  const { notification } = App.useApp();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { currentUser } = useGlobalStore();

  const { data: companiesList, isPending: isCompaniesLoading } = useQuery({
    queryKey: [queryKeys.companiesList],
    queryFn: () => companyAPI.getAllCompanies(),
    retry: false
  });

  const { data: rolesList, isPending: isRolesLoading } = useQuery({
    queryKey: [queryKeys.rolesList],
    queryFn: () => roleAPI.getAllRoles(),
    retry: false
  });

  const { mutate, status, reset } = useMutation({
    mutationFn: (data: UserDTO) => userAPI.createUser(data),
    onSuccess: async response => {
      if (response.data.profilePictureUploadUrl && formValues['Image']) {
        try {
          await uploadFileToS3(formValues['Image'], response.data.profilePictureUploadUrl);
          queryClient.invalidateQueries({ queryKey: [queryKeys.usersList] });
          queryClient.invalidateQueries({ queryKey: [queryKeys.userDetails] });
        } catch (error) {
          notification.error({ message: 'Failed to upload profile picture' });
        }
      }
      queryClient.invalidateQueries({ queryKey: [queryKeys.usersList] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.userDetails] });
      notification.success({ message: 'User created successfully' });
    },
    onError: error =>
      notification.error({ message: extractErrorMessage(error, 'User creation failed') })
  });

  const initialValues = useMemo(() => {
    if (editMode && selectedUser) {
      return {
        UserName: selectedUser?.name,
        Email: selectedUser?.email,
        Title: selectedUser?.title,
        RoleId: selectedUser?.role?.id,
        ...(currentUser?.role?.name === UserRole.superAdmin && {
          CompanyId: selectedUser?.companyId
        }),
        Image: selectedUser?.profilePictureFileName
          ? `${import.meta.env.VITE_S3_BASE_URL}/${selectedUser?.profilePictureFileName}`
          : null
      };
    }
    return undefined;
  }, [editMode, selectedUser, currentUser?.role?.name]);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
      if (initialValues?.Image) {
        setFileList([
          {
            uid: '-1',
            name: 'profile.png',
            status: 'done',
            url: initialValues?.Image
          }
        ]);
      }
    }
  }, [initialValues, form]);

  const hasFormChanged = useMemo(() => {
    if (!formValues) return;

    return !isEqual(formValues, initialValues);
  }, [formValues, initialValues]);

  const { mutate: updateUser } = useMutation({
    mutationFn: (data: { id: number; userDTO: UserDTO }) =>
      userAPI.updateUser(data.id, data.userDTO),
    onSuccess: async response => {
      if (response.data.profilePictureUploadUrl && formValues['Image']) {
        try {
          await uploadFileToS3(formValues['Image'], response.data.profilePictureUploadUrl);
          queryClient.invalidateQueries({ queryKey: [queryKeys.userInfo] });
          queryClient.invalidateQueries({ queryKey: [queryKeys.userDetails] });
        } catch (error) {
          notification.error({ message: 'Failed to upload profile picture' });
        }
      }
      queryClient.invalidateQueries({ queryKey: [queryKeys.userInfo] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.userDetails] });
      notification.success({ message: 'User edited successfully' });
    },
    onError: error =>
      notification.error({ message: extractErrorMessage(error, 'User update failed') })
  });

  const handleModalClose = () => {
    form.resetFields();
    reset();
    onClose();
  };

  const createUser = async () => {
    try {
      const data: UserDTO = {
        name: formValues['UserName'],
        email: formValues['Email'],
        title: formValues['Title'],
        role: rolesList?.data?.content?.find(role => role.id === formValues['RoleId']),
        companyId:
          currentUser?.role?.name === UserRole.superAdmin
            ? formValues['CompanyId']
            : currentUser?.companyId,
        isActive: true,
        profilePictureFileName: formValues['Image'] ? formValues['Image'].name : null
      };
      if (editMode) {
        updateUser({
          id: Number(selectedUser?.id),
          userDTO: data
        });
      } else {
        mutate(data);
      }
      handleModalClose();
    } catch {
      notification.error({ message: 'User creation failed' });
    }
  };

  const handleSave = async () => {
    if (status === APIMutationStatus.success) {
      handleModalClose();
      return;
    }
    try {
      await form.validateFields();
      await createUser();
    } catch {
      notification.error({ message: 'Validation failed' });
    }
  };

  const getBase64 = (file: FileType): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const uploadFileToS3 = async (file: File, presignedUrl: string): Promise<void> => {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type
      }
    });
    if (!response?.ok) {
      throw new Error(`Failed to upload file: ${response.statusText}`);
    }
  };

  const onImageChangeHandler = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length === 0) {
      form.setFieldValue('Image', null);
      setFileList([]);
    } else {
      form.setFieldValue('Image', info.fileList[0].originFileObj);
      setFileList(info.fileList);
    }
  };

  return (
    <Modal
      title={editMode ? 'Edit user' : 'Add user'}
      centered
      open={open}
      closable={false}
      maskClosable={false}
      onCancel={handleModalClose}
      okText={editMode ? 'Save' : 'Add'}
      onOk={handleSave}
      okButtonProps={{ disabled: !hasFormChanged }}
    >
      <Form layout='vertical' form={form}>
        <StyledFormAndProfile>
          <StyledFormInputs>
            <Form.Item
              label='Name'
              name='UserName'
              rules={[{ required: true, message: 'Please enter username!' }]}
            >
              <StyledInput placeholder='Enter user name' />
            </Form.Item>
            <Form.Item
              label='Email'
              name='Email'
              rules={[
                {
                  type: 'email',
                  message: 'The input is not valid E-mail!'
                },
                {
                  required: true,
                  message: 'Please enter email!'
                }
              ]}
            >
              <StyledInput placeholder='Enter email' disabled={editMode} />
            </Form.Item>
            <Form.Item
              label='Job title'
              name='Title'
              rules={[{ required: true, message: 'Please select title' }]}
            >
              <CreatableSearchSelect
                initialOptions={jobRoles}
                placeholder='Select job title'
                isMultiple={false}
                onSelectionChange={(selectedValues: string[]) => {
                  form.setFieldValue('Title', selectedValues[0]);
                }}
                defaultSelectedValues={formValues?.Title ? [formValues?.Title] : []}
              />
            </Form.Item>
            <Form.Item
              name='RoleId'
              label='Roles'
              rules={[{ required: true, message: 'Please select at least one role' }]}
            >
              <Select
                placeholder='Select roles'
                loading={isRolesLoading}
                disabled={
                  isRolesLoading ||
                  (currentUser?.id === selectedUser?.id &&
                    currentUser?.role?.name !== UserRole.superAdmin &&
                    currentUser?.role?.name !== UserRole.companyAdmin)
                }
              >
                {rolesList?.data?.content?.map(role =>
                  role.name === 'Super Admin' &&
                  currentUser?.role?.name !== UserRole.superAdmin ? null : (
                    <Option value={role.id}>{role.name}</Option>
                  )
                )}
              </Select>
            </Form.Item>
            {currentUser?.role?.name === UserRole.superAdmin && (
              <Form.Item
                name='CompanyId'
                label='Company'
                rules={[{ required: true, message: 'Please select company' }]}
              >
                <Select
                  placeholder='Select company'
                  loading={isCompaniesLoading}
                  disabled={isCompaniesLoading}
                >
                  {companiesList?.data?.content?.map(company => (
                    <Option value={company.id}>{company.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </StyledFormInputs>
          <Form.Item name='Image' label='Profile photo'>
            <StyledUploadWrapper>
              <StyledUpload
                isImageUploaded={!form.getFieldValue('Image')}
                listType='picture-card'
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true
                }}
                beforeUpload={() => false}
                onPreview={handlePreview}
                onChange={info => onImageChangeHandler(info)}
                onRemove={() => {
                  form.setFieldValue('Image', null);
                  return true;
                }}
                fileList={fileList}
              >
                {!form.getFieldValue('Image') && (
                  <UploadIcon>
                    <PlusOutlined />
                    Upload
                  </UploadIcon>
                )}
              </StyledUpload>
            </StyledUploadWrapper>
            {previewImage && (
              <Image
                wrapperStyle={{ display: 'none' }}
                preview={{
                  visible: previewOpen,
                  onVisibleChange: visible => setPreviewOpen(visible),
                  afterOpenChange: visible => !visible && setPreviewImage('')
                }}
                src={previewImage}
              />
            )}
          </Form.Item>
        </StyledFormAndProfile>
      </Form>
    </Modal>
  );
};

export default AddUser;
