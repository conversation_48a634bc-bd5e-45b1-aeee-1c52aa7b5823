import { Input, Upload } from "antd";
import styled from "styled-components";
import { themeTokens } from "src/theme/tokens";


export const StyledUpload = styled(Upload)<{ isImageUploaded: boolean }>`
  border: ${({ isImageUploaded }) => (isImageUploaded ? '1px dashed gray' : 'none')};
  border-radius: 10px;
`;

export const StyledFormAndProfile = styled.div`
  display: flex;
  gap: 20px;
`;

export const StyledFormInputs = styled.div`
  flex: 1;
`;

export const StyledUploadWrapper = styled.div`
  width: 150px;
`;

export const StyledInput = styled(Input)`
  border-radius: 4px;
  border: 1px solid ${themeTokens.inputBorder};
  padding: 8px 10px;
`;

export const UploadIcon = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
`;