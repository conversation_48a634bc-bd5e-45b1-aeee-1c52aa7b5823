import { SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Button, Dropdown, Flex, Form, List, Pagination, Empty, Select, Tooltip } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { FaChevronRight } from 'react-icons/fa';
import { FaPlus } from 'react-icons/fa6';
import { LiaSlidersHSolid } from 'react-icons/lia';
import { LuArrowDownUp } from 'react-icons/lu';
import { Link } from 'react-router-dom';
import { PageUserDTO, UserDTO } from 'src/api';
import { companyAPI, roleAPI, userAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import AddUser from './add-user/AddUser';
import CustomIcon from 'src/modules/common/customIcon';
import { queryKeys } from 'src/modules/utils/constant';
import { UserRole } from 'src/modules/utils/permissions';
import {
  Container,
  Content,
  Header,
  HeaderContainer,
  StyledButton,
  StyledCard,
  StyledAvatar,
  FilterDropdownContainer,
  UserTitle,
  UserName,
  UserDetails,
  StyledInput
} from './UserList.style';

interface FilterState {
  roleId?: number;
  companyId?: number;
}

const UserList: React.FC = () => {
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState<boolean>(false);
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [filters, setFilters] = useState<FilterState>({});
  const [isSortAscending, setIsSortAscending] = useState<boolean>(false);

  const [filterForm] = Form.useForm();
  const { currentUser } = useGlobalStore();

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // API Queries
  const { data: rolesList, isPending: isRolesLoading } = useQuery({
    queryKey: [queryKeys.rolesList],
    queryFn: () => roleAPI.getAllRoles(),
    select: res => res.data?.content || [],
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const { data: companiesList, isPending: isCompaniesLoading } = useQuery({
    queryKey: [queryKeys.companiesList],
    queryFn: () => companyAPI.getAllCompanies(0, 1000),
    select: res => {
      if (res.data && typeof res.data === 'object' && 'content' in res.data) {
        return res.data.content || [];
      }
      return [];
    },
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const { data: userList, isPending } = useQuery({
    queryKey: [
      queryKeys.userList,
      currentPage,
      pageSize,
      debouncedSearchText,
      filters.roleId,
      filters.companyId,
      isSortAscending
    ],
    queryFn: () =>
      userAPI.getAllUsers(
        currentPage,
        pageSize,
        ['createdAt', isSortAscending ? 'asc' : 'desc'],
        debouncedSearchText,
        undefined,
        filters.roleId,
        filters.companyId
      ),
    select: res => res.data as PageUserDTO,
    staleTime: 30000 // 30 seconds
  });

  const handleAddUser = useCallback(() => setIsAddUserModalOpen(true), []);

  const onAddUserModalClose = useCallback(() => setIsAddUserModalOpen(false), []);

  const handleFilterReset = useCallback(() => {
    filterForm.resetFields();
    setFilters({});
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const handleFilterApply = useCallback(() => {
    const values = filterForm.getFieldsValue();
    setFilters(values);
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const onSearchHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    setCurrentPage(0);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page - 1);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  const FilterFormContent = () => (
    <Form form={filterForm} layout='vertical' initialValues={filters}>
      <Form.Item label='Role' name='roleId'>
        <Select
          placeholder='Select Role'
          allowClear
          loading={isRolesLoading}
          options={(rolesList || [])
            ?.filter(
              role => role.name !== 'Super Admin' || currentUser?.role?.name === UserRole.superAdmin
            )
            ?.map(role => ({ label: role.name, value: role.id }))}
        />
      </Form.Item>
      <Form.Item label='Company' name='companyId'>
        <Select
          placeholder='Select Company'
          allowClear
          loading={isCompaniesLoading}
          options={(companiesList || [])?.map(company => ({
            label: company.name,
            value: company.id
          }))}
        />
      </Form.Item>
      <Flex justify='space-between' align='center'>
        <Button onClick={handleFilterReset}>Reset</Button>
        <Button type='primary' onClick={handleFilterApply}>
          Apply Filters
        </Button>
      </Flex>
    </Form>
  );

  return (
    <Container>
      <Content>
        <HeaderContainer>
          <Header>Users</Header>
          <Flex gap={24} justify='flex-end' align='center'>
            <StyledInput
              size='large'
              placeholder='Search here'
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={onSearchHandler}
            />
            <Tooltip title='Sort'>
              <LuArrowDownUp
                cursor='pointer'
                onClick={() => setIsSortAscending(!isSortAscending)}
                size={20}
              />
            </Tooltip>
            <Tooltip title='Filter'>
              <Dropdown
                trigger={['click']}
                placement='bottomRight'
                open={isFilterDropdownOpen}
                onOpenChange={setIsFilterDropdownOpen}
                dropdownRender={() => (
                  <FilterDropdownContainer>
                    <Flex vertical gap={16}>
                      <h3>Filter Users</h3>
                      <FilterFormContent />
                    </Flex>
                  </FilterDropdownContainer>
                )}
              >
                <LiaSlidersHSolid cursor='pointer' size={22} />
              </Dropdown>
            </Tooltip>
            <StyledButton
              onClick={handleAddUser}
              icon={<CustomIcon Icon={FaPlus} />}
              type='primary'
            >
              Add User
            </StyledButton>
          </Flex>
        </HeaderContainer>
        <List
          grid={{
            gutter: 25,
            xs: 1,
            sm: 1,
            md: 2,
            lg: 2,
            xl: 2,
            xxl: 2
          }}
          loading={isPending}
          dataSource={(userList?.content || []) as UserDTO[]}
          locale={{ emptyText: <Empty description='No users found' /> }}
          renderItem={item => (
            <List.Item>
              <StyledCard>
                <Flex align='center' justify='space-between'>
                  <Flex gap={8} vertical>
                    <UserTitle>
                      <UserName>{item?.name}</UserName>
                    </UserTitle>
                    <UserDetails>{item?.email}</UserDetails>
                    <UserDetails>{item?.title}</UserDetails>
                  </Flex>
                  <Link to={`${item?.id}`}>
                    <StyledAvatar icon={<CustomIcon Icon={FaChevronRight} />} />
                  </Link>
                </Flex>
              </StyledCard>
            </List.Item>
          )}
        />
        {userList && userList.totalElements && userList.totalElements > 0 ? (
          <Flex justify='center'>
            <Pagination
              current={currentPage + 1}
              pageSize={pageSize}
              total={userList.totalElements}
              showSizeChanger
              pageSizeOptions={['5', '10', '20', '50']}
              onChange={handlePageChange}
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} users`}
            />
          </Flex>
        ) : null}
      </Content>
      {isAddUserModalOpen && (
        <AddUser open={isAddUserModalOpen} onClose={onAddUserModalClose} editMode={false} />
      )}
    </Container>
  );
};

export default UserList;
