import { useMutation, useQuery } from '@tanstack/react-query';
import { App, Col, Popconfirm, Row } from 'antd';
import { useCallback, useState } from 'react';
import { MdEdit } from 'react-icons/md';
import { RxCrossCircled } from 'react-icons/rx';
import { useNavigate, useParams } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import { appRoutes, queryKeys } from '../../../../modules/utils/constant';
import AddUser from '../user-list/add-user/AddUser';
import {
  Container,
  Header,
  Title,
  FieldValue,
  ContentWrapper,
  StyledButton
} from './UserDetails.style';

const UserDetail: React.FC = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { notification } = App.useApp();
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserDTO | null>(null);

  const { data } = useQuery({
    queryKey: [queryKeys.userInfo, userId],
    queryFn: () => userAPI.getUserById(Number(userId)),
    enabled: !!userId,
    select: res => res.data
  });

  const onSuccess = () => {
    notification.success({ message: 'User Deleted Successfully' });
    navigate(`/${appRoutes.user}`);
  };

  const { mutate, isPending: isDeletePending } = useMutation({
    mutationFn: () => userAPI.deleteUser(Number(userId)),
    onSuccess,
    onError: () => notification.error({ message: 'User deletion failed' })
  });

  const handleEditClick = useCallback((user: UserDTO) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  }, []);

  return (
    <Container>
      <Header>
        {data?.name}
        <MdEdit size={24} cursor='pointer' onClick={() => handleEditClick(data!)} />
      </Header>
      <ContentWrapper>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>Email</Title>
            <FieldValue>{data?.email || '-'}</FieldValue>
          </Col>
          <Col xs={24} sm={10} offset={2}>
            <Title>Title</Title>
            <FieldValue>{data?.title || '-'}</FieldValue>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>Active Status</Title>
            <FieldValue>{data?.isActive}</FieldValue>
          </Col>
        </Row>
      </ContentWrapper>
      <Popconfirm
        title='Are you sure to delete this user?'
        onConfirm={() => mutate()}
        onCancel={() => null}
        okText='Yes'
        cancelText='No'
      >
        <StyledButton
          danger
          type='primary'
          loading={isDeletePending}
          disabled={isDeletePending}
          icon={<RxCrossCircled />}
          iconPosition='end'
        >
          Delete User
        </StyledButton>
      </Popconfirm>
      {isEditModalOpen && (
        <AddUser
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={true}
          selectedUser={selectedUser!}
        />
      )}
    </Container>
  );
};

export default UserDetail;
