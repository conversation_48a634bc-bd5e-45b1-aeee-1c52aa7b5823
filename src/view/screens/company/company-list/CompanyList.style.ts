import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Input } from "antd";
import styled from "styled-components";
import { themeTokens } from "src/theme/tokens";

export  const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  padding: 48px 0;
`;

export const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

export const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

export const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 24px;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  background: ${themeTokens.pageBg};
  padding: 20px 0;
`;

export const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

export const StyledCard = styled(Card)`
  width: 100%;
`;

export const StyledAvatar = styled(Avatar)`
  background: #ffffff;
  color: black;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
`;

export const CompanyTitle = styled.div`
  font-weight: 400;
  font-size: 24px;
  line-height: 28px;
`;

export const CompanyName = styled.span`
  font-weight: 700;
`;

export const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

export const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;