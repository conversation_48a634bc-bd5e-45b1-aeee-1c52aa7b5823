import { useMutation, useQuery } from '@tanstack/react-query';
import { App, Col, Popconfirm, Row } from 'antd';
import { useCallback, useState } from 'react';
import { MdEdit } from 'react-icons/md';
import { RxCrossCircled } from 'react-icons/rx';
import { useNavigate, useParams } from 'react-router-dom';
import { CompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import AddCompany from '../company-list/add-company/AddCompany';
import { appRoutes, queryKeys } from 'src/modules/utils/constant';
import { Container, ContentWrapper, FieldValue, Header, StyledButton, Title } from './CompanyDetails.style';


const CompanyDetail: React.FC = () => {
  const { companyId } = useParams();
  const navigate = useNavigate();
  const { notification } = App.useApp();
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanyDTO | null>(null);

  const { data } = useQuery({
    queryKey: [queryKeys.companyInfo, companyId],
    queryFn: () => companyAPI.getCompanyById(Number(companyId)),
    enabled: !!companyId,
    select: res => res.data
  });

  const onSuccess = () => {
    notification.success({ message: 'Company deleted Successfully' });
    navigate(`/${appRoutes.company}`);
  };

  const { mutate, isPending: isDeletePending } = useMutation({
    mutationFn: () => companyAPI.deleteCompany(Number(companyId)),
    onSuccess,
    onError: () => notification.error({ message: 'Company deletion failed' })
  });

  const handleEditClick = useCallback((company: CompanyDTO) => {
    setSelectedCompany(company);
    setIsEditModalOpen(true);
  }, []);

  return (
    <Container>
      <Header>
        {data?.name}
        <MdEdit size={24} cursor='pointer' onClick={() => handleEditClick(data!)} />
      </Header>
      <ContentWrapper>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>SubscriptionStatus</Title>
            <FieldValue>{data?.subscriptionStatus || '-'}</FieldValue>
          </Col>
          <Col xs={24} sm={10} offset={2}>
            <Title>Industry</Title>
            <FieldValue>{data?.industry || '-'}</FieldValue>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>SubscriptionPlans</Title>
            {data?.subscriptionPlans
              ? data?.subscriptionPlans?.map(plan => <FieldValue>{plan?.status}</FieldValue>)
              : '-'}
          </Col>
        </Row>
      </ContentWrapper>
      <Popconfirm
        title='Are you sure to delete this company?'
        onConfirm={() => mutate()}
        onCancel={() => null}
        okText='Yes'
        cancelText='No'
      >
        <StyledButton
          danger
          type='primary'
          loading={isDeletePending}
          disabled={isDeletePending}
          icon={<RxCrossCircled />}
          iconPosition='end'
        >
          Delete Company
        </StyledButton>
      </Popconfirm>
      {isEditModalOpen && (
        <AddCompany
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={isEditModalOpen}
          selectedCompany={selectedCompany!}
        />
      )}
    </Container>
  );
};

export default CompanyDetail;
