import { Button } from "antd";
import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
`;

export const Header = styled.div`
  font-weight: 500;
  font-size: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
`;

export const Title = styled.p`
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 4px;
`;

export const FieldValue = styled.p`
  font-weight: 400;
  font-size: 18px;
  word-wrap: break-word;
`;

export const ContentWrapper = styled.div`
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  gap: 25px;
`;

export const StyledButton = styled(Button)`
  position: absolute;
  right: 48px;
  bottom: 48px;
`;
