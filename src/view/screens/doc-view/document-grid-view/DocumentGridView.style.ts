import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div<{ isThumbnailsOpen: boolean }>`
  background: ${themeTokens.textLight};
  padding: ${({ isThumbnailsOpen }) => (isThumbnailsOpen ? '20px' : '2px 20px')};
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
`;

export const StyledImageContainer = styled.div`
  display: flex !important;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  gap: 2px;
  padding: 10px;
  text-align: center;
  width: 150px;
  max-width: 150px;
`;

export const StyledImage = styled.img<{ isActive: boolean }>`
  height: 100%;
  width: fit-content;
  object-fit: contain;
  max-height: 70px;
  border: 1px solid ${({ isActive }) => (isActive ? themeTokens.primaryColor : '#4e4e4e')};
`;

export const Headingwrapper = styled.span`
  font-size: 18px;
  font-weight: 500;
  font-family: inter;
`;

export const ThumbnailsWrapper = styled.div`
  max-height: 100%;
  overflow: auto;
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;
