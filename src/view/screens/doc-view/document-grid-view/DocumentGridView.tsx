import { Flex } from 'antd';
import React from 'react';
import { DocumentDTOInputDocumentTypeEnum } from 'src/api';
import {
  Headingwrapper,
  Container,
  GridContainer,
  StyledImageContainer,
  StyledImage,
  ThumbnailsWrapper
} from './DocumentGridView.style';
import { ThumbnailInfo } from '../../../../modules/utils/thumbnailInfo';
import useScopesStore from '../../scopes/store/useScopesStore';

interface DocumentGridViewProps {
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
}
enum DocumentTabName {
  drawings = 'Drawings',
  specs = 'Specifications'
}
const DocumentGridView: React.FC<DocumentGridViewProps> = ({ activeTab, setActiveTab }) => {
  const {
    selectedThumbnailInfo,
    setSelectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails
  } = useScopesStore();

  const OnClickThumbnailHandler = (file: ThumbnailInfo) => {
    setSelectedThumbnailInfo(file);
    if (
      activeTab === DocumentTabName.drawings &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
    ) {
      setActiveTab(DocumentTabName.specs);
    } else if (
      activeTab === DocumentTabName.specs &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
    ) {
      setActiveTab(DocumentTabName.drawings);
    }
  };

  return (
    <Container isThumbnailsOpen={true}>
      <Flex vertical gap={30} style={{ height: '100%' }}>
        <ThumbnailsWrapper>
          {activeTab === DocumentTabName.drawings ? (
            <>
              {drawingsThumbnails.length > 0 && <Headingwrapper>Drawings</Headingwrapper>}
              <GridContainer>
                {drawingsThumbnails?.map(file => {
                  const isSheetActive =
                    file?.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId;
                  return (
                    <StyledImageContainer key={file?.sheet?.sheetId}>
                      <StyledImage
                        src={file.thumbnail}
                        alt={file?.sheet?.title}
                        onClick={() => !isSheetActive && OnClickThumbnailHandler(file)}
                        isActive={isSheetActive}
                      />
                      <div>{file?.sheet?.sheetNumber}</div>
                      <div>{file?.sheet?.title}</div>
                    </StyledImageContainer>
                  );
                })}
              </GridContainer>
            </>
          ) : (
            <>
              {specificationsThumbnails.length > 0 && (
                <Headingwrapper>Specifications</Headingwrapper>
              )}
              <GridContainer>
                {specificationsThumbnails?.map(file => {
                  const isSpecsActive =
                    file?.specification?.specificationId ===
                    selectedThumbnailInfo?.specification?.specificationId;
                  return (
                    <StyledImageContainer key={file?.specification?.specificationId}>
                      <StyledImage
                        src={file.thumbnail}
                        alt={file?.specification?.specificationTitle}
                        onClick={() => !isSpecsActive && OnClickThumbnailHandler(file)}
                        isActive={isSpecsActive}
                      />
                      <div>{file?.specification?.specificationCode}</div>
                      <div>{file?.specification?.specificationTitle}</div>
                    </StyledImageContainer>
                  );
                })}
              </GridContainer>
            </>
          )}
        </ThumbnailsWrapper>
      </Flex>
    </Container>
  );
};

export default DocumentGridView;
