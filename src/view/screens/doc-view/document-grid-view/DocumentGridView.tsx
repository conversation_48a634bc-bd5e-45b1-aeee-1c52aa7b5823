import { <PERSON>lex, Tooltip } from 'antd';
import React from 'react';
import { DocumentDTOInputDocumentTypeEnum } from 'src/api';
import {
  Headingwrapper,
  Container,
  GridContainer,
  StyledImageContainer,
  StyledImage,
  Ellipsis,
  ThumbnailsWrapper
} from './DocumentGridView.style';
import { ThumbnailInfo } from '../../../../modules/utils/thumbnailInfo';
import useScopesStore from '../../scopes/store/useScopesStore';

interface DocumentGridViewProps {
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
}
enum DocumentTabName {
  drawings = 'Drawings',
  specs = 'Specifications'
}

const DocumentGridView: React.FC<DocumentGridViewProps> = ({ activeTab, setActiveTab }) => {
  const {
    selectedThumbnailInfo,
    setSelectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails
  } = useScopesStore();

  const OnClickThumbnailHandler = (file: ThumbnailInfo) => {
    setSelectedThumbnailInfo(file);
    if (
      activeTab === DocumentTabName.drawings &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
    ) {
      setActiveTab(DocumentTabName.specs);
    } else if (
      activeTab === DocumentTabName.specs &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
    ) {
      setActiveTab(DocumentTabName.drawings);
    }
  };

  return (
    <Container isThumbnailsOpen={true}>
      <Flex vertical gap={30} style={{ height: '100%' }}>
        <ThumbnailsWrapper>
          {activeTab === DocumentTabName.drawings ? (
            <>
              {drawingsThumbnails.length > 0 && <Headingwrapper>Drawings</Headingwrapper>}
              <GridContainer>
                {drawingsThumbnails?.map(file => {
                  const isSheetActive =
                    file?.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId;
                  return (
                    <StyledImageContainer key={file?.sheet?.sheetId}>
                      <StyledImage
                        src={file.thumbnail}
                        alt={file?.sheet?.title}
                        onClick={() => !isSheetActive && OnClickThumbnailHandler(file)}
                        isActive={isSheetActive}
                      />
                      <Ellipsis>
                        <Tooltip title={`${file?.sheet?.sheetNumber} - ${file?.sheet?.title}`}>
                          {file?.sheet?.sheetNumber} - {file?.sheet?.title}
                        </Tooltip>
                      </Ellipsis>
                    </StyledImageContainer>
                  );
                })}
              </GridContainer>
            </>
          ) : (
            <>
              {specificationsThumbnails.length > 0 && (
                <Headingwrapper>Specifications</Headingwrapper>
              )}
              <GridContainer>
                {specificationsThumbnails?.map(file => {
                  const isSpecsActive =
                    file?.specification?.specificationId ===
                    selectedThumbnailInfo?.specification?.specificationId;
                  return (
                    <StyledImageContainer key={file?.specification?.specificationId}>
                      <StyledImage
                        src={file.thumbnail}
                        alt={file?.specification?.specificationTitle}
                        onClick={() => !isSpecsActive && OnClickThumbnailHandler(file)}
                        isActive={isSpecsActive}
                      />
                      <Ellipsis>
                        <Tooltip
                          title={`${file?.specification?.specificationCode} - ${file?.specification?.specificationTitle}`}
                        >
                          {file?.specification?.specificationCode} -{' '}
                          {file?.specification?.specificationTitle}
                        </Tooltip>
                      </Ellipsis>
                    </StyledImageContainer>
                  );
                })}
              </GridContainer>
            </>
          )}
        </ThumbnailsWrapper>
      </Flex>
    </Container>
  );
};

export default DocumentGridView;
