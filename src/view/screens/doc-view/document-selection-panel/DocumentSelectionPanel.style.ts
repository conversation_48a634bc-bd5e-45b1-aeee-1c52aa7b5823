import { Button } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

export const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #e8e8e8;
`;

export const TabButton = styled.div<{ active: boolean }>`
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;
  font-weight: 500;
  background: ${({ active }) => (active ? themeTokens.menuItemSelectedBg : 'transparent')};
  color: ${({ active }) => (active ? themeTokens.textLight : themeTokens.textDark)};
  border-bottom: ${({ active }) => (active ? `2px solid ${themeTokens.primaryColor}` : 'none')};

  &:hover {
    background: ${({ active }) => (active ? themeTokens.menuItemSelectedBg : themeTokens.pageBg)};
  }
`;

export const ControlsContainer = styled.div`
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
`;

export const ViewToggleContainer = styled.div`
  display: flex;
  gap: 8px;
`;

export const ViewToggleButton = styled(Button)<{ active: boolean }>`
  background: ${({ active }) => (active ? themeTokens.primaryColor : 'transparent')};
  color: ${({ active }) => (active ? themeTokens.textLight : themeTokens.textDark)};
  border-color: ${({ active }) => (active ? themeTokens.primaryColor : '#d9d9d9')};

  &:hover {
    background: ${({ active }) => (active ? themeTokens.primaryColor : themeTokens.pageBg)};
    color: ${({ active }) => (active ? themeTokens.textLight : themeTokens.textDark)};
    border-color: ${({ active }) => (active ? themeTokens.primaryColor : '#d9d9d9')};
  }
`;

export const ContentContainer = styled.div`
  flex: 1;
  overflow: auto;
  padding: 16px;
`;
