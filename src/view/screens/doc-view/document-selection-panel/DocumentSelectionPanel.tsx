import { SearchOutlined, AppstoreOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { Input, Select, Flex } from 'antd';
import React, { useState } from 'react';
import {
  Tab<PERSON>utton,
  PanelContainer,
  TabContainer,
  ControlsContainer,
  ViewToggleContainer,
  ViewToggleButton,
  ContentContainer
} from './DocumentSelectionPanel.style';
import { DocumentTabName } from '../../scopes/document-view/DocumentThumbnailsView';
import DocumentGridView from '../document-grid-view/DocumentGridView';

const { Search } = Input;
const { Option } = Select;

interface DocumentSelectionPanelProps {
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
}

const DocumentSelectionPanel: React.FC<DocumentSelectionPanelProps> = ({
  activeTab,
  setActiveTab
}) => {
  const [searchText, setSearchText] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filter, setFilter] = useState('all');

  const filterOptions = [
    { value: 'all', label: 'All Sheets' },
    { value: 'with-scopes', label: 'Sheets with Scopes' },
    { value: 'without-scopes', label: 'Sheets without Scopes' },
    { value: 'raster', label: 'Raster Sheets (scanned)' },
    { value: 'vector', label: 'Vector Sheets (not scanned)' }
  ];

  return (
    <PanelContainer>
      <TabContainer>
        <TabButton
          active={activeTab === DocumentTabName.drawings}
          onClick={() => setActiveTab(DocumentTabName.drawings)}
        >
          {DocumentTabName.drawings}
        </TabButton>
        <TabButton
          active={activeTab === DocumentTabName.specs}
          onClick={() => setActiveTab(DocumentTabName.specs)}
        >
          {DocumentTabName.specs}
        </TabButton>
      </TabContainer>

      <ControlsContainer>
        <Search
          placeholder='Search documents...'
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
        />

        <Flex justify='space-between' align='center' style={{ marginTop: 12 }}>
          <Select value={filter} onChange={setFilter} style={{ width: 180 }} size='small'>
            {filterOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>

          <ViewToggleContainer>
            <ViewToggleButton
              size='small'
              icon={<AppstoreOutlined />}
              active={viewMode === 'grid'}
              onClick={() => setViewMode('grid')}
            />
            <ViewToggleButton
              size='small'
              icon={<UnorderedListOutlined />}
              active={viewMode === 'list'}
              onClick={() => setViewMode('list')}
            />
          </ViewToggleContainer>
        </Flex>
      </ControlsContainer>

      <ContentContainer>
        {viewMode === 'grid' ? (
          <DocumentGridView activeTab={activeTab} setActiveTab={setActiveTab} />
        ) : (
          <div>Document list view</div>
        )}
      </ContentContainer>
    </PanelContainer>
  );
};

export default DocumentSelectionPanel;
