import React, { useState, useRef, useMemo } from 'react';
import {
  PdfViewerComponent,
  Inject,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  Print,
  TextSelection,
  Annotation,
  TextSearch,
  FormFields,
  FormDesigner,
  PageOrganizer,
  ExtractTextOption,
  AnnotationDataFormat
} from '@syncfusion/ej2-react-pdfviewer';
import { Button, Form, Input, Modal, App } from 'antd';
import { bidItemAPI, noteAPI } from 'src/api/apiClient';
import { useParams } from 'react-router';
import useGlobalStore from 'src/store/useGlobalStore';
import type { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import { DocumentDTOInputDocumentTypeEnum, NoteDTO } from 'src/api';
import { queryKeys } from 'src/modules/utils/constant';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

interface SpecModalViewerProps {
  thumbnail: ThumbnailInfo;
  pdfUrl: string;
  targetPage?: number;
}

enum CustomToolbarItems {
  add = 'add',
  save = 'save'
}

const SpecModalViewer: React.FC<SpecModalViewerProps> = ({ thumbnail, pdfUrl, targetPage }) => {
  const viewer = useRef<PdfViewerComponent>(null);
  const { selectedProjectId } = useGlobalStore();
  const { scopeId } = useParams();
  const { notification } = App.useApp();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedTextInfo, setSelectedTextInfo] = useState<any>(null);

  const resolvedPdfUrl = useMemo(() => {
    if (pdfUrl && pdfUrl.length > 0) return pdfUrl;
    return thumbnail.document?.presignedUrl || '';
  }, [pdfUrl, thumbnail.document]);

  /* ---------------- Notes (annotation) handling ---------------- */
  const { data: notesData } = useQuery({
    queryKey: [queryKeys.getNotes, scopeId, thumbnail.document?.id],
    queryFn: () =>
      noteAPI.getNotes(
        undefined,
        Number(selectedProjectId),
        thumbnail.document?.id,
        Number(scopeId)
      ),
    select: res => res.data.content,
    enabled: !!(selectedProjectId && thumbnail.document?.id && scopeId)
  });

  const { mutateAsync: createNote } = useMutation({
    mutationFn: (data: NoteDTO) => noteAPI.createNote(data),
    onSuccess: () => notification.success({ message: 'Annotations saved' })
  });

  const { mutateAsync: updateNote } = useMutation({
    mutationFn: (p: { id: number; data: NoteDTO }) => noteAPI.updateNote(p.id, p.data),
    onSuccess: () => notification.success({ message: 'Annotations updated' })
  });

  const saveAnnotations = async () => {
    const annotations = await viewer.current?.exportAnnotationsAsObject();
    if (!annotations) return;
    const data: NoteDTO = {
      projectId: Number(selectedProjectId),
      scopeId: scopeId ? Number(scopeId) : undefined,
      documentId: thumbnail.document?.id,
      annotationData: JSON.stringify(annotations)
    };
    if (notesData && notesData.length > 0 && notesData[0].id) {
      await updateNote({ id: notesData[0].id as number, data });
    } else {
      await createNote(data);
    }
  };

  /* ---------------- Add Bid Item handling ---------------- */
  const AddBidItemModal: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
    const [form] = Form.useForm();
    const { mutateAsync: createBidItem, isPending } = useMutation({
      mutationFn: (data: any) => bidItemAPI.createBidItem(data),
      onSuccess: () => {
        notification.success({ message: 'Bid Item created' });
        queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] });
        onClose();
      }
    });

    const handleSave = async () => {
      try {
        const values = await form.validateFields();
        const bidItem = {
          name: values.name,
          bidItemSheets: [],
          bidItemSpecificationDTOList: thumbnail.specification ? [thumbnail.specification] : [],
          projectId: Number(selectedProjectId),
          scopeId: scopeId ? Number(scopeId) : undefined
        } as any;
        await createBidItem(bidItem);
      } catch (err) {
        console.error(err);
      }
    };

    return (
      <Modal
        open={open}
        title='Add Bid Item'
        onCancel={onClose}
        onOk={handleSave}
        confirmLoading={isPending}
        okText='Add'
      >
        <Form form={form} layout='vertical'>
          <Form.Item label='Bid Item Name' name='name' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  const handleToolbarClick = (args: any) => {
    switch (args.item.id) {
      case CustomToolbarItems.add:
        if (!selectedTextInfo) {
          notification.warning({ message: 'Select text before adding bid item' });
          return;
        }
        setIsAddModalOpen(true);
        break;
      case CustomToolbarItems.save:
        saveAnnotations();
        break;
      default:
        break;
    }
  };

  return (
    <>
      <PdfViewerComponent
        ref={viewer}
        id='spec-modal-viewer'
        documentPath={resolvedPdfUrl}
        resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
        width='100%'
        height='100%'
        documentLoad={() => {
          if (targetPage && viewer.current) {
            viewer.current.navigation.goToPage(targetPage as number);
          }
        }}
        isExtractText
        extractTextOption={ExtractTextOption.TextAndBounds}
        textSelectionEnd={args => setSelectedTextInfo(args)}
        toolbarClick={handleToolbarClick}
        toolbarSettings={{
          showTooltip: true,
          toolbarItems: [
            'OpenOption',
            'PageNavigationTool',
            'MagnificationTool',
            'PanTool',
            'SelectionTool',
            'SearchOption',
            'AnnotationEditTool',
            'PrintOption',
            'DownloadOption',
            'UndoRedoTool',
            {
              tooltipText: 'Add Bid Item',
              id: CustomToolbarItems.add,
              align: 'right',
              text: '+',
              type: 'Button',
              cssClass: 'e-pv-add-button'
            },
            {
              tooltipText: 'Save Changes',
              id: CustomToolbarItems.save,
              align: 'right',
              text: 'Save',
              type: 'Button',
              prefixIcon: 'e-save'
            }
          ]
        }}
      >
        <Inject
          services={[
            Toolbar,
            Magnification,
            Navigation,
            LinkAnnotation,
            Print,
            TextSelection,
            Annotation,
            TextSearch,
            FormFields,
            FormDesigner,
            PageOrganizer
          ]}
        />
      </PdfViewerComponent>
      <AddBidItemModal open={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />
    </>
  );
};

export default SpecModalViewer;
