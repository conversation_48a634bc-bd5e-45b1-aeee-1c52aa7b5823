import React from 'react';
import { Modal } from 'antd';
import type { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import SpecModalViewer from './SpecModalViewer';
import { getSpecDocS3Url } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';

interface SpecPdfModalProps {
  visible: boolean;
  onClose: () => void;
  specificationThumbnail: ThumbnailInfo | null;
}

const SpecPdfModal: React.FC<SpecPdfModalProps> = ({
  visible,
  onClose,
  specificationThumbnail
}) => {
  const pdfViewerRef = React.useRef<any>(null);
  const { currentUser, selectedProjectId } = useGlobalStore();
  const targetPage = specificationThumbnail?.specification?.pageNumber;
  const specCode = specificationThumbnail?.specification?.specificationCode;
  const shouldSearchRef = React.useRef<boolean>(true);

  const pdfUrl = React.useMemo(() => {
    if (specificationThumbnail?.document?.presignedUrl)
      return specificationThumbnail.document.presignedUrl;
    if (specificationThumbnail?.specification && currentUser?.companyId && selectedProjectId) {
      const modifiedCode = specificationThumbnail.specification.specificationCode?.replace(
        / /g,
        '+'
      );
      return getSpecDocS3Url(
        currentUser.companyId,
        Number(selectedProjectId),
        specificationThumbnail.specification.documentId,
        modifiedCode
      );
    }
    return null;
  }, [specificationThumbnail, currentUser, selectedProjectId]);

  const handlePdfViewerLoad = () => {
    if (pdfViewerRef.current && specificationThumbnail?.specification?.specificationCode) {
      if (specificationThumbnail?.specification?.pageNumber) {
        pdfViewerRef.current.navigation.goToPage(specificationThumbnail.specification.pageNumber);
        setTimeout(() => {
          // pdfViewerRef.current.textSearchModule.searchText(
          //   specificationThumbnail.specification?.specificationCode,
          //   false,
          //   false
          // );
        }, 500); // Delay to ensure page navigation completes
      } else {
        // pdfViewerRef.current.textSearchModule.searchText(
        //   specificationThumbnail.specification?.specificationCode,
        //   false,
        //   false
        // );
      }
    }
  };

  const handlePageRenderComplete = (args: any) => {
    if (
      shouldSearchRef.current &&
      typeof targetPage === 'number' &&
      args?.pageNumber === targetPage &&
      pdfViewerRef.current &&
      specCode
    ) {
      // pdfViewerRef.current.textSearchModule.searchText(specCode, false, false);
      shouldSearchRef.current = false;
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width='80vw'
      style={{ top: 24 }}
      bodyStyle={{ height: '80vh', padding: 0 }}
      destroyOnClose
      title={specificationThumbnail?.specification?.specificationCode || 'Specification Reference'}
    >
      {pdfUrl ? (
        <SpecModalViewer
          thumbnail={specificationThumbnail as any}
          pdfUrl={pdfUrl}
          targetPage={specificationThumbnail?.specification?.pageNumber}
        />
      ) : (
        <div style={{ padding: 32, textAlign: 'center' }}>
          No document available for this specification.
        </div>
      )}
    </Modal>
  );
};

export default SpecPdfModal;
