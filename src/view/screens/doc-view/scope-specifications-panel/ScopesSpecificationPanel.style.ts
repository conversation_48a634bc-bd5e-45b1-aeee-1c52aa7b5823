import styled from 'styled-components';
import { Select, Button } from 'antd';

export const PanelWrapper = styled.div`
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const Ribbon = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const ScopesListWrapper = styled.div`
  flex: 1;
  padding: 16px 0;
  max-height: 60vh;
  min-height: 200px;
  overflow-y: auto;
  width: 100%;
  overflow-x: hidden;
  /* Hide scrollbars but allow scrolling */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
  }
`;

export const ExpandCollapseControls = styled.div`
  display: flex;
  gap: 8px;
`;

export const BidItemsTableWrapper = styled.div`
  .ant-table-thead {
    display: none;
  }
`;

export const RibbonColumn = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

export const FlexRow = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
`;

export const FlexRowNoMargin = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 0;
`;

export const FixedWidthInput = styled.input`
  width: 180px;
`;

export const TruncatedSpan = styled.span`
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
`;

export const CenteredSpin = styled.div`
  margin: 32px auto;
  display: block;
`;

export const GrayText = styled.span`
  color: #888;
`;

export const DebugTableBorder = styled.div`
  border: 2px solid red;
`;

export const InputWrapper = styled.div`
  width: 180px;
`;

export const DisciplineSelect = styled(Select)`
  width: 220px;
`;

export const AddScopeButtonContainer = styled.div`
  margin-right: 12px;
`;

export const LinkButton = styled(Button)`
  font-weight: 500;
  padding: 0;
`; 
