import styled from 'styled-components';

export const PanelWrapper = styled.div`
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const Ribbon = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const ScopesListWrapper = styled.div`
  flex: 1;
  padding: 16px 0;
  max-height: 60vh;
  min-height: 200px;
  overflow-y: auto;
`;

export const ExpandCollapseControls = styled.div`
  display: flex;
  gap: 8px;
`;

export const BidItemsTableWrapper = styled.div`
  .ant-table-thead {
    display: none;
  }
`; 