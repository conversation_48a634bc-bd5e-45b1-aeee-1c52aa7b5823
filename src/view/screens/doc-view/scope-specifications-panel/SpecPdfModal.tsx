import React from 'react';
import { Modal } from 'antd';
import type { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import {
  PdfViewerComponent,
  Inject,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  Print,
  TextSelection,
  Annotation,
  TextSearch,
  FormFields,
  FormDesigner,
  PageOrganizer
} from '@syncfusion/ej2-react-pdfviewer';

interface SpecPdfModalProps {
  visible: boolean;
  onClose: () => void;
  specificationThumbnail: ThumbnailInfo | null;
}

const SpecPdfModal: React.FC<SpecPdfModalProps> = ({
  visible,
  onClose,
  specificationThumbnail
}) => {
  const pdfViewerRef = React.useRef<any>(null);
  const targetPage = specificationThumbnail?.specification?.pageNumber;
  const specCode = specificationThumbnail?.specification?.specificationCode;
  const shouldSearchRef = React.useRef<boolean>(true);
  const handlePdfViewerLoad = () => {
    if (pdfViewerRef.current && specificationThumbnail?.specification?.specificationCode) {
      if (specificationThumbnail?.specification?.pageNumber) {
        pdfViewerRef.current.navigation.goToPage(specificationThumbnail.specification.pageNumber);
        setTimeout(() => {
          // pdfViewerRef.current.textSearchModule.searchText(
          //   specificationThumbnail.specification?.specificationCode,
          //   false,
          //   false
          // );
        }, 500); // Delay to ensure page navigation completes
      } else {
        // pdfViewerRef.current.textSearchModule.searchText(
        //   specificationThumbnail.specification?.specificationCode,
        //   false,
        //   false
        // );
      }
    }
  };

  const handlePageRenderComplete = (args: any) => {
    if (
      shouldSearchRef.current &&
      typeof targetPage === 'number' &&
      args?.pageNumber === targetPage &&
      pdfViewerRef.current &&
      specCode
    ) {
      // pdfViewerRef.current.textSearchModule.searchText(specCode, false, false);
      shouldSearchRef.current = false;
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width='80vw'
      style={{ top: 24 }}
      bodyStyle={{ height: '80vh', padding: 0 }}
      destroyOnClose
      title={specificationThumbnail?.specification?.specificationCode || 'Specification Reference'}
    >
      {specificationThumbnail?.document?.presignedUrl ? (
        <PdfViewerComponent
          ref={pdfViewerRef}
          id='syncfusion-pdf-viewer-spec-modal'
          documentPath={specificationThumbnail.document.presignedUrl}
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          width='100%'
          height='100%'
          style={{ flex: 3 }}
          documentLoad={handlePdfViewerLoad}
        >
          <Inject
            services={[
              Toolbar,
              Magnification,
              Navigation,
              LinkAnnotation,
              Print,
              TextSelection,
              Annotation,
              TextSearch,
              FormFields,
              FormDesigner,
              PageOrganizer
            ]}
          />
        </PdfViewerComponent>
      ) : (
        <div style={{ padding: 32, textAlign: 'center' }}>
          No document available for this specification.
        </div>
      )}
    </Modal>
  );
};

export default SpecPdfModal;
