import { SearchOutlined, PlusSquareOutlined, MinusSquareOutlined } from '@ant-design/icons';
import { useQuery, useQueries } from '@tanstack/react-query';
import { Tabs, Button, Input, Collapse, Tooltip, Spin, Table, Select } from 'antd';
import React, { useState, useMemo, useEffect } from 'react';
import { scopeAPI, bidItemAPI, projectAPI } from 'src/api/apiClient';
import type { BidItemDTO } from 'src/api/models/bid-item-dto';
import type { BidItemSpecificationDTO } from 'src/api/models/bid-item-specification-dto';
import type { PageBidItemDTO } from 'src/api/models/page-bid-item-dto';
import { queryKeys, disciplines } from 'src/modules/utils/constant';
import type { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import useScopesStore from 'src/view/screens/scopes/store/useScopesStore';
import {
  PanelWrapper,
  Ribbon,
  ScopesListWrapper,
  ExpandCollapseControls,
  BidItemsTableWrapper,
  RibbonColumn,
  FlexRow,
  FlexRowNoMargin,
  FixedWidthInput,
  TruncatedSpan,
  CenteredSpin,
  GrayText,
  InputWrapper,
  DisciplineSelect,
  AddScopeButtonContainer,
  LinkButton
} from './ScopesSpecificationPanel.style';
import SpecPdfModal from './spec-model/SpecModal';
import { getThumbnailS3Url } from 'src/modules/utils/thumbnailInfo';
import { useParams, useNavigate } from 'react-router-dom';
import { FaPlus } from 'react-icons/fa6';
import HasProjectPermission from 'src/modules/guards/HasProjectPermission';
import { UserProjectPermission } from 'src/modules/utils/permissions';
import AddScopeModal from 'src/view/screens/scopes/scope-sub-nav/add-scope/AddScope';

const { TabPane } = Tabs;
const { Panel } = Collapse;

const ScopesSpecificationPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('scopes');
  const [expandedScopes, setExpandedScopes] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [selectedDiscipline, setSelectedDiscipline] = useState<string>('All');
  const [isSortAscending] = useState<boolean>(true);
  const { scopeId } = useParams();
  const navigate = useNavigate();
  const { selectedProjectId, selectedVersion, currentUser } = useGlobalStore();
  const {
    setSelectedThumbnailInfo,
    specificationsThumbnails,
    drawingsThumbnails,
    selectedThumbnailInfo,
    setSpecificationsThumbnails,
    setIsDocumentView,
    setDrawingsThumbnails
  } = useScopesStore();

  // Modal state for spec PDF
  const [specModalVisible, setSpecModalVisible] = useState(false);
  const [selectedSpecThumbnail, setSelectedSpecThumbnail] = useState<ThumbnailInfo | null>(null);

  const { data: scopesData, isLoading: isScopesLoading } = useQuery({
    queryKey: [
      queryKeys.allScopes,
      selectedProjectId,
      selectedThumbnailInfo?.sheet,
      selectedVersion,
      selectedThumbnailInfo?.specification,
      selectedDiscipline,
      searchValue,
      isSortAscending
    ],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        searchValue || undefined,
        selectedDiscipline !== 'All' ? selectedDiscipline : undefined,
        String(selectedVersion?.version),
        0,
        1000,
        isSortAscending ? ['division,asc'] : ['division,desc'],
        selectedThumbnailInfo?.sheet?.sheetId,
        selectedThumbnailInfo?.specification?.specificationId
      ),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  // Filter scopes by search value and show all scopes (no restriction by sheet or scopeId)
  const filteredScopes = useMemo(
    () =>
      scopesData?.content?.filter(
        scope =>
          scope.id !== undefined &&
          (scope.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
            scope.description?.toLowerCase().includes(searchValue.toLowerCase()))
      ) || [],
    [scopesData, searchValue]
  );

  // Fetch bid items for expanded scopes only
  const bidItemsQueries = useQueries({
    queries: expandedScopes.map(scopeId => ({
      queryKey: ['bidItems', scopeId],
      queryFn: () =>
        bidItemAPI.getBidItemsByScopeId(Number(scopeId), 0, 1000).then(res => res.data),
      enabled: !!scopeId
    }))
  });

  // Helper to get bid items for a scope
  function getBidItemsForScope(scopeId: number | string): {
    data?: PageBidItemDTO;
    isLoading?: boolean;
  } {
    const idx = expandedScopes.indexOf(String(scopeId));
    return idx !== -1
      ? (bidItemsQueries[idx] as { data?: PageBidItemDTO; isLoading?: boolean })
      : {};
  }

  const handleExpandAll = () => {
    if (filteredScopes.length > 0) {
      setExpandedScopes(filteredScopes.map(scope => String(scope.id!)));
    }
  };
  const handleCollapseAll = () => setExpandedScopes([]);

  // Table columns for bid items
  const bidItemColumns = [
    {
      title: 'Bid Item Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => {
        const truncated = text && text.length > 20 ? text.slice(0, 20) + '...' : text;
        return (
          <Tooltip title={text}>
            <LinkButton
              type='link'
              onClick={() => {
                const [bidItemId, rowSheetId] = record.key.split('-');
                let foundThumb: ThumbnailInfo | undefined;
                let targetScopeId: string | undefined;
                outer: for (const loopScopeId of expandedScopes) {
                  const { data: bidItemsData } = getBidItemsForScope(loopScopeId);
                  const bidItems = bidItemsData?.content || [];
                  const bidItem = bidItems.find((b: any) => String(b.id) === bidItemId);
                  if (bidItem && bidItem.bidItemSheets && bidItem.bidItemSheets.length > 0) {
                    // Prefer sheet with coordinates, else iterate to find first with existing thumbnail, else default to first
                    const candidateSheets = [
                      ...bidItem.bidItemSheets.filter((s: any) => s.coordinates),
                      ...bidItem.bidItemSheets.filter((s: any) => !s.coordinates)
                    ];
                    let sheet = bidItem.bidItemSheets.find(
                      (s: any) => String(s.sheetId) === rowSheetId
                    );
                    if (!sheet) {
                      sheet = candidateSheets[0];
                    }
                    foundThumb = drawingsThumbnails.find(
                      t => t.sheet && String(t.sheet.sheetId) === String(sheet.sheetId)
                    );
                    targetScopeId = loopScopeId;
                    if (!foundThumb) {
                      foundThumb = drawingsThumbnails.find(
                        t => t.sheet && String(t.sheet.sheetId) === String(sheet.sheetId)
                      );
                    }
                    break outer;
                  }
                }
                async function attachDocumentInfo(thumbnail: ThumbnailInfo | undefined) {
                  if (thumbnail && !thumbnail.document) {
                    try {
                      const res = await projectAPI.getProjectWithDocuments(
                        Number(selectedProjectId)
                      );
                      const docs = res.data.documents || [];
                      const doc = docs.find((d: any) => d.id === thumbnail.sheet?.documentId);
                      if (doc) {
                        thumbnail.document = doc;
                      }
                    } catch (e) {
                      console.error('Error fetching project documents', e);
                    }
                  }
                  return thumbnail;
                }
                attachDocumentInfo(foundThumb).then(finalThumb => {
                  if (finalThumb) {
                    setSelectedThumbnailInfo(finalThumb);
                    setIsDocumentView(true);
                    if (targetScopeId && targetScopeId !== scopeId) {
                      navigate(`../${targetScopeId}`);
                    }
                  }
                });
              }}
            >
              <TruncatedSpan>{truncated}</TruncatedSpan>
            </LinkButton>
          </Tooltip>
        );
      }
    },
    {
      title: 'Specification Code',
      dataIndex: 'specList',
      key: 'specificationCode',
      render: (specList: BidItemSpecificationDTO[] | undefined, record: any) => {
        if (!specList || specList.length === 0) return <span>-</span>;

        // Helper to open modal for a specific spec
        const openSpecThumbnail = (spec: BidItemSpecificationDTO) => {
          const specId = spec.specificationId;
          if (!specId) return;

          let thumb = specificationsThumbnails.find(
            t => t.specification && String(t.specification.specificationId) === String(specId)
          );

          // Fallback: generate thumbnail from spec data if not already present
          if (!thumb && spec && currentUser?.companyId) {
            const thumbnailUrl = getThumbnailS3Url(
              currentUser.companyId,
              Number(selectedProjectId),
              Number(spec.documentId),
              Number(spec.pageNumber) || 0
            );
            thumb = {
              document: null,
              thumbnail: thumbnailUrl,
              specification: spec
            } as ThumbnailInfo;
          }

          if (thumb) {
            setSelectedSpecThumbnail(thumb);
            setSpecModalVisible(true);
          }
        };

        return (
          <>
            {specList.map((spec, idx) => (
              <React.Fragment key={spec.specificationId || idx}>
                <LinkButton type='link' onClick={() => openSpecThumbnail(spec)}>
                  {spec.specificationCode}
                </LinkButton>
                {idx < specList.length - 1 && ', '}
              </React.Fragment>
            ))}
          </>
        );
      }
    }
  ];

  // Helper to flatten bid items for table
  function getBidItemTableData(bidItems: BidItemDTO[]): any[] {
    const rows: any[] = [];
    const currentSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    bidItems.forEach(bidItem => {
      if (bidItem.bidItemSheets && bidItem.bidItemSheets.length > 0) {
        const relevantSheets = currentSheetId
          ? bidItem.bidItemSheets.filter(
              (sheet: any) => String(sheet.sheetId) === String(currentSheetId)
            )
          : bidItem.bidItemSheets;

        if (currentSheetId && relevantSheets.length === 0) {
          return;
        }

        relevantSheets.forEach((sheet: any) => {
          const specList = bidItem.bidItemSpecificationDTOList || [];

          rows.push({
            key: `${bidItem.id}-${sheet.sheetId}`,
            name: bidItem.name,
            specList
          });
        });
      } else {
        if (!currentSheetId) {
          rows.push({
            key: `${bidItem.id}-noSheet`,
            name: bidItem.name,
            specList: bidItem.bidItemSpecificationDTOList || []
          });
        }
      }
    });
    return rows;
  }

  useEffect(() => {
    async function fetchAndSetSpecThumbnails() {
      if (!selectedProjectId || !currentUser?.companyId) return;
      try {
        const res = await bidItemAPI.getSheetsAndSpecs(
          Number(selectedProjectId),
          scopeId,
          scopeId,
          0,
          1000
        );
        const data = res.data;
        const specs = data?.bidItemSpecificationDTOList || [];
        const specificationThumbnails = specs.map((item: any) => {
          const thumbnail = getThumbnailS3Url(
            currentUser.companyId,
            Number(selectedProjectId),
            Number(item.documentId),
            Number(item.pageNumber) || 0
          );
          return {
            document: null,
            thumbnail,
            specification: item
          };
        });
        setSpecificationsThumbnails(specificationThumbnails);
        const sheets = data?.bidItemSheets || [];
        const drawingThumbnails = sheets.map((item: any) => {
          const thumbnail = getThumbnailS3Url(
            currentUser.companyId,
            Number(selectedProjectId),
            Number(item.documentId),
            Number(item.pageNumber) || 0
          );
          return {
            document: null,
            thumbnail,
            sheet: item
          } as ThumbnailInfo;
        });
        setDrawingsThumbnails(drawingThumbnails);
      } catch (err) {
        setSpecificationsThumbnails([]);
      }
    }
    fetchAndSetSpecThumbnails();
  }, [selectedProjectId, setSpecificationsThumbnails, currentUser, scopeId, setDrawingsThumbnails]);

  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);

  const addScopeButton = (
    <AddScopeButtonContainer>
      <HasProjectPermission requiredPermissions={[UserProjectPermission.CREATE_SCOPE]}>
        <Button
          type='primary'
          size='small'
          icon={<FaPlus size={12} />}
          onClick={() => setIsAddModalOpen(true)}
        />
      </HasProjectPermission>
    </AddScopeButtonContainer>
  );

  return (
    <PanelWrapper>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        tabBarExtraContent={{ left: addScopeButton }}
      >
        <TabPane tab='Relevant Scopes' key='scopes'>
          <RibbonColumn as={Ribbon}>
            <FlexRow>
              <DisciplineSelect
                showSearch
                value={selectedDiscipline}
                onChange={value => setSelectedDiscipline(String(value))}
                options={disciplines.map(d => ({ label: d.label, value: d.label }))}
              />
            </FlexRow>
            <FlexRowNoMargin>
              <InputWrapper>
                <Input
                  placeholder='Search scopes...'
                  prefix={<SearchOutlined />}
                  value={searchValue}
                  onChange={e => setSearchValue(e.target.value)}
                />
              </InputWrapper>
              <ExpandCollapseControls>
                <Tooltip title='Expand All'>
                  <Button onClick={handleExpandAll} size='middle' icon={<PlusSquareOutlined />} />
                </Tooltip>
                <Tooltip title='Collapse All'>
                  <Button
                    onClick={handleCollapseAll}
                    size='middle'
                    icon={<MinusSquareOutlined />}
                  />
                </Tooltip>
              </ExpandCollapseControls>
            </FlexRowNoMargin>
          </RibbonColumn>
          <ScopesListWrapper>
            {isScopesLoading ? (
              <CenteredSpin>
                <Spin />
              </CenteredSpin>
            ) : filteredScopes.length === 0 ? (
              <GrayText>No scopes.</GrayText>
            ) : (
              <Collapse
                activeKey={expandedScopes}
                onChange={keys => setExpandedScopes(Array.isArray(keys) ? keys.map(String) : [])}
                accordion={false}
              >
                {filteredScopes.map(scope => {
                  const { data: bidItemsData, isLoading: isBidItemsLoading } = getBidItemsForScope(
                    scope.id!
                  );
                  const bidItems = bidItemsData?.content || [];
                  return (
                    <Panel
                      header={
                        scope.scopeSpecCode && scope.name
                          ? `${scope.scopeSpecCode}`
                          : scope.name || 'Untitled Scope'
                      }
                      key={scope.id!}
                    >
                      {isBidItemsLoading ? (
                        <Spin size='small' />
                      ) : (
                        <Table
                          columns={bidItemColumns}
                          dataSource={getBidItemTableData(bidItems)}
                          pagination={false}
                          size='small'
                          showHeader={false}
                          locale={{
                            emptyText: <GrayText>No bid items in this scope.</GrayText>
                          }}
                        />
                      )}
                    </Panel>
                  );
                })}
              </Collapse>
            )}
          </ScopesListWrapper>
        </TabPane>
      </Tabs>
      <SpecPdfModal
        visible={specModalVisible}
        onClose={() => setSpecModalVisible(false)}
        specificationThumbnail={selectedSpecThumbnail}
      />
      {isAddModalOpen && (
        <AddScopeModal visible={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />
      )}
    </PanelWrapper>
  );
};

export default ScopesSpecificationPanel;
