import {
  SearchOutlined,
  FilterOutlined,
  PlusSquareOutlined,
  MinusSquareOutlined
} from '@ant-design/icons';
import { useQuery, useQueries } from '@tanstack/react-query';
import { Tabs, Button, Input, Collapse, Tooltip, Spin, Table } from 'antd';
import React, { useState, useMemo } from 'react';
import { scopeAPI, bidItemAPI } from 'src/api/apiClient';
import type { BidItemDTO } from 'src/api/models/bid-item-dto';
import type { BidItemSpecificationDTO } from 'src/api/models/bid-item-specification-dto';
import type { PageBidItemDTO } from 'src/api/models/page-bid-item-dto';
import { queryKeys } from 'src/modules/utils/constant';
import type { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import useScopesStore from 'src/view/screens/scopes/store/useScopesStore';
import {
  PanelWrapper,
  Ribbon,
  ScopesListWrapper,
  ExpandCollapseControls,
  BidItemsTableWrapper
} from './ScopesSpecificationPanel.style';
import SpecPdfModal from './SpecPdfModal';

const { TabPane } = Tabs;
const { Panel } = Collapse;

const ScopesSpecificationPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('scopes');
  const [expandedScopes, setExpandedScopes] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const { selectedProjectId, selectedVersion } = useGlobalStore();
  const {
    setSelectedThumbnailInfo,
    specificationsThumbnails,
    drawingsThumbnails,
    selectedThumbnailInfo
  } = useScopesStore();

  // Modal state for spec PDF
  const [specModalVisible, setSpecModalVisible] = useState(false);
  const [selectedSpecThumbnail, setSelectedSpecThumbnail] = useState<ThumbnailInfo | null>(null);

  const { data: scopesData, isLoading: isScopesLoading } = useQuery({
    queryKey: [
      queryKeys.allScopes,
      selectedProjectId,
      selectedThumbnailInfo?.sheet,
      selectedVersion,
      selectedThumbnailInfo?.specification
    ],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        String(selectedVersion?.version),
        0,
        1000,
        ['division,asc'],
        selectedThumbnailInfo?.sheet?.sheetId,
        selectedThumbnailInfo?.specification?.specificationId
      ),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  // Filter scopes by search value and show all scopes (no restriction by sheet or scopeId)
  const filteredScopes = useMemo(
    () =>
      scopesData?.content?.filter(
        scope =>
          scope.id !== undefined &&
          (scope.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
            scope.description?.toLowerCase().includes(searchValue.toLowerCase()))
      ) || [],
    [scopesData, searchValue]
  );

  // Fetch bid items for expanded scopes only
  const bidItemsQueries = useQueries({
    queries: expandedScopes.map(scopeId => ({
      queryKey: ['bidItems', scopeId],
      queryFn: () =>
        bidItemAPI.getBidItemsByScopeId(Number(scopeId), 0, 1000).then(res => res.data),
      enabled: !!scopeId
    }))
  });

  // Helper to get bid items for a scope
  function getBidItemsForScope(scopeId: number | string): {
    data?: PageBidItemDTO;
    isLoading?: boolean;
  } {
    const idx = expandedScopes.indexOf(String(scopeId));
    return idx !== -1
      ? (bidItemsQueries[idx] as { data?: PageBidItemDTO; isLoading?: boolean })
      : {};
  }

  const handleExpandAll = () => {
    if (filteredScopes.length > 0) {
      setExpandedScopes(filteredScopes.map(scope => String(scope.id!)));
    }
  };
  const handleCollapseAll = () => setExpandedScopes([]);

  // Table columns for bid items
  const bidItemColumns = [
    {
      title: 'Bid Item Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => {
        return (
          <Button
            type='link'
            style={{ fontWeight: 500, padding: 0 }}
            onClick={() => {
              const [bidItemId] = record.key.split('-');
              expandedScopes.forEach(scopeId => {
                const { data: bidItemsData } = getBidItemsForScope(scopeId);
                const bidItems = bidItemsData?.content || [];
                const bidItem = bidItems.find((b: any) => String(b.id) === bidItemId);
                if (bidItem && bidItem.bidItemSheets && bidItem.bidItemSheets.length > 0) {
                  const sheet = bidItem.bidItemSheets[0];
                  if (sheet && sheet.sheetId) {
                    const thumb = drawingsThumbnails.find(
                      t => t.sheet && String(t.sheet.sheetId) === String(sheet.sheetId)
                    );
                    if (thumb) {
                      setSelectedThumbnailInfo(thumb);
                    }
                  }
                }
              });
            }}
          >
            {/* {cleaned} */}
          </Button>
        );
      }
    },
    {
      title: 'Specification Code',
      dataIndex: 'specificationCode',
      key: 'specificationCode',
      render: (specCode: string, record: any) => {
        if (!specCode || specCode === '-') return <span>-</span>;
        return (
          <Button
            type='link'
            style={{ padding: 0 }}
            onClick={() => {
              const specId = record.key.split('-')[1];
              const thumb = specificationsThumbnails.find(
                t => t.specification && String(t.specification.specificationId) === String(specId)
              );
              if (thumb) {
                setSelectedSpecThumbnail(thumb);
                setSpecModalVisible(true);
              }
            }}
          >
            {specCode}
          </Button>
        );
      }
    }
  ];

  // Helper to flatten bid items for table
  function getBidItemTableData(bidItems: BidItemDTO[]): any[] {
    const rows: any[] = [];
    bidItems.forEach(bidItem => {
      if (bidItem.bidItemSpecificationDTOList && bidItem.bidItemSpecificationDTOList.length > 0) {
        bidItem.bidItemSpecificationDTOList.forEach((spec: BidItemSpecificationDTO) => {
          rows.push({
            key: `${bidItem.id}-${spec.specificationId}`,
            name: bidItem.name,
            specificationCode: spec.specificationCode
          });
        });
      } else {
        rows.push({
          key: `${bidItem.id}-no-spec`,
          name: bidItem.name,
          specificationCode: '-'
        });
      }
    });
    return rows;
  }

  return (
    <PanelWrapper>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab='Scopes' key='scopes'>
          <Ribbon style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
            <div style={{ display: 'flex', gap: 8, marginBottom: 8, alignItems: 'center' }}>
              <Input
                placeholder='Search scopes...'
                prefix={<SearchOutlined />}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                style={{ width: 180 }}
              />
              <Tooltip title='Filter'>
                <Button icon={<FilterOutlined />} />
              </Tooltip>
              <ExpandCollapseControls>
                <Tooltip title='Expand All'>
                  <Button onClick={handleExpandAll} size='middle' icon={<PlusSquareOutlined />} />
                </Tooltip>
                <Tooltip title='Collapse All'>
                  <Button
                    onClick={handleCollapseAll}
                    size='middle'
                    icon={<MinusSquareOutlined />}
                  />
                </Tooltip>
              </ExpandCollapseControls>
            </div>
            <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 0 }}>
              <Button type='primary'>Add Scope</Button>
              <Button type='primary'>Add Bid Item</Button>
            </div>
          </Ribbon>
          <ScopesListWrapper>
            {isScopesLoading ? (
              <Spin style={{ margin: '32px auto', display: 'block' }} />
            ) : (
              <Collapse
                activeKey={expandedScopes}
                onChange={keys => setExpandedScopes(Array.isArray(keys) ? keys.map(String) : [])}
                accordion={false}
              >
                {filteredScopes.map(scope => {
                  const { data: bidItemsData, isLoading: isBidItemsLoading } = getBidItemsForScope(
                    scope.id!
                  );
                  const bidItems = bidItemsData?.content || [];
                  return (
                    <Panel
                      header={
                        scope.scopeSpecCode && scope.name
                          ? `${scope.scopeSpecCode}`
                          : scope.name || 'Untitled Scope'
                      }
                      key={scope.id!}
                    >
                      {isBidItemsLoading ? (
                        <Spin size='small' />
                      ) : bidItems.length === 0 ? (
                        <div style={{ color: '#888', padding: '12px 0' }}>
                          No bid items in this scope.
                        </div>
                      ) : (
                        <BidItemsTableWrapper>
                          <Table
                            columns={bidItemColumns}
                            dataSource={getBidItemTableData(bidItems)}
                            pagination={false}
                            size='small'
                          />
                        </BidItemsTableWrapper>
                      )}
                    </Panel>
                  );
                })}
              </Collapse>
            )}
          </ScopesListWrapper>
        </TabPane>
      </Tabs>
      <SpecPdfModal
        visible={specModalVisible}
        onClose={() => setSpecModalVisible(false)}
        specificationThumbnail={selectedSpecThumbnail}
      />
    </PanelWrapper>
  );
};

export default ScopesSpecificationPanel;
