import {
  FileTextOutlined,
  FileImageOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { List, Avatar, Tag, Empty } from 'antd';
import React from 'react';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { SelectedDocument } from './DocView';
import { DocumentTabName } from '../../scopes/document-view/DocumentThumbnailsView';

const StyledList = styled(List)`
  .ant-list-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 4px;
    margin-bottom: 4px;
    padding: 12px;

    &:hover {
      background: ${themeTokens.pageBg};
    }
  }
`;

const SelectedListItem = styled(List.Item)<{ selected: boolean }>`
  background: ${({ selected }) =>
    selected ? `${themeTokens.primaryColor}20` : 'transparent'} !important;
  border: ${({ selected }) =>
    selected ? `1px solid ${themeTokens.primaryColor}` : '1px solid transparent'};
`;

const DocumentMeta = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 4px;
`;

const DocumentDescription = styled.div`
  color: ${themeTokens.textGray};
  font-size: 12px;
  margin-top: 4px;
`;

interface DocumentListViewProps {
  activeTab: DocumentTabName;
  searchText: string;
  filter: string;
  selectedDocument: SelectedDocument | null;
  setSelectedDocument: (document: SelectedDocument | null) => void;
}

interface Document {
  id: string;
  name: string;
  type: 'plan' | 'spec';
  hasScopes: boolean;
  isRaster: boolean;
  url?: string;
  description?: string;
}

const DocumentListView: React.FC<DocumentListViewProps> = ({
  activeTab,
  searchText,
  filter,
  selectedDocument,
  setSelectedDocument
}) => {
  // Mock data - replace with actual API calls
  const mockDocuments: Document[] = [
    {
      id: '1',
      name: 'A00-10 - Sheet Title',
      type: 'plan',
      hasScopes: true,
      isRaster: false,
      url: '/sample-document.pdf',
      description: 'Architectural floor plan with detailed room layouts'
    },
    {
      id: '2',
      name: 'Discipline 1 - Sheet 2',
      type: 'plan',
      hasScopes: false,
      isRaster: true,
      url: '/sample-document-2.pdf',
      description: 'Structural engineering drawings'
    },
    {
      id: '3',
      name: 'Discipline 2 - Sheet 3',
      type: 'plan',
      hasScopes: true,
      isRaster: false,
      url: '/sample-document-3.pdf',
      description: 'Electrical system layout and specifications'
    },
    {
      id: '4',
      name: 'Specification Document 1',
      type: 'spec',
      hasScopes: true,
      isRaster: false,
      url: '/sample-spec-1.pdf',
      description: 'Material specifications and requirements'
    },
    {
      id: '5',
      name: 'Specification Document 2',
      type: 'spec',
      hasScopes: false,
      isRaster: true,
      url: '/sample-spec-2.pdf',
      description: 'Construction standards and procedures'
    }
  ];

  const filteredDocuments = mockDocuments.filter(doc => {
    // Filter by tab
    if (doc.type !== activeTab.slice(0, -1)) return false;

    // Filter by search text
    if (searchText && !doc.name.toLowerCase().includes(searchText.toLowerCase())) {
      return false;
    }

    // Filter by type
    switch (filter) {
      case 'with-scopes':
        return doc.hasScopes;
      case 'without-scopes':
        return !doc.hasScopes;
      case 'raster':
        return doc.isRaster;
      case 'vector':
        return !doc.isRaster;
      default:
        return true;
    }
  });

  const handleDocumentClick = (document: Document) => {
    const selectedDoc: SelectedDocument = {
      id: document.id,
      name: document.name,
      type: document.type,
      url: document.url
    };
    setSelectedDocument(selectedDoc);
  };

  const getDocumentIcon = (document: Document) => {
    if (document.type === 'plan') {
      return <FileImageOutlined style={{ fontSize: '24px', color: themeTokens.primaryColor }} />;
    }
    return <FileTextOutlined style={{ fontSize: '24px', color: themeTokens.linkBlue }} />;
  };

  if (filteredDocuments.length === 0) {
    return <Empty description={`No ${activeTab} found`} style={{ marginTop: '50px' }} />;
  }

  return (
    <StyledList
      itemLayout='horizontal'
      dataSource={filteredDocuments}
      renderItem={document => (
        <SelectedListItem
          selected={selectedDocument?.id === document.id}
          onClick={() => handleDocumentClick(document)}
        >
          <List.Item.Meta
            avatar={
              <Avatar icon={getDocumentIcon(document)} style={{ backgroundColor: 'transparent' }} />
            }
            title={document.name}
            description={
              <>
                {document.description && (
                  <DocumentDescription>{document.description}</DocumentDescription>
                )}
                <DocumentMeta>
                  <Tag
                    icon={document.hasScopes ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                    color={document.hasScopes ? 'success' : 'default'}
                    style={{ fontSize: '10px' }}
                  >
                    {document.hasScopes ? 'Has Scopes' : 'No Scopes'}
                  </Tag>
                  <Tag color={document.isRaster ? 'orange' : 'blue'} style={{ fontSize: '10px' }}>
                    {document.isRaster ? 'Raster' : 'Vector'}
                  </Tag>
                </DocumentMeta>
              </>
            }
          />
        </SelectedListItem>
      )}
    />
  );
};

export default DocumentListView;
