import { Checkbox, Flex, Input } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const PDFViewerContainer = styled.div`
  height: 100%;
`;

export const LoaderContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  .loader {
    position: absolute;
    top: 30%;
  }
`;

export const Image = styled.img`
  width: auto;
  height: 85%;
  object-fit: contain;
  align-self: flex-start;
`;

export const Container = styled(Flex)`
  height: 100%;
  justify-content: center;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  width: 100%;
`;

export const HeaderText = styled.div`
  font-weight: 500;
  font-size: 25px;
  line-height: 28px;
`;

export const SearchHeaderContainer = styled.div`
  width: 100%;
`;

export const SearchContainer = styled(Flex)`
  width: 100%;
`;

export const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  .ant-select-selector {
    padding-left: 6px !important;
  }
`;

export const SearchBar = styled(Input)`
  width: 55%;
  min-width: 300px;
  border: 1px solid #201a22;
`;

export const CheckboxStyle = styled(Checkbox)<{ isActive: boolean }>`
  & .ant-checkbox .ant-checkbox-inner {
    border: 1px solid ${({ isActive }) => (!isActive ? themeTokens.activeBlue : 'unset')};
  }
`;

export const SearchResultsText = styled.div`
  font-size: 14px;
  margin-top: 8px;
  margin-left: 4px;
`;

export const BoldText = styled.span`
  font-weight: 700;
`;
