@import '../../../../../node_modules/@syncfusion/ej2-base/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-buttons/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-dropdowns/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-inputs/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-navigations/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-popups/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-pdfviewer/styles/material.css';

/* Custom styling for DocView PDF Viewer */
#docview-pdf-viewer .e-pv-viewer-main-container {
  background-color: white;
}

#docview-pdf-viewer .e-pv-toolbar {
  background-color: #003249;
  color: white;
}

#docview-pdf-viewer .e-pv-toolbar .e-toolbar-item .e-btn {
  color: white;
}

#docview-pdf-viewer .e-pv-toolbar .e-toolbar-item .e-btn:hover {
  background-color: #084967;
}

/* Customize toolbar icons */
#docview-pdf-viewer .e-pv-toolbar .e-toolbar-item .e-btn .e-btn-icon {
  color: white;
}

/* Page navigation styling */
#docview-pdf-viewer .e-pv-page-navigation-container {
  background-color: #f6f5f5;
}

/* Search highlight styling */
#docview-pdf-viewer .e-pv-search-text-highlight {
  background-color: #9de0af !important;
}

/* Annotation styling */
#docview-pdf-viewer .e-pv-annotation-toolbar {
  background-color: #003249;
}

#docview-pdf-viewer .e-pv-annotation-toolbar .e-toolbar-item .e-btn {
  color: white;
}

/* Zoom controls */
#docview-pdf-viewer .e-pv-zoom-toolbar {
  background-color: #f6f5f5;
}

/* Loading spinner */
#docview-pdf-viewer .e-pv-loading-indicator {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Scrollbar styling */
#docview-pdf-viewer .e-pv-viewer-container::-webkit-scrollbar {
  width: 8px;
}

#docview-pdf-viewer .e-pv-viewer-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

#docview-pdf-viewer .e-pv-viewer-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

#docview-pdf-viewer .e-pv-viewer-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Search Modal Specific Styling */
#search-modal-pdf-viewer .e-pv-viewer-main-container {
  background-color: white;
}

#search-modal-pdf-viewer .e-pv-toolbar {
  background-color: #003249;
  color: white;
}

#search-modal-pdf-viewer .e-pv-toolbar .e-toolbar-item .e-btn {
  color: white;
}

#search-modal-pdf-viewer .e-pv-toolbar .e-toolbar-item .e-btn:hover {
  background-color: #084967;
}

/* Modal Animation */
.ant-modal-mask {
  animation: fadeIn 0.3s ease-in-out;
}

.ant-modal-wrap {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
