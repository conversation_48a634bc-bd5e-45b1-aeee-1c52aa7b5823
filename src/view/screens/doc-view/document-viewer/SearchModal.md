# Search Modal Implementation

## Overview
The Search Modal is a bottom-up sliding popup that provides an isolated search interface for documents within the Document Viewer component. It allows users to search through drawings and specifications without affecting the main document viewer state.

## Features

### Modal Design
- **Bottom-up sliding animation**: Modal slides up from the bottom of the screen
- **Large viewport**: Takes up 90% of screen width and 70% of screen height
- **Isolated state**: All interactions within the modal are separate from the main viewer

### Search Functionality
- **Search bar**: Located at the top of the modal for easy access
- **Real-time search**: Searches through documents, scopes, and specifications
- **Clear functionality**: Users can clear search and view all documents

### Left Panel - Document Organization
- **Drawings Section**: 
  - Displays drawings grouped by scope name
  - Accordion-style collapsible sections
  - Shows document count per scope
  - Thumbnail grid view within each scope

- **Specifications Section**:
  - Similar structure to drawings
  - Grouped by scope name
  - Collapsible accordion interface
  - Thumbnail previews

### Right Panel - PDF Preview
- **Document viewer**: Full SyncFusion PDF viewer integration
- **Document title**: Shows selected document name in header
- **Navigation tools**: Page navigation, zoom, search within document
- **Read-only mode**: Annotations are locked for preview purposes

### Thumbnail Interaction
- **Visual selection**: Selected thumbnails are highlighted with primary color border
- **Hover effects**: Thumbnails show hover state for better UX
- **Click to preview**: Clicking a thumbnail loads it in the right panel
- **Auto-navigation**: Automatically navigates to the correct page number

## Technical Implementation

### Components
1. **SearchModal.tsx**: Main modal container with search and layout
2. **SearchModalPDFViewer.tsx**: Isolated PDF viewer for modal preview

### State Management
- **Isolated state**: Modal maintains its own thumbnail data separate from main viewer
- **Search state**: Independent search functionality with its own API calls
- **Selection state**: Tracks selected thumbnail within modal context

### Data Flow
1. User clicks search button in main DocumentViewer
2. Modal opens and loads initial document data
3. User can search, which triggers isolated API calls
4. Results are displayed in grouped accordion format
5. Thumbnail selection updates the PDF preview
6. Modal state is reset when closed

### API Integration
- **Search API**: Uses `openSearchAPI.searchByText()` for document search
- **Thumbnail generation**: Leverages existing thumbnail URL generation
- **Document loading**: Supports both regular documents and specification documents

## Usage

### Opening the Modal
```typescript
// In DocumentViewer component
<Button
  icon={<SearchOutlined />}
  type='primary'
  size='large'
  onClick={() => setIsSearchModalVisible(true)}
>
  Search
</Button>
```

### Modal Props
```typescript
interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  drawingsThumbnails: ThumbnailInfo[];
  specificationsThumbnails: ThumbnailInfo[];
  isLoading?: boolean;
  onSearch?: (searchText: string) => void;
}
```

## Styling

### Theme Integration
- Uses consistent theme tokens for colors
- Matches existing application design patterns
- Responsive layout with proper spacing

### Animations
- Smooth slide-up animation for modal appearance
- Fade-in effect for modal mask
- Hover transitions for interactive elements

### Responsive Design
- Fixed modal dimensions for consistent experience
- Scrollable content areas for large datasets
- Proper overflow handling

## Benefits

1. **Isolated Experience**: Search doesn't interfere with main document viewing
2. **Comprehensive View**: Users can see all related documents in organized format
3. **Quick Preview**: Instant document preview without leaving search context
4. **Scope Organization**: Documents are logically grouped by scope for easy navigation
5. **Performance**: Efficient thumbnail loading and caching

## Future Enhancements

1. **Advanced Filters**: Add filtering by document type, date, etc.
2. **Bookmarking**: Allow users to bookmark frequently accessed documents
3. **Recent Searches**: Store and display recent search queries
4. **Bulk Actions**: Enable multiple document selection and actions
5. **Export Options**: Allow exporting search results or selected documents
