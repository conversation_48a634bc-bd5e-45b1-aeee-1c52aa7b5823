import { LoadingOutlined } from '@ant-design/icons';
import { Modal, Input, Spin } from 'antd';
import React, { useState, useEffect } from 'react';
import { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import SearchModalPDFViewer from './SearchModalPDFViewer';

const { Search } = Input;

const StyledModal = styled(Modal)`
  .ant-modal {
    margin: 0;
    padding: 0;
    max-width: 100vw;
  }

  .ant-modal-content {
    border-radius: 16px 16px 0 0;
    overflow: hidden;
  }

  .ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
    background: ${themeTokens.textLight};
  }

  .ant-modal-body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .ant-modal-footer {
    display: none;
  }
`;

const SearchContainer = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
`;

const ContentContainer = styled.div`
  display: flex;
  height: calc(70vh - 80px);
`;

const LeftPanel = styled.div`
  width: 400px;
  border-right: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
  overflow-y: auto;
`;

const RightPanel = styled.div`
  flex: 1;
  background: ${themeTokens.pageBg};
  display: flex;
  flex-direction: column;
`;

const SectionHeader = styled.div`
  padding: 16px 20px 8px;
  font-weight: 600;
  font-size: 16px;
  color: ${themeTokens.textDark};
  background: ${themeTokens.pageBg};
  border-bottom: 1px solid #e8e8e8;
`;

const ThumbnailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
`;

const ThumbnailItem = styled.div<{ selected: boolean }>`
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: ${({ selected }) =>
    selected ? `2px solid ${themeTokens.primaryColor}` : '1px solid #e8e8e8'};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${themeTokens.primaryColor};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const ThumbnailImage = styled.img`
  width: 100%;
  height: 60px;
  object-fit: cover;
  display: block;
`;

const ThumbnailTitle = styled.div`
  padding: 4px 6px;
  font-size: 10px;
  background: ${themeTokens.textLight};
  color: ${themeTokens.textDark};
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${themeTokens.textGray};
  font-size: 14px;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  drawingsThumbnails: ThumbnailInfo[];
  specificationsThumbnails: ThumbnailInfo[];
  isLoading?: boolean;
  onSearch?: (searchText: string) => void;
}

const SearchModal: React.FC<SearchModalProps> = ({
  visible,
  onClose,
  drawingsThumbnails,
  specificationsThumbnails,
  isLoading = false,
  onSearch
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedThumbnail, setSelectedThumbnail] = useState<ThumbnailInfo | null>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setSearchText('');
      setSelectedThumbnail(null);
    }
  }, [visible]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    onSearch?.(value);
  };

  const handleThumbnailClick = (thumbnail: ThumbnailInfo) => {
    setSelectedThumbnail(thumbnail);
  };

  const renderThumbnailGrid = (thumbnails: ThumbnailInfo[], type: 'drawings' | 'specs') => {
    if (isLoading) {
      return (
        <LoadingContainer>
          <Spin indicator={<LoadingOutlined spin />} />
        </LoadingContainer>
      );
    }

    if (thumbnails.length === 0) {
      return <EmptyState>No {type} found</EmptyState>;
    }

    return (
      <ThumbnailGrid>
        {thumbnails.map((thumbnail, index) => {
          const isActive = selectedThumbnail === thumbnail;
          const title =
            type === 'drawings'
              ? thumbnail.sheet?.title
              : thumbnail.specification?.specificationTitle;
          const subtitle =
            type === 'drawings'
              ? thumbnail.sheet?.sheetNumber
              : thumbnail.specification?.specificationCode;

          return (
            <ThumbnailItem
              key={`${type}-${index}`}
              selected={isActive}
              onClick={() => !isActive && handleThumbnailClick(thumbnail)}
            >
              <ThumbnailImage src={thumbnail.thumbnail} alt={title || 'Document'} />
              <ThumbnailTitle>
                {subtitle && <div style={{ fontSize: '9px', fontWeight: 'bold' }}>{subtitle}</div>}
                <div>{title || 'Untitled'}</div>
              </ThumbnailTitle>
            </ThumbnailItem>
          );
        })}
      </ThumbnailGrid>
    );
  };

  return (
    <StyledModal
      open={visible}
      onCancel={onClose}
      width='90vw'
      // style={{
      //   bottom: 0
      //   // position: 'fixed'
      //   // margin: 0
      // }}
      styles={{
        mask: { background: 'rgba(0, 0, 0, 0.3)' }
      }}
      destroyOnClose
      title='Search Documents'
      // centered={false}
    >
      <SearchContainer>
        <Search
          placeholder='Search documents, scopes, or specifications...'
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          onSearch={handleSearch}
          size='large'
          allowClear
        />
      </SearchContainer>

      <ContentContainer>
        <LeftPanel>
          {drawingsThumbnails.length > 0 && (
            <>
              <SectionHeader>Drawings ({drawingsThumbnails.length})</SectionHeader>
              <div style={{ padding: '0 16px' }}>
                {renderThumbnailGrid(drawingsThumbnails, 'drawings')}
              </div>
            </>
          )}

          {specificationsThumbnails.length > 0 && (
            <>
              <SectionHeader>Specifications ({specificationsThumbnails.length})</SectionHeader>
              <div style={{ padding: '0 16px 16px' }}>
                {renderThumbnailGrid(specificationsThumbnails, 'specs')}
              </div>
            </>
          )}

          {drawingsThumbnails.length === 0 &&
            specificationsThumbnails.length === 0 &&
            !isLoading && <EmptyState>No documents found</EmptyState>}
        </LeftPanel>

        <RightPanel>
          <SearchModalPDFViewer selectedThumbnail={selectedThumbnail} />
        </RightPanel>
      </ContentContainer>
    </StyledModal>
  );
};

export default SearchModal;
