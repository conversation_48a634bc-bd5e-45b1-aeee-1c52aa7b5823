import { SearchOutlined, LoadingOutlined } from '@ant-design/icons';
import { Modal, Input, Collapse, Flex, Spin } from 'antd';
import React, { useState, useEffect } from 'react';
import { DocumentTabName } from 'src/modules/utils/constant';
import { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import SearchModalPDFViewer from './SearchModalPDFViewer';

const { Search } = Input;
const { Panel } = Collapse;

const StyledModal = styled(Modal)`
  .ant-modal {
    margin: 0;
    padding: 0;
    max-width: 100vw;
  }

  .ant-modal-content {
    border-radius: 16px 16px 0 0;
    overflow: hidden;
  }

  .ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
    background: ${themeTokens.textLight};
  }

  .ant-modal-body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .ant-modal-footer {
    display: none;
  }
`;

const SearchContainer = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
`;

const ContentContainer = styled.div`
  display: flex;
  height: calc(70vh - 80px);
`;

const LeftPanel = styled.div`
  width: 400px;
  border-right: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
  overflow-y: auto;
`;

const RightPanel = styled.div`
  flex: 1;
  background: ${themeTokens.pageBg};
  display: flex;
  flex-direction: column;
`;

const SectionHeader = styled.div`
  padding: 16px 20px 8px;
  font-weight: 600;
  font-size: 16px;
  color: ${themeTokens.textDark};
  background: ${themeTokens.pageBg};
  border-bottom: 1px solid #e8e8e8;
`;

const StyledCollapse = styled(Collapse)`
  border: none;
  background: transparent;

  .ant-collapse-item {
    border: none;
    margin-bottom: 8px;
  }

  .ant-collapse-header {
    background: ${themeTokens.pageBg} !important;
    border-radius: 6px !important;
    font-weight: 500;
    padding: 12px 16px !important;
  }

  .ant-collapse-content {
    border: none;
    background: transparent;
  }

  .ant-collapse-content-box {
    padding: 8px 16px 16px !important;
  }
`;

const ThumbnailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
`;

const ThumbnailItem = styled.div<{ selected: boolean }>`
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: ${({ selected }) =>
    selected ? `2px solid ${themeTokens.primaryColor}` : '1px solid #e8e8e8'};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${themeTokens.primaryColor};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const ThumbnailImage = styled.img`
  width: 100%;
  height: 60px;
  object-fit: cover;
  display: block;
`;

const ThumbnailTitle = styled.div`
  padding: 4px 6px;
  font-size: 10px;
  background: ${themeTokens.textLight};
  color: ${themeTokens.textDark};
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${themeTokens.textGray};
  font-size: 14px;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  drawingsThumbnails: ThumbnailInfo[];
  specificationsThumbnails: ThumbnailInfo[];
  isLoading?: boolean;
  onSearch?: (searchText: string) => void;
}

interface GroupedThumbnails {
  [scopeName: string]: ThumbnailInfo[];
}

const SearchModal: React.FC<SearchModalProps> = ({
  visible,
  onClose,
  drawingsThumbnails,
  specificationsThumbnails,
  isLoading = false,
  onSearch
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedThumbnail, setSelectedThumbnail] = useState<ThumbnailInfo | null>(null);
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setSearchText('');
      setSelectedThumbnail(null);
      setExpandedPanels([]);
    }
  }, [visible]);

  // Group thumbnails by scope
  const groupThumbnailsByScope = (thumbnails: ThumbnailInfo[]): GroupedThumbnails => {
    return thumbnails.reduce((groups: GroupedThumbnails, thumbnail) => {
      const scopeName =
        thumbnail.sheet?.scopeName || thumbnail.specification?.scopeName || 'Unassigned';
      if (!groups[scopeName]) {
        groups[scopeName] = [];
      }
      groups[scopeName].push(thumbnail);
      return groups;
    }, {});
  };

  const groupedDrawings = groupThumbnailsByScope(drawingsThumbnails);
  const groupedSpecs = groupThumbnailsByScope(specificationsThumbnails);

  const handleSearch = (value: string) => {
    setSearchText(value);
    onSearch?.(value);
  };

  const handleThumbnailClick = (thumbnail: ThumbnailInfo) => {
    setSelectedThumbnail(thumbnail);
  };

  const renderThumbnailGroup = (groupName: string, thumbnails: ThumbnailInfo[]) => (
    <Panel header={`${groupName} (${thumbnails.length})`} key={groupName}>
      {isLoading ? (
        <LoadingContainer>
          <Spin indicator={<LoadingOutlined spin />} />
        </LoadingContainer>
      ) : thumbnails.length > 0 ? (
        <ThumbnailGrid>
          {thumbnails.map((thumbnail, index) => (
            <ThumbnailItem
              key={`${groupName}-${index}`}
              selected={selectedThumbnail === thumbnail}
              onClick={() => handleThumbnailClick(thumbnail)}
            >
              <ThumbnailImage
                src={thumbnail.thumbnail}
                alt={
                  thumbnail.sheet?.title ||
                  thumbnail.specification?.specificationTitle ||
                  'Document'
                }
              />
              <ThumbnailTitle>
                {thumbnail.sheet?.title ||
                  thumbnail.specification?.specificationTitle ||
                  'Untitled'}
              </ThumbnailTitle>
            </ThumbnailItem>
          ))}
        </ThumbnailGrid>
      ) : (
        <EmptyState>No documents found</EmptyState>
      )}
    </Panel>
  );

  return (
    <StyledModal
      open={visible}
      onCancel={onClose}
      width='90vw'
      style={{
        bottom: 0,
        position: 'fixed',
        margin: 0
      }}
      maskStyle={{
        background: 'rgba(0, 0, 0, 0.3)'
      }}
      destroyOnClose
      title='Search Documents'
      // centered={false}
    >
      <SearchContainer>
        <Search
          placeholder='Search documents, scopes, or specifications...'
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          onSearch={handleSearch}
          size='large'
          allowClear
        />
      </SearchContainer>

      <ContentContainer>
        <LeftPanel>
          <SectionHeader>Drawings</SectionHeader>
          <div style={{ padding: '0 16px' }}>
            <StyledCollapse activeKey={expandedPanels} onChange={setExpandedPanels} ghost>
              {Object.entries(groupedDrawings).map(([scopeName, thumbnails]) =>
                renderThumbnailGroup(scopeName, thumbnails)
              )}
            </StyledCollapse>
          </div>

          <SectionHeader>Specifications</SectionHeader>
          <div style={{ padding: '0 16px 16px' }}>
            <StyledCollapse activeKey={expandedPanels} onChange={setExpandedPanels} ghost>
              {Object.entries(groupedSpecs).map(([scopeName, thumbnails]) =>
                renderThumbnailGroup(scopeName, thumbnails)
              )}
            </StyledCollapse>
          </div>
        </LeftPanel>

        <RightPanel>
          <SearchModalPDFViewer selectedThumbnail={selectedThumbnail} />
        </RightPanel>
      </ContentContainer>
    </StyledModal>
  );
};

export default SearchModal;
