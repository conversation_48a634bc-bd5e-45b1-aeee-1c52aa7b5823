import { LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Modal, Input, Spin, Collapse } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { DocumentDTOInputDocumentTypeEnum } from 'src/api';
import { bidItemAPI, openSearchAPI, projectAPI } from 'src/api/apiClient';
import { queryKeys } from 'src/modules/utils/constant';
import { getThumbnailS3Url, ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import PDFViewer from '../PDFViewer';

const { Panel } = Collapse;

export const StyledImageContainer = styled.div`
  display: flex !important;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  gap: 2px;
  padding: 10px;
  text-align: center;
  width: 150px;
  max-width: 150px;
`;

export const StyledImage = styled.img<{ isActive: boolean }>`
  height: 100%;
  width: fit-content;
  object-fit: contain;
  max-height: 70px;
  border: 1px solid ${({ isActive }) => (isActive ? themeTokens.primaryColor : '#4e4e4e')};
`;

const StyledModal = styled(Modal)`
  .ant-modal {
    margin: 0;
    padding: 0;
    max-width: 100vw;
  }

  .ant-modal-content {
    border-radius: 16px 16px 0 0;
    overflow: hidden;
  }

  .ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
    background: ${themeTokens.textLight};
  }

  .ant-modal-body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .ant-modal-footer {
    display: none;
  }
`;

export const PDFViewerContainer = styled.div`
  height: 100%;
`;

export const LoaderContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  .loader {
    position: absolute;
    top: 30%;
  }
`;

export const Image = styled.img`
  width: auto;
  height: 85%;
  object-fit: contain;
  align-self: flex-start;
`;

const SearchContainer = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
`;

const ContentContainer = styled.div`
  display: flex;
  height: calc(70vh - 80px);
`;

const SearchBar = styled(Input)`
  width: 55%;
  min-width: 300px;
  border: 1px solid #201a22;
`;

const LeftPanel = styled.div`
  width: 400px;
  border-right: 1px solid #e8e8e8;
  background: ${themeTokens.textLight};
  overflow-y: auto;
`;

const RightPanel = styled.div`
  flex: 1;
  background: ${themeTokens.pageBg};
  display: flex;
  flex-direction: column;
`;

const SectionHeader = styled.div`
  padding: 16px 20px 8px;
  font-weight: 600;
  font-size: 16px;
  color: ${themeTokens.textDark};
  background: ${themeTokens.pageBg};
  border-bottom: 1px solid #e8e8e8;
`;

const StyledCollapse = styled(Collapse)`
  border: none;
  background: transparent;

  .ant-collapse-item {
    border: none;
    margin-bottom: 8px;
  }

  .ant-collapse-header {
    background: ${themeTokens.pageBg} !important;
    border-radius: 6px !important;
    font-weight: 500;
    padding: 12px 16px !important;
  }

  .ant-collapse-content {
    border: none;
    background: transparent;
  }

  .ant-collapse-content-box {
    padding: 8px 16px 16px !important;
  }
`;

const ThumbnailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
`;

const ThumbnailItem = styled.div<{ selected: boolean }>`
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: ${({ selected }) =>
    selected ? `2px solid ${themeTokens.primaryColor}` : '1px solid #e8e8e8'};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${themeTokens.primaryColor};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const ThumbnailImage = styled.img`
  width: 100%;
  height: 60px;
  object-fit: cover;
  display: block;
`;

const ThumbnailTitle = styled.div`
  padding: 4px 6px;
  font-size: 10px;
  background: ${themeTokens.textLight};
  color: ${themeTokens.textDark};
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${themeTokens.textGray};
  font-size: 14px;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  isLoading?: boolean;
  searchText: string;
  handleSearch: (query: string) => void;
}

interface GroupedThumbnails {
  [scopeName: string]: ThumbnailInfo[];
}

const SearchModal: React.FC<SearchModalProps> = ({
  visible,
  onClose,
  isLoading = false,
  searchText,
  handleSearch
}) => {
  const { scopeId } = useParams();
  const isMounted = useRef(false);

  const [selectedThumbnail, setSelectedThumbnail] = useState<ThumbnailInfo | null>(null);
  const [expandedDrawingPanels, setExpandedDrawingPanels] = useState<string[]>([]);
  const [expandedSpecPanels, setExpandedSpecPanels] = useState<string[]>([]);
  const [modalDrawingThumbnails, setModalDrawingThumbnails] = useState<ThumbnailInfo[]>([]);
  const [modalSpecificationThumbnails, setModalSpecificationThumbnails] = useState<ThumbnailInfo[]>(
    []
  );

  const { selectedProjectId, currentUser, selectedVersion } = useGlobalStore();

  const {
    data: searchTextData,
    refetch: fetchSearchTextData,
    isFetching
  } = useQuery({
    queryKey: [queryKeys.searchTextData, selectedProjectId, selectedVersion, scopeId],
    queryFn: () =>
      openSearchAPI.searchByText(
        Number(selectedProjectId),
        searchText,
        undefined,
        undefined,
        'all',
        'all'
      ),
    enabled: false
  });

  const {
    data: drawingsAndSpecsData,
    isFetching: isFetchingDrawingsAndSpecs,
    refetch: fetchDrawingsAndSpecs
  } = useQuery({
    queryKey: [selectedProjectId],
    queryFn: () => bidItemAPI.getSheetsAndSpecs(Number(selectedProjectId), 'all', 'all', 0, 1000),
    select: res => res.data,
    enabled: false
  });

  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: false,
    select: res => res.data
  });

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs || !drawingsAndSpecsData) return;
    const drawings = drawingsAndSpecsData?.bidItemSheets;
    const specs = drawingsAndSpecsData?.bidItemSpecificationDTOList;
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (drawings) {
      drawingThumbnails = drawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setModalDrawingThumbnails(drawingThumbnails);
    }
    if (specs) {
      specificationThumbnails = specs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setModalSpecificationThumbnails(specificationThumbnails);
    }
  }, [drawingsAndSpecsData, isFetchingDrawingsAndSpecs]);

  useEffect(() => {
    if (isFetching || !searchTextData) return;

    const data = (searchTextData?.data || [])?.map(item => {
      const doc =
        documentsData?.documents?.find(doc => doc?.id === Number(item?.documentId)) || null;
      const thumbnail = getThumbnailS3Url(
        currentUser?.companyId || 0,
        Number(selectedProjectId),
        Number(item.documentId),
        Number(item.pageNumber) || 0
      );

      return {
        document: doc,
        sheet: item?.sheets?.[0],
        thumbnail,
        specification: item?.specifications?.[0]
      };
    });
    setModalDrawingThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
      )
    );
    setModalSpecificationThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
      )
    );
  }, [searchTextData, isFetching]);

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      return;
    }
    if (selectedProjectId) {
      if (searchText.length > 0) {
        fetchSearchTextData();
      } else {
        fetchDrawingsAndSpecs();
      }
    }
  }, [searchText]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      // handleSearch('');
      setSelectedThumbnail(null);
      setExpandedDrawingPanels([]);
      setExpandedSpecPanels([]);
    }
  }, [visible]);

  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length === 0) {
      fetchDrawingsAndSpecs();
    }
    handleSearch(e.target.value);
  };

  const onSearchHandler = () => {
    if (searchText.length > 0) fetchSearchTextData();
    else fetchDrawingsAndSpecs();
  };
  // Group thumbnails by document name or create fallback groups
  const groupThumbnailsByDocument = (thumbnails: ThumbnailInfo[]): GroupedThumbnails => {
    return thumbnails.reduce((groups: GroupedThumbnails, thumbnail) => {
      // Use document title as group name, fallback to "Unassigned"
      const groupName = thumbnail.document?.title || 'Unassigned Documents';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(thumbnail);
      return groups;
    }, {});
  };

  const groupedDrawings = groupThumbnailsByDocument(modalDrawingThumbnails);
  const groupedSpecs = groupThumbnailsByDocument(modalSpecificationThumbnails);

  const handleThumbnailClick = (thumbnail: ThumbnailInfo) => {
    setSelectedThumbnail(thumbnail);
  };

  const renderThumbnailGroup = (
    groupName: string,
    thumbnails: ThumbnailInfo[],
    type: 'drawings' | 'specs'
  ) => {
    let content;

    if (isLoading) {
      content = (
        <LoadingContainer>
          <Spin indicator={<LoadingOutlined spin />} />
        </LoadingContainer>
      );
    } else if (thumbnails.length > 0) {
      content = (
        <ThumbnailGrid>
          {thumbnails.map((thumbnail, index) => {
            const isActive = selectedThumbnail === thumbnail;
            const title =
              type === 'drawings'
                ? thumbnail.sheet?.title
                : thumbnail.specification?.specificationTitle;
            const subtitle =
              type === 'drawings'
                ? thumbnail.sheet?.sheetNumber
                : thumbnail.specification?.specificationCode;

            return (
              <ThumbnailItem
                key={`${type}-${groupName}-${index}`}
                selected={isActive}
                onClick={() => !isActive && handleThumbnailClick(thumbnail)}
              >
                <ThumbnailImage src={thumbnail.thumbnail} alt={title || 'Document'} />
                <ThumbnailTitle>
                  {subtitle && (
                    <div style={{ fontSize: '9px', fontWeight: 'bold' }}>{subtitle}</div>
                  )}
                  <div>{title || 'Untitled'}</div>
                </ThumbnailTitle>
              </ThumbnailItem>
            );
          })}
        </ThumbnailGrid>
      );
    } else {
      content = <EmptyState>No documents found</EmptyState>;
    }

    return (
      <Panel header={`${groupName} (${thumbnails.length})`} key={`${type}-${groupName}`}>
        {content}
      </Panel>
    );
  };

  return (
    <StyledModal
      open={visible}
      onCancel={onClose}
      width='90vw'
      destroyOnClose
      title='Search Documents'
    >
      <SearchContainer>
        <SearchBar
          placeholder='Search here'
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={handleSearchChange}
          size='large'
          variant='outlined'
          onPressEnter={onSearchHandler}
        />
      </SearchContainer>

      <ContentContainer>
        <LeftPanel>
          <SectionHeader>Drawings ({modalDrawingThumbnails.length})</SectionHeader>
          {Object.keys(groupedDrawings).length > 0 && (
            <div style={{ padding: '0 16px' }}>
              <StyledCollapse
                activeKey={expandedDrawingPanels}
                onChange={setExpandedDrawingPanels}
                ghost
              >
                {Object.entries(groupedDrawings).map(([groupName, thumbnails]) =>
                  renderThumbnailGroup(groupName, thumbnails, 'drawings')
                )}
              </StyledCollapse>
            </div>
          )}

          <SectionHeader>Specifications ({modalSpecificationThumbnails.length})</SectionHeader>
          {Object.keys(groupedSpecs).length > 0 && (
            <div style={{ padding: '0 16px 16px' }}>
              <StyledCollapse activeKey={expandedSpecPanels} onChange={setExpandedSpecPanels} ghost>
                {Object.entries(groupedSpecs).map(([groupName, thumbnails]) =>
                  renderThumbnailGroup(groupName, thumbnails, 'specs')
                )}
              </StyledCollapse>
            </div>
          )}
        </LeftPanel>

        <RightPanel>
          <PDFViewerContainer>
            {selectedThumbnail ? (
              <PDFViewer
                isThumbnailsDrawerMinimised={true}
                selectedThumbnailInfo={selectedThumbnail}
                setSelectedThumbnailInfo={setSelectedThumbnail}
              />
            ) : (
              <div>Please select a thumbnail to preview</div>
            )}
          </PDFViewerContainer>
        </RightPanel>
      </ContentContainer>
    </StyledModal>
  );
};

export default SearchModal;
