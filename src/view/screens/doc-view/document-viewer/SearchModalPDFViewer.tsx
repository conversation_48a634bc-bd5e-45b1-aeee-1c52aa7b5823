import React, { useRef, useEffect } from 'react';
import {
  PdfViewerComponent,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  Print,
  TextSelection,
  Annotation,
  TextSearch,
  FormFields,
  FormDesigner,
  Inject,
  PageOrganizer
} from '@syncfusion/ej2-react-pdfviewer';
import styled from 'styled-components';
import { themeTokens } from 'src/theme/tokens';
import { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import { getSpecDocS3Url } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import './DocumentViewer.css';

const ViewerContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${themeTokens.textLight};
`;

const HeaderRibbon = styled.div`
  padding: 12px 16px;
  background: ${themeTokens.tableHeaderBg};
  color: ${themeTokens.textLight};
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #e8e8e8;
`;

const ViewerContent = styled.div`
  flex: 1;
  position: relative;
  min-height: 0;
`;

const PlaceholderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${themeTokens.textGray};
  font-size: 16px;
  background: ${themeTokens.pageBg};
`;

const PlaceholderIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

interface SearchModalPDFViewerProps {
  selectedThumbnail: ThumbnailInfo | null;
}

const SearchModalPDFViewer: React.FC<SearchModalPDFViewerProps> = ({ selectedThumbnail }) => {
  const viewer = useRef<PdfViewerComponent>(null);
  const { currentUser, selectedProjectId } = useGlobalStore();

  useEffect(() => {
    if (selectedThumbnail && viewer.current) {
      let documentUrl = '';

      // Determine the document URL based on the type
      if (selectedThumbnail.document?.presignedUrl) {
        documentUrl = selectedThumbnail.document.presignedUrl;
      } else if (selectedThumbnail.specification) {
        // For specifications, use the spec document URL
        documentUrl = getSpecDocS3Url(
          currentUser?.companyId,
          Number(selectedProjectId),
          selectedThumbnail.specification.documentId,
          selectedThumbnail.specification.modifiedCode
        );
      }

      if (documentUrl) {
        viewer.current.load(documentUrl, null);
      }
    }
  }, [selectedThumbnail, currentUser, selectedProjectId]);

  const onDocumentLoad = () => {
    console.log('Search modal document loaded successfully');

    // Navigate to specific page if available
    if (selectedThumbnail?.sheet?.pageNumber && viewer.current) {
      viewer.current.navigation.goToPage(selectedThumbnail.sheet.pageNumber);
    } else if (selectedThumbnail?.specification?.pageNumber && viewer.current) {
      viewer.current.navigation.goToPage(selectedThumbnail.specification.pageNumber);
    }
  };

  const onToolBarClick = (args: any) => {
    console.log('Search modal toolbar clicked:', args.item.id);
  };

  const getDocumentTitle = () => {
    if (selectedThumbnail?.sheet) {
      return selectedThumbnail.sheet.title || 'Drawing Document';
    } else if (selectedThumbnail?.specification) {
      return selectedThumbnail.specification.specificationTitle || 'Specification Document';
    }
    return 'Document Preview';
  };

  if (!selectedThumbnail) {
    return (
      <ViewerContainer>
        <HeaderRibbon>Document Preview</HeaderRibbon>
        <PlaceholderContainer>
          <PlaceholderIcon>📄</PlaceholderIcon>
          <div>Select a document to preview</div>
        </PlaceholderContainer>
      </ViewerContainer>
    );
  }

  return (
    <ViewerContainer>
      <HeaderRibbon>{getDocumentTitle()}</HeaderRibbon>

      <ViewerContent>
        <PdfViewerComponent
          ref={viewer}
          id='search-modal-pdf-viewer'
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          width='100%'
          height='100%'
          documentLoad={onDocumentLoad}
          toolbarClick={onToolBarClick}
          toolbarSettings={{
            showTooltip: true,
            toolbarItems: [
              'PageNavigationTool',
              'MagnificationTool',
              'PanTool',
              'SelectionTool',
              'SearchOption',
              'PrintOption',
              'DownloadOption'
            ]
          }}
          annotationSettings={{
            isLock: true // Read-only mode for search modal
          }}
          textSearchSettings={{
            isTextSearch: true
          }}
        >
          <Inject
            services={[
              Toolbar,
              Magnification,
              Navigation,
              Annotation,
              LinkAnnotation,
              Print,
              TextSelection,
              TextSearch,
              FormFields,
              FormDesigner,
              PageOrganizer
            ]}
          />
        </PdfViewerComponent>
      </ViewerContent>
    </ViewerContainer>
  );
};

export default SearchModalPDFViewer;
