import { LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Button, Flex, Select, Spin } from 'antd';
import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  DocumentDTOInputDocumentTypeEnum,
  DocumentWithPresignedUrlDTOInputDocumentTypeEnum
} from 'src/api';
import { bidItemAPI, openSearchAPI, projectAPI, scopeAPI } from 'src/api/apiClient';
import SearchDropdown from 'src/modules/common/searchDropdown';
import { queryKeys } from 'src/modules/utils/constant';
import { getThumbnailS3Url, ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import './DocumentViewer.css';
import {
  Container,
  Content,
  HeaderText,
  PDFViewerContainer,
  LoaderContainer,
  Image,
  SearchHeaderContainer,
  SearchContainer,
  FilterContainer,
  SearchBar,
  CheckboxStyle,
  SearchResultsText,
  BoldText
} from './DocumentViewer style';
import SearchModal from './SearchModal';
import { DocumentTabName } from '../../scopes/document-view/DocumentThumbnailsView';
import PDFViewer from '../../scopes/document-view/PDFViewer';
import useScopesStore from '../../scopes/store/useScopesStore';

interface DocumentViewerProps {
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
}
const filterType = {
  all: 'All',
  currentScope: 'Current Scope'
};
const DocumentViewer: React.FC<DocumentViewerProps> = ({ activeTab, setActiveTab }) => {
  const { projectId } = useParams();

  const isMounted = useRef(false);
  const [searchText, setSearchText] = useState<string>('');
  // const [showDrawings, setShowDrawings] = useState<boolean>(true);
  // const [showSpecs, setShowSpecs] = useState<boolean>(true);
  // const [drawingsFilterType, setDrawingsFilterType] = useState<string>(filterType.currentScope);
  // const [specificationsFilterType, setSpecificationsFilterType] = useState<string>(
  //   filterType.currentScope
  // );
  const [isSearchTextAvailable, setIsSearchTextAvailable] = useState<boolean>(false);
  const [isSearchModalVisible, setIsSearchModalVisible] = useState<boolean>(false);
  const [modalDrawingsThumbnails, setModalDrawingsThumbnails] = useState<ThumbnailInfo[]>([]);
  const [modalSpecificationsThumbnails, setModalSpecificationsThumbnails] = useState<
    ThumbnailInfo[]
  >([]);

  const {
    selectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails,
    isCreatingThumbnails,
    isLoadingDocument,
    setSpecificationsThumbnails,
    setDrawingsThumbnails
  } = useScopesStore();
  const { selectedProjectId, currentUser, selectedVersion } = useGlobalStore();
  const { scopeId } = useParams();

  const {
    data: searchTextData,
    refetch: fetchSearchTextData,
    isFetching
  } = useQuery({
    queryKey: [
      queryKeys.searchTextData,
      // drawingsFilterType,
      // specificationsFilterType,
      // showDrawings,
      // showSpecs,
      projectId,
      selectedVersion,
      scopeId
    ],
    queryFn: () =>
      openSearchAPI.searchByText(
        Number(projectId),
        searchText,
        undefined,
        undefined,
        // drawingFilters,
        // specificationFilters
        'all',
        'all'
      ),
    enabled: false
  });

  useEffect(() => {
    if (
      selectedThumbnailInfo?.document?.inputDocumentType ===
      DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Specification
    )
      setActiveTab(DocumentTabName.specs);
  }, []);

  // const drawingFilters = useMemo(() => {
  //   if (showDrawings) {
  //     return drawingsFilterType === filterType.currentScope ? scopeId : drawingsFilterType;
  //   }
  //   return undefined;
  // }, [drawingsFilterType, scopeId, showDrawings]);

  // const specificationFilters = useMemo(() => {
  //   if (showSpecs) {
  //     return specificationsFilterType === filterType.currentScope
  //       ? scopeId
  //       : specificationsFilterType;
  //   }
  //   return undefined;
  // }, [scopeId, showSpecs, specificationsFilterType]);

  const {
    data: drawingsAndSpecsData,
    isFetching: isFetchingDrawingsAndSpecs,
    refetch: fetchDrawingsAndSpecs
  } = useQuery({
    queryKey: [
      queryKeys.drawingsAndSpecs,
      selectedProjectId
      //  drawingFilters, specificationFilters
    ],
    queryFn: () =>
      bidItemAPI.getSheetsAndSpecs(
        Number(selectedProjectId),
        // drawingFilters,
        'all',
        // specificationFilters,
        'all',
        0,
        1000
      ),
    select: res => res.data,
    enabled: false
  });

  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: false,
    select: res => res.data
  });

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs || !drawingsAndSpecsData) return;
    const drawings = drawingsAndSpecsData?.bidItemSheets;
    const specs = drawingsAndSpecsData?.bidItemSpecificationDTOList;
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (drawings) {
      drawingThumbnails = drawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setDrawingsThumbnails(drawingThumbnails);
    }
    if (specs) {
      specificationThumbnails = specs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setSpecificationsThumbnails(specificationThumbnails);
    }
  }, [drawingsAndSpecsData, isFetchingDrawingsAndSpecs]);

  useEffect(() => {
    if (isFetching || !searchTextData) return;

    const data = (searchTextData?.data || [])?.map(item => {
      const doc =
        documentsData?.documents?.find(doc => doc?.id === Number(item?.documentId)) || null;
      const thumbnail = getThumbnailS3Url(
        currentUser?.companyId || 0,
        Number(selectedProjectId),
        Number(item.documentId),
        Number(item.pageNumber) || 0
      );

      return {
        document: doc,
        sheet: item?.sheets?.[0],
        thumbnail,
        specification: item?.specifications?.[0]
      };
    });
    setDrawingsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
      )
    );
    setSpecificationsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
      )
    );
  }, [searchTextData, isFetching]);

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      return;
    }
    if (selectedProjectId) {
      if (searchText.length > 0) {
        fetchSearchTextData();
      } else {
        fetchDrawingsAndSpecs();
      }
    }
  }, [searchText]);

  useEffect(() => {
    setIsSearchTextAvailable(!!searchText);
  }, []);

  // Initialize modal data when modal opens
  useEffect(() => {
    if (
      isSearchModalVisible &&
      modalDrawingsThumbnails.length === 0 &&
      modalSpecificationsThumbnails.length === 0
    ) {
      // Load initial data for modal
      setModalDrawingsThumbnails(drawingsThumbnails);
      setModalSpecificationsThumbnails(specificationsThumbnails);
    }
  }, [
    isSearchModalVisible,
    drawingsThumbnails,
    specificationsThumbnails,
    modalDrawingsThumbnails.length,
    modalSpecificationsThumbnails.length
  ]);

  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length === 0) {
      fetchDrawingsAndSpecs();
    }
    setSearchText(e.target.value);
  };

  const onSearchHandler = () => {
    setIsSearchTextAvailable(!!searchText);
    if (searchText.length > 0) fetchSearchTextData();
    else fetchDrawingsAndSpecs();
  };

  const handleModalSearch = (modalSearchText: string) => {
    // Perform search for modal with isolated state
    if (modalSearchText.length > 0) {
      openSearchAPI
        .searchByText(Number(projectId), modalSearchText, undefined, undefined, 'all', 'all')
        .then(response => {
          const data = (response?.data || [])?.map(item => {
            const doc =
              documentsData?.documents?.find(doc => doc?.id === Number(item?.documentId)) || null;
            const thumbnail = getThumbnailS3Url(
              currentUser?.companyId || 0,
              Number(selectedProjectId),
              Number(item.documentId),
              Number(item.pageNumber) || 0
            );

            return {
              document: doc,
              sheet: item?.sheets?.[0],
              thumbnail,
              specification: item?.specifications?.[0]
            };
          });

          setModalDrawingsThumbnails(
            data?.filter(
              item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
            ) || []
          );
          setModalSpecificationsThumbnails(
            data?.filter(
              item =>
                item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
            ) || []
          );
        })
        .catch(error => {
          console.error('Modal search error:', error);
          setModalDrawingsThumbnails([]);
          setModalSpecificationsThumbnails([]);
        });
    } else {
      // Load all documents when search is empty
      fetchDrawingsAndSpecs().then(() => {
        setModalDrawingsThumbnails(drawingsThumbnails);
        setModalSpecificationsThumbnails(specificationsThumbnails);
      });
    }
  };

  const handleCloseSearchModal = () => {
    setIsSearchModalVisible(false);
    // Reset modal state
    setModalDrawingsThumbnails([]);
    setModalSpecificationsThumbnails([]);
  };
  if (isCreatingThumbnails) return <Spin indicator={<LoadingOutlined spin />} size='large' />;

  return (
    <Container>
      <Content>
        <SearchHeaderContainer>
          <Flex align='center' justify='space-between' gap={24}>
            <SearchContainer align='center' gap={20}>
              <SearchBar
                placeholder='Search here'
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={handleSearchChange}
                size='large'
                variant='outlined'
                onPressEnter={onSearchHandler}
              />
              <Button
                icon={<SearchOutlined />}
                type='primary'
                size='large'
                style={{ width: 'fit-content' }}
                onClick={() => setIsSearchModalVisible(true)}
              >
                Search
              </Button>
            </SearchContainer>
          </Flex>
          {isSearchTextAvailable && !isFetchingDrawingsAndSpecs && !isFetching && (
            <SearchResultsText>
              <BoldText>{drawingsThumbnails.length + specificationsThumbnails.length}</BoldText>{' '}
              items found{' '}
              <span>
                - <BoldText>{drawingsThumbnails?.length}</BoldText> drawings{' '}
              </span>
              <span>
                and <BoldText>{specificationsThumbnails?.length}</BoldText> specifications
              </span>
            </SearchResultsText>
          )}
        </SearchHeaderContainer>
        <Flex align='center' gap={16}>
          <HeaderText>
            {activeTab === DocumentTabName.drawings
              ? selectedThumbnailInfo?.sheet?.title
              : selectedThumbnailInfo?.specification?.specificationTitle}
          </HeaderText>
        </Flex>

        <PDFViewerContainer>
          {isLoadingDocument && (
            <LoaderContainer>
              <Image
                src={selectedThumbnailInfo?.thumbnail}
                alt={
                  selectedThumbnailInfo?.sheet?.title ||
                  selectedThumbnailInfo?.specification?.specificationTitle
                }
              />
              <Spin className='loader' indicator={<LoadingOutlined spin />} size='large' />
            </LoaderContainer>
          )}

          {selectedThumbnailInfo ? (
            <PDFViewer isThumbnailsDrawerMinimised={true} />
          ) : (
            <div>Please select a thumbnail to preview</div>
          )}
        </PDFViewerContainer>
      </Content>

      <SearchModal
        visible={isSearchModalVisible}
        onClose={handleCloseSearchModal}
        drawingsThumbnails={modalDrawingsThumbnails}
        specificationsThumbnails={modalSpecificationsThumbnails}
        onSearch={handleModalSearch}
        isLoading={isFetching}
      />
    </Container>
  );
};

export default DocumentViewer;
