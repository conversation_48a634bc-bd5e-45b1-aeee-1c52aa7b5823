import { LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Button, Flex, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  DocumentDTOInputDocumentTypeEnum,
  DocumentWithPresignedUrlDTOInputDocumentTypeEnum
} from 'src/api';
import { bidItemAPI, openSearchAPI, projectAPI } from 'src/api/apiClient';
import { queryKeys } from 'src/modules/utils/constant';
import { getThumbnailS3Url, ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import './DocumentViewer.css';
import {
  Container,
  Content,
  HeaderText,
  PDFViewerContainer,
  LoaderContainer,
  Image,
  SearchHeaderContainer,
  SearchContainer,
  SearchBar
} from './DocumentViewer style';
import SearchModal from './SearchModal';
import { DocumentTabName } from '../../scopes/document-view/DocumentThumbnailsView';
import PDFViewer from '../../scopes/document-view/PDFViewer';
import useScopesStore from '../../scopes/store/useScopesStore';

interface DocumentViewerProps {
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ activeTab, setActiveTab }) => {
  const { projectId } = useParams();

  const [searchText, setSearchText] = useState('');
  const [isSearchModalVisible, setIsSearchModalVisible] = useState<boolean>(false);

  const {
    selectedThumbnailInfo,
    isCreatingThumbnails,
    isLoadingDocument,
    setSpecificationsThumbnails,
    setDrawingsThumbnails
  } = useScopesStore();
  const { selectedProjectId, currentUser, selectedVersion } = useGlobalStore();
  const { scopeId } = useParams();

  const { data: searchTextData, isFetching } = useQuery({
    queryKey: [queryKeys.searchTextData, projectId, selectedVersion, scopeId],
    queryFn: () =>
      openSearchAPI.searchByText(
        Number(projectId),
        undefined,
        undefined,
        undefined,
        scopeId,
        scopeId
      ),
    enabled: false
  });

  useEffect(() => {
    if (
      selectedThumbnailInfo?.document?.inputDocumentType ===
      DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Specification
    )
      setActiveTab(DocumentTabName.specs);
  }, []);

  const { data: drawingsAndSpecsData, isFetching: isFetchingDrawingsAndSpecs } = useQuery({
    queryKey: [queryKeys.drawingsAndSpecs, selectedProjectId],
    queryFn: () =>
      bidItemAPI.getSheetsAndSpecs(Number(selectedProjectId), scopeId, scopeId, 0, 1000),
    select: res => res.data,
    enabled: false
  });

  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: false,
    select: res => res.data
  });

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs || !drawingsAndSpecsData) return;
    const drawings = drawingsAndSpecsData?.bidItemSheets;
    const specs = drawingsAndSpecsData?.bidItemSpecificationDTOList;
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (drawings) {
      drawingThumbnails = drawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setDrawingsThumbnails(drawingThumbnails);
    }
    if (specs) {
      specificationThumbnails = specs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setSpecificationsThumbnails(specificationThumbnails);
    }
  }, [drawingsAndSpecsData, isFetchingDrawingsAndSpecs]);

  useEffect(() => {
    if (isFetching || !searchTextData) return;

    const data = (searchTextData?.data || [])?.map(item => {
      const doc =
        documentsData?.documents?.find(doc => doc?.id === Number(item?.documentId)) || null;
      const thumbnail = getThumbnailS3Url(
        currentUser?.companyId || 0,
        Number(selectedProjectId),
        Number(item.documentId),
        Number(item.pageNumber) || 0
      );

      return {
        document: doc,
        sheet: item?.sheets?.[0],
        thumbnail,
        specification: item?.specifications?.[0]
      };
    });
    setDrawingsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
      )
    );
    setSpecificationsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
      )
    );
  }, [searchTextData, isFetching]);

  const handleCloseSearchModal = () => {
    setIsSearchModalVisible(false);
  };

  const handleSearch = (text: string) => {
    setSearchText(text);
  };

  if (isCreatingThumbnails) return <Spin indicator={<LoadingOutlined spin />} size='large' />;

  return (
    <Container>
      <Content>
        <SearchHeaderContainer>
          <Flex align='center' justify='space-between' gap={24}>
            <SearchBar
              placeholder='Search here'
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => handleSearch(e.target.value)}
              size='large'
              variant='outlined'
              onPressEnter={() => setIsSearchModalVisible(true)}
            />
          </Flex>
        </SearchHeaderContainer>
        <Flex align='center' gap={16}>
          <HeaderText>
            {activeTab === DocumentTabName.drawings
              ? selectedThumbnailInfo?.sheet?.title
              : selectedThumbnailInfo?.specification?.specificationTitle}
          </HeaderText>
        </Flex>

        <PDFViewerContainer>
          {isLoadingDocument && (
            <LoaderContainer>
              <Image
                src={selectedThumbnailInfo?.thumbnail}
                alt={
                  selectedThumbnailInfo?.sheet?.title ||
                  selectedThumbnailInfo?.specification?.specificationTitle
                }
              />
              <Spin className='loader' indicator={<LoadingOutlined spin />} size='large' />
            </LoaderContainer>
          )}

          {selectedThumbnailInfo ? (
            <PDFViewer isThumbnailsDrawerMinimised={true} />
          ) : (
            <div>Please select a thumbnail to preview</div>
          )}
        </PDFViewerContainer>
      </Content>
      {isSearchModalVisible && (
        <SearchModal
          visible={isSearchModalVisible}
          onClose={handleCloseSearchModal}
          isLoading={isFetching}
          searchText={searchText}
          handleSearch={handleSearch}
        />
      )}
    </Container>
  );
};

export default DocumentViewer;
