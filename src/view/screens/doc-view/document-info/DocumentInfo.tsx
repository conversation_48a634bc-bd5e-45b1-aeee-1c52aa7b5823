import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DocumentWithPresignedUrlDTOInputDocumentTypeEnum } from 'src/api';
import { bidItemAPI, projectAPI, scopeAPI } from 'src/api/apiClient';
import { DocumentTabName, queryKeys } from 'src/modules/utils/constant';
import { getThumbnailS3Url, ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import useGlobalStore from 'src/store/useGlobalStore';
import { DocViewContainer, SectionA, SectionB, SectionC } from './DocumentInfo.style';
import useScopesStore from '../../scopes/store/useScopesStore';
import DocumentSelectionPanel from '../document-selection-panel/DocumentSelectionPanel';
import DocumentViewer from '../document-viewer/DocumentViewer';
import ScopesSpecificationsPanel from '../scope-specifications-panel/ScopesSpecificationPanel';

const DocumentInfo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<DocumentTabName>(DocumentTabName.drawings);
  const { selectedProjectId, selectedVersion, currentUser } = useGlobalStore();
  const {
    isDocumentView,
    setDrawingsThumbnails,
    setSpecificationsThumbnails,
    setIsCreatingThumbnails,
    selectedThumbnailInfo,
    setSelectedThumbnailInfo
  } = useScopesStore();

  const { scopeId } = useParams();
  const navigate = useNavigate();

  const { data: scopesList } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId, selectedVersion],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        String(selectedVersion?.version),
        0,
        10,
        ['division,asc']
      ),
    select: res => res.data,
    enabled: !!selectedProjectId,
    retry: false
  });

  const { data: currentScopeDrawingsAndScecs, isFetching: isFetchingDrawingsAndSpecs } = useQuery({
    queryKey: [queryKeys.currentScopeDrawingsAndSpecs, selectedProjectId, scopeId],
    queryFn: () => bidItemAPI.getSheetsAndSpecs(Number(selectedProjectId), 'all', 'all', 0, 1000),
    select: res => res.data,
    enabled: !!selectedProjectId && !!scopeId
  });

  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: !!selectedProjectId,
    select: res => res.data
  });

  useEffect(() => {
    if (!scopeId && scopesList?.content && scopesList?.content?.length > 0) {
      navigate(String(scopesList.content[0]?.id));
    }
  }, [scopeId, scopesList]);

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs) {
      setIsCreatingThumbnails(true);
      return;
    }
    const currentScopeDrawings = currentScopeDrawingsAndScecs?.bidItemSheets || [];
    const currentScopeSpecs = currentScopeDrawingsAndScecs?.bidItemSpecificationDTOList || [];
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (currentScopeDrawings) {
      drawingThumbnails = currentScopeDrawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setDrawingsThumbnails(drawingThumbnails);
    }
    if (currentScopeSpecs) {
      specificationThumbnails = currentScopeSpecs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setSpecificationsThumbnails(specificationThumbnails);
    }
    const setFallbackThumbnail = () => {
      if (drawingThumbnails.length > 0) setSelectedThumbnailInfo(drawingThumbnails[0]);
      else if (specificationThumbnails.length > 0)
        setSelectedThumbnailInfo(specificationThumbnails[0]);
      else setSelectedThumbnailInfo(null);
    };

    if (!isDocumentView || selectedThumbnailInfo === null) {
      setFallbackThumbnail();
    } else {
      const docType = selectedThumbnailInfo?.document?.inputDocumentType;
      if (docType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
        const found = drawingThumbnails.find(
          thumbnail => thumbnail.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId
        );
        if (!found) setFallbackThumbnail();
      } else {
        const found = specificationThumbnails.find(
          thumbnail =>
            thumbnail.specification?.specificationId ===
            selectedThumbnailInfo?.specification?.specificationId
        );
        if (!found) setFallbackThumbnail();
      }
    }
    setIsCreatingThumbnails(false);
  }, [currentScopeDrawingsAndScecs, isFetchingDrawingsAndSpecs]);

  return (
    <DocViewContainer>
      <SectionA>
        <DocumentSelectionPanel activeTab={activeTab} setActiveTab={setActiveTab} />
      </SectionA>
      <SectionB>
        <DocumentViewer activeTab={activeTab} setActiveTab={setActiveTab} />
      </SectionB>
      <SectionC>
        <ScopesSpecificationsPanel />
      </SectionC>
    </DocViewContainer>
  );
};

export default DocumentInfo;
