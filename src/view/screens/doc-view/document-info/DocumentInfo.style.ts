import { Content } from 'antd/es/layout/layout';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const DocViewContainer = styled(Content)`
  display: flex;
  height: calc(100vh - 60px);
  background: ${themeTokens.pageBg};
`;

export const SectionA = styled.div`
  width: 300px;
  min-width: 300px;
  background: ${themeTokens.textLight};
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
`;

export const SectionB = styled.div`
  flex: 1;
  background: ${themeTokens.textLight};
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

export const SectionC = styled.div`
  width: 350px;
  min-width: 350px;
  background: ${themeTokens.textLight};
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
`;
