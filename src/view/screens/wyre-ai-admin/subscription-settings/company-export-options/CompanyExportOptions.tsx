import { Button, Form, message, Checkbox, Row } from 'antd';
import React from 'react';
import {
  ExportLabel,
  ExportOptionsBox,
  ExportRow,
  StyledInput
} from './CompanyExportOptions.style';

const CompanyExportOptions: React.FC = () => {
  const [form] = Form.useForm();

  // Simulate API call
  const handleSubmit = async () => {
    message.success('Subscription settings updated!');
  };

  return (
    <ExportOptionsBox>
      <Form layout='vertical' form={form} onFinish={handleSubmit}>
        <Form.Item name='exportCompanyName'>
          <ExportRow>
            <ExportLabel>Company Name</ExportLabel>
            <StyledInput defaultValue='Company Xyz' disabled />
          </ExportRow>
        </Form.Item>
        <ExportRow>
          <ExportLabel>Building Connected</ExportLabel>
          <Checkbox />
        </ExportRow>
        <ExportRow>
          <ExportLabel>Company Template</ExportLabel>
          <Checkbox />
        </ExportRow>
        <ExportRow>
          <ExportLabel>Excel</ExportLabel>
          <Checkbox />
        </ExportRow>
        <Row justify='end'>
          <Button type='primary' htmlType='submit'>
            Update
          </Button>
        </Row>
      </Form>
    </ExportOptionsBox>
  );
};

export default CompanyExportOptions;
