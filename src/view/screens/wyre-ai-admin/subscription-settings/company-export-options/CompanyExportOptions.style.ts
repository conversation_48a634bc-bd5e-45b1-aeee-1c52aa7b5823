import { Input } from 'antd';
import styled from 'styled-components';

export const ExportOptionsBox = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #fff;
  padding: 32px 40px 32px 40px;
  margin-top: 24px;
  min-width: 400px;
  max-width: 600px;
`;

export const ExportRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  font-size: 17px;
  font-weight: 500;
  gap: 24px;
`;

export const ExportLabel = styled.span`
  flex: 1;
`;

export const StyledInput = styled(Input)`
  width: 300px;
`;
