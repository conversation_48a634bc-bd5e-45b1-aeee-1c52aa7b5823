import { ArrowLeftOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SwitchCompanyModal from 'src/modules/common/SwitchCompanyModal';
import { appRoutes } from 'src/modules/utils/constant';
import useGlobalStore from 'src/store/useGlobalStore';
import CompanyExportOptions from './company-export-options/CompanyExportOptions';
import CompanySettings from './company-settings/CompanySettings';
import { Container, BackButton, Header, HeaderLeft, PageTitle } from './SubscriptionSettings.style';

const { TabPane } = Tabs;

const SubscriptionSettings: React.FC = () => {
  const navigate = useNavigate();
  const { activeCompany } = useGlobalStore();
  const [showSwitchModal, setShowSwitchModal] = React.useState(false);

  useEffect(() => {
    if (!activeCompany) setShowSwitchModal(true);
    else setShowSwitchModal(false);
  }, [activeCompany]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  if (!activeCompany) {
    return <SwitchCompanyModal open={showSwitchModal} onClose={() => {}} />;
  }

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Subscription Settings</PageTitle>
        </HeaderLeft>
      </Header>
      <Tabs defaultActiveKey='1'>
        <TabPane tab='Company Settings' key='1'>
          <CompanySettings activeCompany={activeCompany} />
        </TabPane>
        <TabPane tab='Company Export Options' key='2'>
          <CompanyExportOptions />
        </TabPane>
      </Tabs>
      <SwitchCompanyModal open={showSwitchModal} onClose={() => setShowSwitchModal(false)} />
    </Container>
  );
};

export default SubscriptionSettings;
