import { Form } from 'antd';
import styled from 'styled-components';

export const Section = styled.div`
  border-radius: 8px;
  display: flex;
  gap: 24px;
  flex-direction: column;
`;

export const SubscriptionBox = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 14px;
  background: #fafbfc;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  transition:
    box-shadow 0.2s,
    border-color 0.2s;
  &:hover {
    box-shadow: 0 4px 24px 0 rgba(60, 60, 60, 0.12);
    border-color: #ff6a2f;
  }
`;

export const FeaturesIntegrationsWrapper = styled.div`
  display: flex;
  gap: 32px;
  height: 100%;
  justify-content: center;
`;

export const FeatureBox = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 14px;
  background: #fafbfc;
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 4px 24px 0 rgba(60, 60, 60, 0.12);
    border-color: #ff6a2f;
  }
`;

export const FeatureBoxTitle = styled.div`
  font-weight: 600;
  font-size: 16px;
  color: #222;
  letter-spacing: 0.5px;
`;

export const Label = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

export const StyledForm = styled(Form)`
  display: flex;
  gap: 24px;
  flex-direction: column;
`;

export const ProjectLimitRow = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const SectionRow = styled.div`
  display: flex;
  gap: 32px;
`;
