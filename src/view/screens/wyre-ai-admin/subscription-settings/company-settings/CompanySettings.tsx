import { PlusOutlined } from '@ant-design/icons';
import {
  Input,
  Button,
  DatePicker,
  Radio,
  InputNumber,
  Switch,
  Space,
  Row,
  Col,
  Form,
  message,
  Flex
} from 'antd';
import React, { useState } from 'react';
import {
  FeatureBox,
  FeatureBoxTitle,
  FeaturesIntegrationsWrapper,
  Label,
  ProjectLimitRow,
  Section,
  SectionRow,
  StyledForm,
  SubscriptionBox
} from './CompanySettings.style';

interface CompanySettingsProps {
  activeCompany: { id: number; name: string };
}

const CompanySettings: React.FC<CompanySettingsProps> = ({ activeCompany }) => {
  const [domains, setDomains] = useState<string[]>(['.xyz.com']);
  const [newDomain, setNewDomain] = useState('');
  const [form] = Form.useForm();

  // Add Multi Domain
  const handleAddDomain = () => {
    if (newDomain && !domains.includes(newDomain)) {
      setDomains([...domains, newDomain]);
      setNewDomain('');
      // Optionally update form value for domains
      form.setFieldValue('domains', [...domains, newDomain]);
    }
  };

  const projectLimitChange = (value: any) => {
    form.setFieldValue('projectsAvailable', value - form.getFieldValue('projectsUsed'));
  };

  // Simulate API call
  const handleSubmit = async (values: any) => {
    message.success('Subscription settings updated!');
  };

  return (
    <StyledForm
      form={form}
      layout='vertical'
      initialValues={{
        companyEmailDomain: '.xyz.com',
        domains,
        subscriptionType: 'Enterprise',
        companyStatus: 'Active',
        projectLimit: 10000,
        projectsUsed: 2500,
        projectsAvailable: 7500,
        versionLimit: 10,
        features: {
          companyAdmin: true,
          enterpriseDashboard: true,
          scopeMatrix: true,
          apiAccess: true
        },
        integrations: {
          procore: true,
          autodesk: true
        }
      }}
      onFinish={handleSubmit}
    >
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item label='Company Name'>
            <Input value={activeCompany?.name} disabled />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label='Company Email Domain'
            name='companyEmailDomain'
            rules={[{ required: true }]}
          >
            <Space>
              <Input
                placeholder='.xyz.com'
                value={newDomain}
                onChange={e => setNewDomain(e.target.value)}
              />
              <Button icon={<PlusOutlined />} onClick={handleAddDomain} type='primary'>
                Add Multi Domain
              </Button>
            </Space>
          </Form.Item>
          <Form.Item name='domains' initialValue={domains} hidden>
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Section>
        <SectionRow>
          <Col flex={2}>
            <SubscriptionBox>
              <Label>Subscription</Label>
              <Form.Item name='subscriptionType'>
                <Radio.Group>
                  <Radio value='Enterprise'>Enterprise</Radio>
                  <Radio value='Non-Enterprise'>Non-Enterprise</Radio>
                </Radio.Group>
              </Form.Item>
              <ProjectLimitRow>
                <Col span={12}>
                  <Form.Item label='Start Date*' name='startDate' rules={[{ required: true }]}>
                    <DatePicker />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label='End Date*' name='endDate' rules={[{ required: true }]}>
                    <DatePicker />
                  </Form.Item>
                </Col>
              </ProjectLimitRow>
              <Label>Set Company As</Label>
              <Form.Item name='companyStatus'>
                <Radio.Group>
                  <Radio value='Active'>Active</Radio>
                  <Radio value='Disable'>Disable</Radio>
                  <Radio value='Delinquent'>Delinquent</Radio>
                </Radio.Group>
              </Form.Item>
              <Label>Project Limit</Label>
              <ProjectLimitRow>
                <Col>
                  <Form.Item name='projectLimit' noStyle>
                    <InputNumber min={1} onChange={projectLimitChange} />
                  </Form.Item>
                </Col>
                <Flex gap={12} align='center'>
                  <span>Used</span>
                  <Form.Item name='projectsUsed' noStyle>
                    <InputNumber min={0} disabled />
                  </Form.Item>
                </Flex>
                <Flex gap={12} align='center'>
                  <span>Available</span>
                  <Form.Item name='projectsAvailable' noStyle>
                    <InputNumber min={0} disabled />
                  </Form.Item>
                </Flex>
              </ProjectLimitRow>
              <Label>Version Limit</Label>
              <Form.Item name='versionLimit'>
                <InputNumber min={1} />
              </Form.Item>
            </SubscriptionBox>
          </Col>
          <Col flex={2}>
            <FeaturesIntegrationsWrapper>
              <FeatureBox>
                <FeatureBoxTitle>Enable Features</FeatureBoxTitle>
                <Row align='middle' justify='space-between'>
                  <span>Company Admin</span>
                  <Form.Item name={['features', 'companyAdmin']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
                <Row align='middle' justify='space-between'>
                  <span>Enterprise Dashboard</span>
                  <Form.Item name={['features', 'enterpriseDashboard']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
                <Row align='middle' justify='space-between'>
                  <span>Scope Matrix</span>
                  <Form.Item name={['features', 'scopeMatrix']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
                <Row align='middle' justify='space-between'>
                  <span>API Access</span>
                  <Form.Item name={['features', 'apiAccess']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
              </FeatureBox>
              <FeatureBox>
                <FeatureBoxTitle>Enable Integrations</FeatureBoxTitle>
                <Row align='middle' justify='space-between'>
                  <span>Procore</span>
                  <Form.Item name={['integrations', 'procore']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
                <Row align='middle' justify='space-between'>
                  <span>Autodesk</span>
                  <Form.Item name={['integrations', 'autodesk']} valuePropName='checked'>
                    <Switch />
                  </Form.Item>
                </Row>
              </FeatureBox>
            </FeaturesIntegrationsWrapper>
          </Col>
        </SectionRow>
        <Row justify='end'>
          <Button type='primary' htmlType='submit'>
            Update
          </Button>
        </Row>
      </Section>
    </StyledForm>
  );
};

export default CompanySettings;
