import { ArrowLeftOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Flex, Tag, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import ExportFile from 'src/modules/common/exportFile';
import { themeTokens } from 'src/theme/tokens';

import {
  FilterDropdownContainer,
  BackButton,
  Container,
  FilterButton,
  FilterButtonContainer,
  FilterInput,
  GlobalSearchInput,
  Header,
  PageTitle,
  StyledTable,
  StyledTableContainer,
  TooltipContent,
  HeaderContainer
} from './CompanyManagement.style';
import { queryKeys, appRoutes } from '../../../../modules/utils/constant';
import { formatDate } from '../../../../modules/utils/util';

export interface ExtendedCompanyDTO extends CompanyDTO {
  userCount?: number;
  projectCount?: number;
  lastActivity?: string;
}

const CompanyManagement: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  // Fetch companies data
  const { data: companiesData, isLoading } = useQuery({
    queryKey: [
      queryKeys.companiesList,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      companyAPI.getAllCompanies(
        currentPage - 1,
        pageSize,
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined,
        debouncedSearchText || undefined,
        columnFilters.industry || undefined,
        columnFilters.subscriptionStatus || undefined
      ),
    select: response => response.data
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    if (filters) {
      const newFilters: Record<string, any> = {};
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilters[key] = filters[key][0]; // Take first filter value
        }
      });
      setColumnFilters(newFilters);
      setCurrentPage(1); // Reset to first page when filtering
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getSubscriptionStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return themeTokens.successGreen;
      case 'TRIAL':
        return themeTokens.warningOrange;
      case 'SUSPENDED':
        return themeTokens.dangerRed;
      default:
        return themeTokens.textGray;
    }
  };

  const columns: ColumnsType<ExtendedCompanyDTO> = useMemo(
    () => [
      {
        title: 'Company ID',
        dataIndex: 'id',
        key: 'id',
        width: 120,
        sorter: true
      },
      {
        title: 'Company Name',
        dataIndex: 'name',
        key: 'name',
        width: 170,
        sorter: true,
        ...getColumnSearchProps('name', 'company name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company Admin Name',
        dataIndex: 'adminName',
        key: 'adminName',
        width: 210,
        sorter: true,
        ...getColumnSearchProps('name', 'Company admin name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company Admin Email',
        dataIndex: 'adminEmail',
        key: 'adminEmail',
        width: 190,
        sorter: true,
        render: (adminEmail: string) => adminEmail || '-'
      },
      {
        title: 'Company Active',
        dataIndex: 'active',
        key: 'active',
        width: 160,
        sorter: true,
        render: (active: string) => active || '-'
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 130,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Email Domain',
        dataIndex: 'emailDomain',
        key: 'emailDomain',
        sorter: true,
        width: 140,
        render: (emailDomain: string) => emailDomain || '-'
      },
      {
        title: 'Multi Domain Emails',
        dataIndex: 'multiDomainEmails',
        key: 'multiDomainEmails',
        sorter: true,
        width: 180,
        render: (multiDomainEmails: string) => multiDomainEmails || '-'
      },
      {
        title: 'SSO Flag',
        dataIndex: 'SSOFlag',
        key: 'SSOFlag',
        sorter: true,
        width: 110,
        render: (SSOFlag: string) => SSOFlag || '-'
      },
      {
        title: 'Role',
        dataIndex: 'role',
        sorter: true,
        key: 'role',
        width: 80,
        render: (role: string) => role || '-'
      }
    ],
    [getColumnSearchProps]
  );

  const exportColumns = [
    { label: 'Company ID', key: 'id' },
    { label: 'Company Name', key: 'name' },
    { label: 'Company Admin Name', key: 'adminName' },
    { label: 'Company Admin Email', key: 'adminEmail' },
    { label: 'Company Active', key: 'active' },
    { label: 'Created Date', key: 'createdAt' },
    { label: 'Email Domain', key: 'emailDomain' },
    { label: 'Multi Domain Emails', key: 'multiDomainEmails' },
    { label: 'SSO Flag', key: 'SSOFlag' },
    { label: 'Role', key: 'role' }
  ];

  return (
    <Container>
      <HeaderContainer>
        <Header>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Company Management</PageTitle>
        </Header>
        <Flex gap={24} align='center'>
          <GlobalSearchInput
            placeholder='Search across all companies...'
            allowClear
            enterButton={<SearchOutlined />}
            size='large'
            onSearch={handleGlobalSearch}
            onChange={e => {
              if (!e.target.value) {
                handleGlobalSearch('');
              }
            }}
          />
          <ExportFile
            data={
              companiesData?.content?.map(company => ({
                ...company,
                createdAt: formatDate(company.createdAt)
              })) ||
              [] ||
              []
            }
            filename='companiesList'
            columns={exportColumns}
          />
        </Flex>
      </HeaderContainer>
      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={companiesData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          rowClassName={() => 'editable-row'}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: companiesData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} companies`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ y: 'calc(100vh - 320px)' }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default CompanyManagement;
