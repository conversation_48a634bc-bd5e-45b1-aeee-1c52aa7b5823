import { Button, Input, Space, Switch, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

export const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  background: ${themeTokens.pageBg};
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
`;

export const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

export const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

export const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

export const FilterButtonContainer = styled(Space)`
  /* Styled Space component for filter buttons */
`;

export const FilterButton = styled(Button)`
  width: 90px;
`;

export const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const EmailTooltipContent = styled.span`
  display: block;
  max-width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

export const ModalContainer = styled.div`
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
`;

export const ModalTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
`;

export const ModalDescription = styled.p`
  font-size: 14px;
`;

export const StyledTable = styled(Table<any>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;
