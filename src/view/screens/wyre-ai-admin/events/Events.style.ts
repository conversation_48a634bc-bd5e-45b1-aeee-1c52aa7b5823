import { Button, Input, Space, Table, Select, SelectProps } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

export const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  background: ${themeTokens.pageBg};
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
`;

export const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

export const ExportButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const FiltersContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  align-items: center;
`;

export const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const FilterLabel = styled.span`
  font-size: 12px;
  font-weight: 500;
  color: ${themeTokens.textGray};
  text-transform: uppercase;
`;

export const FilterSelect = styled(Select)<SelectProps<string>>`
  width: 180px;
`;

export const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

export const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

export const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

export const FilterButtonContainer = styled(Space)`
  /* Styled Space component for filter buttons */
`;

export const FilterButton = styled(Button)`
  width: 90px;
`;

export const TooltipContent = styled.span`
  display: block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const StatusTag = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'success':
        return '#52c41a';
      case 'failed':
      case 'error':
        return '#ff4d4f';
      case 'pending':
        return '#faad14';
      default:
        return '#1890ff';
    }
  }};
  background-color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'success':
        return '#f6ffed';
      case 'failed':
      case 'error':
        return '#fff2f0';
      case 'pending':
        return '#fffbe6';
      default:
        return '#e6f7ff';
    }
  }};
  border: 1px solid
    ${props => {
      switch (props.status.toLowerCase()) {
        case 'success':
          return '#b7eb8f';
        case 'failed':
        case 'error':
          return '#ffccc7';
        case 'pending':
          return '#ffe58f';
        default:
          return '#91d5ff';
      }
    }};
`;

export const UserTypeTag = styled.span<{ userType: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: ${props => (props.userType.toLowerCase() === 'paid' ? '#52c41a' : '#faad14')};
  background-color: ${props => (props.userType.toLowerCase() === 'paid' ? '#f6ffed' : '#fffbe6')};
  border: 1px solid ${props => (props.userType.toLowerCase() === 'paid' ? '#b7eb8f' : '#ffe58f')};
`;

export const StyledTable = styled(Table<any>)`
  .editable-row {
    transition: opacity 0.3s;
  }

  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;
