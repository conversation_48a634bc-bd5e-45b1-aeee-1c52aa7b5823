import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import { Flex, Tooltip, notification } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ExportFile from 'src/modules/common/exportFile';
import { themeTokens } from 'src/theme/tokens';
import {
  Con<PERSON>er,
  Header,
  BackButton,
  PageTitle,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  TooltipContent,
  StatusTag,
  UserTypeTag,
  StyledTable,
  GlobalSearchInput,
  HeaderContainer
} from './Events.style';
import { appRoutes } from '../../../../modules/utils/constant';
import { formatDate } from '../../../../modules/utils/util';

// Types for Event data
interface EventDTO {
  id: number;
  userName: string;
  userEmail: string;
  userType: 'Paid' | 'Unpaid';
  moduleName: string;
  actionType: string;
  status: 'Success' | 'Failed' | 'Pending';
  description: string;
  companyName: string;
  projectName?: string;
  createdTime: string;
  ipAddress: string;
}

const Events: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Mock data for now - replace with actual API call
  const mockEvents: EventDTO[] = [
    {
      id: 1,
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      userType: 'Paid',
      moduleName: 'Projects',
      actionType: 'Create',
      status: 'Success',
      description: 'Created new project "Office Building A"',
      companyName: 'ABC Construction',
      projectName: 'Office Building A',
      createdTime: '2024-01-15T10:30:00Z',
      ipAddress: '*************'
    },
    {
      id: 2,
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      userType: 'Unpaid',
      moduleName: 'Scopes',
      actionType: 'Update',
      status: 'Success',
      description: 'Updated scope "Electrical Work"',
      companyName: 'XYZ Engineering',
      projectName: 'Residential Complex',
      createdTime: '2024-01-15T09:15:00Z',
      ipAddress: '*************'
    },
    {
      id: 3,
      userName: 'Mike Johnson',
      userEmail: '<EMAIL>',
      userType: 'Paid',
      moduleName: 'Users',
      actionType: 'Login',
      status: 'Success',
      description: 'User logged into the system',
      companyName: 'DEF Contractors',
      createdTime: '2024-01-15T08:45:00Z',
      ipAddress: '*************'
    }
  ];

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleExport = () => {
    // TODO: Implement actual export functionality
    notification.success({
      message: 'Export Started',
      description:
        "Your activity log export has been initiated. You will receive an email when it's ready."
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      )
    }),
    []
  );

  const columns: ColumnsType<EventDTO> = useMemo(
    () => [
      {
        title: 'User Name',
        dataIndex: 'userName',
        key: 'userName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('userName', 'user name'),
        render: (userName: string) => (
          <Tooltip title={userName}>
            <TooltipContent>{userName}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'User Email',
        dataIndex: 'userEmail',
        key: 'userEmail',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('userEmail', 'email'),
        render: (userEmail: string) => (
          <Tooltip title={userEmail}>
            <TooltipContent>{userEmail}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'User Type',
        dataIndex: 'userType',
        key: 'userType',
        width: 100,
        sorter: true,
        render: (userType: string) => <UserTypeTag userType={userType}>{userType}</UserTypeTag>
      },
      {
        title: 'Module',
        dataIndex: 'moduleName',
        key: 'moduleName',
        width: 120,
        sorter: true,
        ...getColumnSearchProps('moduleName', 'module'),
        render: (moduleName: string) => moduleName
      },
      {
        title: 'Action Type',
        dataIndex: 'actionType',
        key: 'actionType',
        width: 100,
        sorter: true,
        render: (actionType: string) => actionType
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        sorter: true,
        render: (status: string) => <StatusTag status={status}>{status}</StatusTag>
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
        width: 250,
        ...getColumnSearchProps('description', 'description'),
        render: (description: string) => (
          <Tooltip title={description}>
            <TooltipContent>{description}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <TooltipContent>{companyName}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Name',
        dataIndex: 'projectName',
        key: 'projectName',
        width: 150,
        render: (projectName: string) => projectName || '-'
      },
      {
        title: 'Created Time',
        dataIndex: 'createdTime',
        key: 'createdTime',
        width: 150,
        sorter: true,
        render: (createdTime: string) => formatDate(createdTime)
      },
      {
        title: 'IP Address',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        width: 120,
        render: (ipAddress: string) => ipAddress
      }
    ],
    [getColumnSearchProps]
  );

  const exportColumns = [
    { label: 'User Name', key: 'userName' },
    { label: 'User Email', key: 'userEmail' },
    { label: 'User Type', key: 'userType' },
    { label: 'Module', key: 'moduleName' },
    { label: 'Action Type', key: 'actionType' },
    { label: 'Status', key: 'status' },
    { label: 'Description', key: 'description' },
    { label: 'Company', key: 'companyName' },
    { label: 'Project Name', key: 'projectName' },
    { label: 'Created Time', key: 'createdTime' },
    { label: 'IP Address', key: 'ipAddress' }
  ];

  return (
    <Container>
      <HeaderContainer>
        <Header>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Events</PageTitle>
        </Header>
        <Flex gap={24} align='center'>
          <GlobalSearchInput
            placeholder='Search across all events...'
            allowClear
            enterButton={<SearchOutlined />}
            size='large'
          />
          <ExportFile data={mockEvents || []} columns={exportColumns} filename='eventsList' />
        </Flex>
      </HeaderContainer>
      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={mockEvents || []}
          rowKey='id'
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: mockEvents?.length || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} events`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1600 }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default Events;
