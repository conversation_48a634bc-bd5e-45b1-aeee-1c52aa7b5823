import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Flex, Input, Menu, Select, SelectProps, Popconfirm, App, Tooltip, But<PERSON> } from 'antd';
import Sider from 'antd/es/layout/Sider';
import React, { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import { FaPlus } from 'react-icons/fa6';
import { HiOutlineChevronLeft, HiOutlineChevronRight } from 'react-icons/hi';
import { LuArrowDownUp } from 'react-icons/lu';
import { MdEdit } from 'react-icons/md';
import { RiDeleteBin6Line } from 'react-icons/ri';
import { useNavigate, useParams } from 'react-router';
import { BidItemDTO, ScopeDTO } from 'src/api';
import { scopeAPI } from 'src/api/apiClient';
import SubSiderThemeProvider from 'src/view/screens/scopes/SubSiderThemeProvider';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import AddScopeModal from './add-scope/AddScope';
import useGlobalStore from '../../../../store/useGlobalStore';
import HasProjectPermission from '../../../../modules/guards/HasProjectPermission';
import { appRoutes, disciplines, queryKeys } from '../../../../modules/utils/constant';
import { UserProjectPermission } from '../../../../modules/utils/permissions';
import useScopesStore from '../store/useScopesStore';

const StyledSider = styled(Sider)`
  height: 100vh;
  color: ${themeTokens.textLight};
  position: relative;
`;

const CollapseButton = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: ${themeTokens.collapseIconBg};
  border: 1px solid ${themeTokens.collapseIconBorder};
  top: 64px;
  right: -17px;
  z-index: 10;
  border-radius: 50%;
  height: 35px;
  cursor: pointer;
  && {
    width: 35px;
  }
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
`;

const DocumentViewContent = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px 24px 0;
  gap: 24px;
`;

const BreadcrumbSeparator = styled.div`
  display: flex;
  gap: 8px;
`;

const ScopeTitle = styled.div<{ isScopeText?: boolean }>`
  font-size: 20px;
  cursor: ${({ isScopeText }) => (isScopeText ? 'pointer' : 'default')};
`;

const Title = styled.div`
  font-weight: 700;
  font-size: 20px;
`;

const StyledSelect = styled(Select)<SelectProps<string>>`
  width: 100%;
`;

const Label = styled.div`
  font-weight: 500;
  font-size: 16px;
`;

const Actions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  align-items: center;
`;

const StyledMenu = styled(Menu)`
  padding: 0 20px;
`;

const MenuItemContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const IconsContainer = styled.div`
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
`;

const ScopeName = styled.span<{ width?: number }>`
  max-width: ${({ width }) => width || 250}px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
`;

const MenuItemWrapper = styled.div`
  &:hover ${IconsContainer} {
    opacity: 1;
  }
`;

const ScrollableMenuWrapper = styled.div`
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }
`;

const BidItemsWrapper = styled.div`
  height: calc(100vh - 160px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }
`;

const ScopeNameTooltip: React.FC<{
  name?: string;
  division?: string;
  width?: number;
  index?: number;
}> = ({ name, division, width, index }) => {
  const textRef = useRef<HTMLSpanElement>(null);
  const [isEllipsisActive, setIsEllipsisActive] = useState(false);

  // Format the scope name with division
  const formattedName = useMemo(() => {
    if (!division) return name;

    // Extract division number from division string
    const divisionNumber = division.match(/\d+/);
    const formattedDivision = divisionNumber ? divisionNumber[0] : division;

    return `Div ${formattedDivision} - ${name}`;
  }, [name, division]);

  useEffect(() => {
    const checkForEllipsis = () => {
      if (textRef.current) {
        const { offsetWidth, scrollWidth } = textRef.current;
        setIsEllipsisActive(scrollWidth > offsetWidth);
      }
    };

    checkForEllipsis();
    window.addEventListener('resize', checkForEllipsis);
    return () => window.removeEventListener('resize', checkForEllipsis);
  }, [formattedName]);

  return isEllipsisActive ? (
    <Tooltip title={formattedName} placement='right'>
      <ScopeName ref={textRef} width={width}>
        {index}
        {index ? '. ' : ''}
        {formattedName}
      </ScopeName>
    </Tooltip>
  ) : (
    <ScopeName ref={textRef} width={width}>
      {index}
      {index ? '. ' : ''}
      {formattedName}
    </ScopeName>
  );
};

const ScopeSubNavigation: React.FC<{
  isDocumentView: boolean;
  bidItems?: BidItemDTO[];
}> = ({ isDocumentView, bidItems }) => {
  const [selectedDiscipline, setSelectedDiscipline] = useState<string>('All');
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isSortAscending, setIsSortAscending] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('');
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedScope, setSelectedScope] = useState<ScopeDTO | null>(null);

  const currentPage = 0;
  const pageSize = 200;

  const { scopeId } = useParams();
  const navigate = useNavigate();
  const { isScopeSubMenuOpen, setScopeSubMenuOpen, selectedProjectId, selectedVersion } =
    useGlobalStore();
  const { setIsDocumentView } = useScopesStore();
  const queryClient = useQueryClient();
  const { notification } = App.useApp();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const { data: scopesList } = useQuery({
    queryKey: [
      queryKeys.allScopes,
      selectedProjectId,
      debouncedSearchQuery,
      selectedDiscipline !== 'All' ? selectedDiscipline : undefined,
      isSortAscending,
      selectedVersion
    ],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined, // division
        debouncedSearchQuery || undefined,
        selectedDiscipline !== 'All' ? selectedDiscipline : undefined,
        String(selectedVersion?.version),
        currentPage,
        pageSize,
        isSortAscending ? ['division,asc'] : ['division,desc']
      ),
    retry: false,
    enabled: !!selectedProjectId,
    select: res => res.data
  });

  const { mutate: deleteScope } = useMutation({
    mutationFn: (scopeId: number) => scopeAPI.deleteScope1(scopeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allScopes] });
      notification.success({ message: 'Scope deleted successfully' });
    },
    onError: () => {
      notification.error({ message: 'Failed to delete scope' });
    }
  });

  const handleEditClick = useCallback((scope: ScopeDTO) => {
    setSelectedScope(scope);
    setIsEditModalOpen(true);
  }, []);

  const handleAddModalClose = useCallback(() => {
    setIsAddModalOpen(false);
  }, []);

  const handleEditModalClose = useCallback(() => {
    setIsEditModalOpen(false);
    setSelectedScope(null);
  }, []);

  const scopeName = useMemo(() => {
    const data = scopesList?.content?.find(scope => scope.id === Number(scopeId))?.name;
    return data;
  }, [scopeId, scopesList, selectedVersion]);

  useEffect(() => {
    if (scopesList?.content?.[0]?.id)
      navigate(
        `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}/${scopesList?.content?.[0]?.id}`
      );
  }, [selectedVersion]);

  return (
    <>
      {isScopeSubMenuOpen && (
        <SubSiderThemeProvider>
          <StyledSider
            collapsible
            collapsed={false}
            onCollapse={() => setScopeSubMenuOpen(false)}
            width={350}
            trigger={null}
          >
            <CollapseButton onClick={() => setScopeSubMenuOpen(!isScopeSubMenuOpen)}>
              {!isScopeSubMenuOpen ? (
                <HiOutlineChevronRight size='18px' color='white' />
              ) : (
                <HiOutlineChevronLeft size='18px' color='white' />
              )}
            </CollapseButton>
            {!isDocumentView ? (
              <Container>
                <Content>
                  <Flex gap={16}>
                    <HasProjectPermission
                      requiredPermissions={[UserProjectPermission.CREATE_SCOPE]}
                    >
                      <Button
                        type='primary'
                        size='small'
                        icon={<FaPlus size={14} />}
                        onClick={() => setIsAddModalOpen(true)}
                      />
                    </HasProjectPermission>
                    <Title>Scopes({scopesList?.totalElements || 0})</Title>
                  </Flex>
                  <Flex gap={8} vertical>
                    <Label>Discipline</Label>
                    <StyledSelect
                      showSearch
                      onChange={value => setSelectedDiscipline(value)}
                      value={selectedDiscipline}
                      options={disciplines?.map(discipline => ({
                        label: discipline.label,
                        value: discipline.label
                      }))}
                      allowClear={false}
                    />
                  </Flex>
                  <Actions>
                    <Input
                      placeholder='Search scopes...'
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      allowClear
                    />
                    <Tooltip title='Sort by Division'>
                      <LuArrowDownUp
                        cursor='pointer'
                        onClick={() => setIsSortAscending(!isSortAscending)}
                        size={20}
                      />
                    </Tooltip>
                  </Actions>
                </Content>
                <ScrollableMenuWrapper>
                  <StyledMenu
                    onClick={event =>
                      navigate(
                        `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}/${event.key}`
                      )
                    }
                    selectedKeys={[scopeId ?? (String(scopesList?.content?.[0]?.id) || '')]}
                    items={scopesList?.content?.map(item => ({
                      key: item?.id || '',
                      label: (
                        <MenuItemWrapper>
                          <MenuItemContainer>
                            <ScopeNameTooltip name={item?.scopeNameWithSpecCode || item.name} />
                            <IconsContainer onClick={e => e.stopPropagation()}>
                              <HasProjectPermission
                                requiredPermissions={[UserProjectPermission.EDIT_SCOPE]}
                              >
                                <Tooltip title='Edit Scope'>
                                  <MdEdit
                                    size={16}
                                    cursor='pointer'
                                    onClick={() => handleEditClick(item)}
                                  />
                                </Tooltip>
                              </HasProjectPermission>
                              <HasProjectPermission
                                requiredPermissions={[UserProjectPermission.DELETE_SCOPE]}
                              >
                                <Tooltip title='Delete Scope'>
                                  <Popconfirm
                                    title='Are you sure you want to delete this scope?'
                                    onConfirm={() => item?.id && deleteScope(item.id)}
                                    okText='Yes'
                                    cancelText='No'
                                    placement='left'
                                  >
                                    <RiDeleteBin6Line size={16} cursor='pointer' />
                                  </Popconfirm>
                                </Tooltip>
                              </HasProjectPermission>
                            </IconsContainer>
                          </MenuItemContainer>
                        </MenuItemWrapper>
                      )
                    }))}
                  />
                </ScrollableMenuWrapper>
              </Container>
            ) : (
              <Container>
                <DocumentViewContent>
                  <Flex gap={16}>
                    <HasProjectPermission
                      requiredPermissions={[UserProjectPermission.CREATE_SCOPE]}
                    >
                      <Button
                        type='primary'
                        size='small'
                        icon={<FaPlus size={14} />}
                        onClick={() => setIsAddModalOpen(true)}
                      />
                    </HasProjectPermission>
                    <BreadcrumbSeparator>
                      <ScopeTitle onClick={() => setIsDocumentView(false)} isScopeText={true}>
                        Scopes
                      </ScopeTitle>
                      <ScopeTitle>&gt;</ScopeTitle>
                      <ScopeTitle>
                        <ScopeNameTooltip name={scopeName || ''} width={175} />
                      </ScopeTitle>
                    </BreadcrumbSeparator>
                  </Flex>
                  <Flex vertical gap={16}>
                    <Title>Bid Items ({bidItems?.length})</Title>
                    <BidItemsWrapper>
                      <Flex vertical gap={8}>
                        {bidItems?.map((item: BidItemDTO, index: number) => (
                          <ScopeNameTooltip name={item?.name || ''} index={index + 1} />
                        ))}
                      </Flex>
                    </BidItemsWrapper>
                  </Flex>
                </DocumentViewContent>
              </Container>
            )}
          </StyledSider>
        </SubSiderThemeProvider>
      )}
      {isAddModalOpen && <AddScopeModal visible={isAddModalOpen} onClose={handleAddModalClose} />}
      {isEditModalOpen && selectedScope && (
        <AddScopeModal
          visible={isEditModalOpen}
          onClose={handleEditModalClose}
          editMode={true}
          scopeData={selectedScope}
        />
      )}
    </>
  );
};

export default ScopeSubNavigation;
