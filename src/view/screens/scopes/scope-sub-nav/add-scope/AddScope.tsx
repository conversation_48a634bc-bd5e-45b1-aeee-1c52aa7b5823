import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Modal, Form, App, Select } from 'antd';
import { isEqual } from 'lodash';
import React, { useEffect, useCallback, useMemo } from 'react';
import { MasterScopeDTO, ScopeCreateDTO, ScopeDTO, ScopeUpdateDTO } from 'src/api';
import { masterScopeAPI, scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import CreatableSearchSelect from '../../../../../modules/common/creatableSearchSelect';
import { APIMutationStatus, disciplines, queryKeys } from '../../../../../modules/utils/constant';
import { extractErrorMessage } from '../../../../../modules/utils/errorHandler';

interface AddScopeModalProps {
  visible: boolean;
  onClose: () => void;
  editMode?: boolean;
  scopeData?: ScopeDTO;
}

const AddScopeModal: React.FC<AddScopeModalProps> = ({
  visible,
  onClose,
  editMode = false,
  scopeData
}) => {
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();
  const { selectedProjectId, selectedVersion } = useGlobalStore();
  const { notification } = App.useApp();

  // Memoize the initial form values for edit mode
  const initialValues = useMemo(() => {
    if (editMode && scopeData) {
      return {
        name: scopeData.name,
        discipline: scopeData.discipline,
        division: scopeData.division,
        specificationCode: scopeData.scopeSpecCode
      };
    }
    return undefined;
  }, [editMode, scopeData]);

  // Set form values when in edit mode
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const handleMutationSuccess = useCallback(
    (message: string) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allScopes] });
      notification.success({ message });
    },
    [queryClient, notification]
  );

  // Fetch master scopes from API
  const { data: masterScopesData } = useQuery({
    queryKey: [queryKeys.masterScopes],
    queryFn: () =>
      masterScopeAPI.getMasterScopes(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        0, // page
        1000 // size
      ),

    select: response => response.data?.content,
    staleTime: 5 * 60 * 1000
  });

  // Fetch Specification code based on division
  const { data: specificationsByDivision } = useQuery({
    queryKey: [queryKeys.specsByDivision, formValues?.division],
    queryFn: () => masterScopeAPI.getAggregatedSpecsByDivision(formValues?.division || ''),

    select: response => response.data,
    staleTime: 5 * 60 * 1000,
    enabled: !!formValues?.division
  });

  // Create scope mutation
  const {
    mutate: createScopeMutate,
    status: createStatus,
    reset: createReset
  } = useMutation({
    mutationFn: (data: ScopeDTO) => scopeAPI.createScope(data),
    onSuccess: () => handleMutationSuccess('Scope created successfully'),
    onError: error =>
      notification.error({ message: extractErrorMessage(error, 'Scope creation failed') })
  });

  // Update scope mutation
  const {
    mutate: updateScopeMutate,
    status: updateStatus,
    reset: updateReset
  } = useMutation({
    mutationFn: (data: { id: number; updateData: ScopeUpdateDTO }) =>
      scopeAPI.updateScope(data.id, data.updateData),
    onSuccess: () => handleMutationSuccess('Scope updated successfully'),
    onError: error =>
      notification.error({ message: extractErrorMessage(error, 'Scope update failed') })
  });

  const hasFormChanged = useMemo(() => {
    if (!formValues) return;
    return !isEqual(formValues, initialValues);
  }, [formValues, initialValues]);

  // Format master scopes for display
  const formattedMasterScopes = useMemo(() => {
    if (!masterScopesData) return [];

    const uniqueScopes = new Map();

    masterScopesData.forEach((scope: MasterScopeDTO) => {
      const name = scope.name || '';
      const division = scope.division || '';
      const uniqueKey = `${division}-${name}`;

      if (name && !uniqueScopes.has(uniqueKey)) {
        uniqueScopes.set(uniqueKey, scope);
      }
    });
    return Array.from(uniqueScopes.values()) as MasterScopeDTO[];
  }, [masterScopesData]);

  useEffect(() => {
    if (
      specificationsByDivision?.relevantSpecSections &&
      specificationsByDivision?.relevantSpecSections?.length > 0
    ) {
      if (specificationsByDivision?.relevantSpecSections?.[0])
        form.setFieldValue(
          'specificationCode',
          specificationsByDivision?.relevantSpecSections?.[0]
        );
      if (specificationsByDivision?.discipline)
        form.setFieldValue('discipline', specificationsByDivision?.discipline);
    }
  }, [specificationsByDivision]);

  // Extract unique divisions from master scopes
  const divisions = useMemo(() => {
    if (!masterScopesData) return [];

    const uniqueDivisions = new Set<string>();
    masterScopesData.forEach(scope => {
      if (scope.division) {
        uniqueDivisions.add(scope.division);
      }
    });
    return Array.from(uniqueDivisions).sort();
  }, [masterScopesData]);

  const handleScopeChange = useCallback(
    (selectedValues: string[]) => {
      const selectedValue = selectedValues[0];
      form.setFieldValue('name', selectedValue);
      const selectedScope = formattedMasterScopes.find(
        scope => scope.scopeSpecCode === selectedValue
      );

      if (selectedScope) {
        form.setFieldValue('discipline', selectedScope.discipline);
        form.setFieldValue('division', selectedScope.division);
        form.setFieldValue('specificationCode', selectedScope.scopeSpecCode);
      }
    },
    [form, formattedMasterScopes]
  );

  const handleModalClose = useCallback(() => {
    form.resetFields();
    createReset();
    updateReset();
    onClose();
  }, [form, createReset, updateReset, onClose]);

  const saveScope = useCallback(async () => {
    try {
      const masterScope = masterScopesData?.find(
        masterScope => masterScope.scopeSpecCode === formValues?.name
      );
      let scopeName = formValues?.name;
      if (!masterScope) {
        scopeName = `${formValues?.specificationCode?.split(' - ')?.[0]} - ${formValues?.name}`;
      }

      const data: ScopeCreateDTO = {
        projectId: Number(selectedProjectId),
        name: formValues?.name,
        discipline: formValues?.discipline,
        division: formValues?.division,
        scopeNameWithSpecCode: scopeName,
        scopeSpecCode: formValues?.specificationCode,
        projectDocumentSetId: Number(selectedVersion?.versionId)
      };

      if (editMode && scopeData?.id) {
        updateScopeMutate({ id: scopeData.id, updateData: data });
      } else {
        createScopeMutate(data);
      }
      handleModalClose();
    } catch (error) {
      notification.error({
        message: editMode ? 'Scope update failed' : 'Scope creation failed',
        description: String(error)
      });
    }
  }, [
    masterScopesData,
    formValues?.name,
    formValues?.discipline,
    formValues?.division,
    formValues?.specificationCode,
    selectedProjectId,
    selectedVersion?.versionId,
    editMode,
    scopeData?.id,
    handleModalClose,
    updateScopeMutate,
    createScopeMutate,
    notification
  ]);

  const handleSave = useCallback(async () => {
    const currentStatus = editMode ? updateStatus : createStatus;
    if (currentStatus === APIMutationStatus.success) {
      handleModalClose();
      return;
    }
    try {
      await form.validateFields();
      await saveScope();
    } catch (error) {
      notification.error({
        message: 'Validation failed',
        description: String(error)
      });
    }
  }, [editMode, updateStatus, createStatus, handleModalClose, form, saveScope, notification]);

  const getDefaultScopeValue = useCallback(() => {
    const masterScope = masterScopesData?.find(
      masterScope => masterScope.scopeSpecCode === scopeData?.scopeNameWithSpecCode
    );
    if (scopeData?.scopeNameWithSpecCode && masterScope) {
      return [scopeData.scopeNameWithSpecCode];
    }
    if (scopeData?.name) {
      return [scopeData.name];
    }
    return [];
  }, [scopeData]);

  return (
    <Modal
      title={editMode ? 'Edit Scope' : 'Add Scope'}
      open={visible}
      closeIcon={null}
      maskClosable={false}
      onCancel={onClose}
      okText={editMode ? 'Save' : 'Add'}
      centered
      onOk={handleSave}
      okButtonProps={{ disabled: !hasFormChanged }}
    >
      <Form form={form} layout='vertical'>
        <Form.Item
          label='Scope Name'
          name='name'
          rules={[{ required: true, message: 'Please select scope' }]}
        >
          <CreatableSearchSelect
            initialOptions={formattedMasterScopes?.map(scope => scope?.scopeSpecCode || '')}
            placeholder='Select or create scope'
            isMultiple={false}
            defaultSelectedValues={getDefaultScopeValue()}
            onSelectionChange={handleScopeChange}
          />
        </Form.Item>
        <Form.Item
          label='Division'
          name='division'
          rules={[{ required: true, message: 'Please select division' }]}
        >
          <CreatableSearchSelect
            initialOptions={divisions}
            placeholder='Select division'
            isCreateable={false}
            isMultiple={false}
            defaultSelectedValues={[formValues?.division]}
            onSelectionChange={(selectedValues: string[]) =>
              form.setFieldValue('division', selectedValues[0])
            }
          />
        </Form.Item>
        <Form.Item
          label='Specification Code'
          name='specificationCode'
          rules={[{ required: true, message: 'Please select specification code' }]}
        >
          <CreatableSearchSelect
            initialOptions={specificationsByDivision?.relevantSpecSections || []}
            placeholder='Select specification code'
            isCreateable={false}
            isMultiple={false}
            defaultSelectedValues={[formValues?.specificationCode]}
            onSelectionChange={(selectedValues: string[]) =>
              form.setFieldValue('specificationCode', selectedValues[0])
            }
          />
        </Form.Item>
        <Form.Item
          label='Discipline'
          name='discipline'
          rules={[{ required: true, message: 'Please select discipline' }]}
        >
          <Select
            showSearch
            placeholder='Select discipline'
            value={formValues?.discipline}
            options={disciplines}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddScopeModal;
