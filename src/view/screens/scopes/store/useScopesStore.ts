import { ThumbnailInfo } from 'src/modules/utils/thumbnailInfo';
import { create } from 'zustand';

interface ScopesState {
  isDocumentView: boolean;
  setIsDocumentView: (isDocumentView: boolean) => void;
  selectedThumbnailInfo: ThumbnailInfo | null;
  setSelectedThumbnailInfo: (thumbnailInfo: ThumbnailInfo | null) => void;
  drawingsThumbnails: ThumbnailInfo[];
  setDrawingsThumbnails: (drawingsThumbnails: ThumbnailInfo[]) => void;
  specificationsThumbnails: ThumbnailInfo[];
  setSpecificationsThumbnails: (specificationsThumbnails: ThumbnailInfo[]) => void;
  isCreatingThumbnails: boolean;
  setIsCreatingThumbnails: (isCreatingThumbnails: boolean) => void;
  isLoadingDocument: boolean;
  setIsLoadingDocument: (isLoadingDocument: boolean) => void;
}

const useScopesStore = create<ScopesState>(set => ({
  isDocumentView: false,
  setIsDocumentView: isDocumentView => set({ isDocumentView }),
  selectedThumbnailInfo: null,
  setSelectedThumbnailInfo: thumbnailInfo => set({ selectedThumbnailInfo: thumbnailInfo }),
  drawingsThumbnails: [],
  setDrawingsThumbnails: drawingsThumbnails => set({ drawingsThumbnails }),
  specificationsThumbnails: [],
  setSpecificationsThumbnails: specificationsThumbnails => set({ specificationsThumbnails }),
  isCreatingThumbnails: false,
  setIsCreatingThumbnails: isCreatingThumbnails => set({ isCreatingThumbnails }),
  isLoadingDocument: false,
  setIsLoadingDocument: isLoadingDocument => set({ isLoadingDocument })
}));

export default useScopesStore;
