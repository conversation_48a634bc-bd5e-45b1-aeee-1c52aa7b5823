@import '../../../../../node_modules/@syncfusion/ej2-base/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-buttons/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-dropdowns/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-inputs/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-navigations/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-popups/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css';
@import '../../../../../node_modules/@syncfusion/ej2-pdfviewer/styles/material.css';

.e-pv-viewer-main-container {
  background-color: white;
}

.e-pv-viewer-container {
  background-color: white;
}

.e-pv-add-bid-icon::before {
  content: '\e688';
  font-family: 'e-icons';
  font-size: 16px;
}

.e-pv-next-page-navigation-icon-diabled,
.e-pv-previous-page-navigation-icon-disabled {
  cursor: none;
  pointer-events: none;
  opacity: 0.5;
}

.e-pv-add-button {
  margin-top: 2px;

  .e-tbar-btn {
    border-radius: 50% !important;
    padding: 1px 5px !important;
    background-color: #ff6a34 !important;
    &:hover {
      opacity: 0.8;
    }
  }

  span {
    font-weight: 600;
    font-size: 18px;
    color: white;
    margin-top: -4px;
    margin-bottom: -4px;
  }
}
