import { DownOutlined, LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Input, Checkbox, Flex, Carousel, Select, Button, Spin, Tooltip } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { IoIosArrowForward, IoIosArrowBack } from 'react-icons/io';
import { useParams } from 'react-router';
import { DocumentDTOInputDocumentTypeEnum } from 'src/api';
import { bidItemAPI, openSearchAPI, projectAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { DocumentTabName } from './DocumentThumbnailsView';
import { getThumbnailS3Url, ThumbnailInfo } from '../../../../modules/utils/thumbnailInfo';
import useScopesStore from '../store/useScopesStore';
import { queryKeys } from '../../../../modules/utils/constant';

const Container = styled.div<{ isThumbnailsOpen: boolean }>`
  background: ${themeTokens.textLight};
  padding: ${({ isThumbnailsOpen }) => (isThumbnailsOpen ? '20px' : '2px 20px')};
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const SearchBar = styled(Input)`
  width: 55%;
  min-width: 300px;
  border: 1px solid #201a22;
`;

const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  .ant-select-selector {
    padding-left: 6px !important;
  }
`;

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
`;

const StyledImageContainer = styled.div`
  display: flex !important;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  gap: 2px;
  padding: 10px;
  text-align: center;
  width: 150px;
  max-width: 150px;
`;

const StyledImage = styled.img<{ isActive: boolean }>`
  height: 100%;
  width: fit-content;
  object-fit: contain;
  max-height: 70px;
  border: 1px solid ${({ isActive }) => (isActive ? themeTokens.primaryColor : '#4e4e4e')};
`;

const SearchHeaderContainer = styled.div`
  width: 100%;
  padding: 0 40px;
`;

const SearchContainer = styled(Flex)`
  width: 100%;
`;

const StyledCarousel = styled(Carousel)`
  position: relative;
  padding: 0 30px;
`;

const Ellipsis = styled.div<{ width?: string }>`
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
`;

const filterType = {
  all: 'All',
  currentScope: 'Current Scope'
};

const CheckboxStyle = styled(Checkbox)<{ isActive: boolean }>`
  & .ant-checkbox .ant-checkbox-inner {
    border: 1px solid ${({ isActive }) => (!isActive ? themeTokens.activeBlue : 'unset')};
  }
`;

const SearchResultsText = styled.div`
  font-size: 14px;
  margin-top: 8px;
  margin-left: 4px;
`;

const BoldText = styled.span`
  font-weight: 700;
`;

const Headingwrapper = styled.span`
  font-size: 18px;
  font-weight: 500;
  font-family: inter;
`;

const ThumbnailsWrapper = styled.div`
  max-height: 100%;
  overflow: auto;
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

interface FileViewerProps {
  isThumbnailsOpen: boolean;
  drawingsThumbnails: ThumbnailInfo[];
  specificationsThumbnails: ThumbnailInfo[];
  activeTab: DocumentTabName;
  setActiveTab: (value: DocumentTabName) => void;
  setIsThumbnailsOpen: (value: boolean) => void;
  setIsThumbnailsDrawerMinised: (value: boolean) => void;
  isThumbnailsDrawerMinimised: boolean;
}

const FileViewer: React.FC<FileViewerProps> = ({
  setIsThumbnailsOpen,
  isThumbnailsOpen,
  drawingsThumbnails,
  specificationsThumbnails,
  activeTab,
  setActiveTab,
  setIsThumbnailsDrawerMinised,
  isThumbnailsDrawerMinimised
}) => {
  const { projectId } = useParams();
  const { scopeId } = useParams();
  const [searchText, setSearchText] = useState<string>('');
  const [showDrawings, setShowDrawings] = useState<boolean>(true);
  const [showSpecs, setShowSpecs] = useState<boolean>(true);
  const [drawingsFilterType, setDrawingsFilterType] = useState<string>(filterType.currentScope);
  const [specificationsFilterType, setSpecificationsFilterType] = useState<string>(
    filterType.currentScope
  );
  const [isSearchTextAvailable, setIsSearchTextAvailable] = useState<boolean>(false);

  const {
    selectedThumbnailInfo,
    setSelectedThumbnailInfo,
    setDrawingsThumbnails,
    setSpecificationsThumbnails
  } = useScopesStore();
  const { currentUser, selectedProjectId, selectedVersion } = useGlobalStore();
  const isMounted = useRef(false);

  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length === 0) {
      fetchDrawingsAndSpecs();
    }
    setSearchText(e.target.value);
  };

  const drawingFilters = useMemo(() => {
    if (showDrawings) {
      return drawingsFilterType === filterType.currentScope ? scopeId : drawingsFilterType;
    }
    return undefined;
  }, [drawingsFilterType, scopeId, showDrawings]);

  const specificationFilters = useMemo(() => {
    if (showSpecs) {
      return specificationsFilterType === filterType.currentScope
        ? scopeId
        : specificationsFilterType;
    }
    return undefined;
  }, [scopeId, showSpecs, specificationsFilterType]);

  const {
    data: searchTextData,
    refetch: fetchSearchTextData,
    isFetching
  } = useQuery({
    queryKey: [
      queryKeys.searchTextData,
      drawingsFilterType,
      specificationsFilterType,
      showDrawings,
      showSpecs,
      projectId,
      selectedVersion,
      scopeId
    ],
    queryFn: () =>
      openSearchAPI.searchByText(
        Number(projectId),
        searchText,
        undefined,
        undefined,
        drawingFilters,
        specificationFilters
      ),
    enabled: false
  });

  const {
    data: drawingsAndSpecsData,
    isFetching: isFetchingDrawingsAndSpecs,
    refetch: fetchDrawingsAndSpecs
  } = useQuery({
    queryKey: [queryKeys.drawingsAndSpecs, selectedProjectId, drawingFilters, specificationFilters],
    queryFn: () =>
      bidItemAPI.getSheetsAndSpecs(
        Number(selectedProjectId),
        drawingFilters,
        specificationFilters,
        0,
        1000
      ),
    select: res => res.data,
    enabled: false
  });

  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: false,
    select: res => res.data
  });

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs || !drawingsAndSpecsData) return;
    const drawings = drawingsAndSpecsData?.bidItemSheets;
    const specs = drawingsAndSpecsData?.bidItemSpecificationDTOList;
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (drawings) {
      drawingThumbnails = drawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setDrawingsThumbnails(drawingThumbnails);
    }
    if (specs) {
      specificationThumbnails = specs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setSpecificationsThumbnails(specificationThumbnails);
    }
  }, [drawingsAndSpecsData, isFetchingDrawingsAndSpecs]);

  useEffect(() => {
    if (isFetching || !searchTextData) return;

    const data = (searchTextData?.data || [])?.map(item => {
      const doc =
        documentsData?.documents?.find(doc => doc?.id === Number(item?.documentId)) || null;
      const thumbnail = getThumbnailS3Url(
        currentUser?.companyId || 0,
        Number(selectedProjectId),
        Number(item.documentId),
        Number(item.pageNumber) || 0
      );

      return {
        document: doc,
        sheet: item?.sheets?.[0],
        thumbnail,
        specification: item?.specifications?.[0]
      };
    });
    setDrawingsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
      )
    );
    setSpecificationsThumbnails(
      data?.filter(
        item => item.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
      )
    );
  }, [searchTextData, isFetching]);

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      return;
    }
    if (selectedProjectId) {
      if (searchText.length > 0) {
        fetchSearchTextData();
      } else {
        fetchDrawingsAndSpecs();
      }
    }
  }, [showDrawings, showSpecs, drawingsFilterType, specificationsFilterType]);

  useEffect(() => {
    setIsSearchTextAvailable(!!searchText);
  }, [drawingFilters, specificationFilters]);

  //Do not delete will reuse for implementing FE search
  // const onSearchHandler = async () => {
  // if (!searchText || searchText.trim().length === 0) {
  //   setFilteredThumbnails(thumbnails);
  //   return;
  // }
  // const res = await findThumbnailsWithText(
  //   searchText,
  //   thumbnails[0]?.document?.presignedUrl,
  //   thumbnails[0]?.sheet?.pageNumber
  // );
  // const filteredThumbnails = thumbnails.filter(thumbnail => {
  //   return res.some(result => result.page === thumbnail?.sheet?.pageNumber);
  // });
  // setFilteredThumbnails(filteredThumbnails);
  // };

  const OnClickThumbnailHandler = (file: ThumbnailInfo) => {
    setIsThumbnailsOpen(!isThumbnailsOpen);
    setSelectedThumbnailInfo(file);
    if (
      activeTab === DocumentTabName.drawings &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
    ) {
      setActiveTab(DocumentTabName.specs);
    } else if (
      activeTab === DocumentTabName.specs &&
      file.document?.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
    ) {
      setActiveTab(DocumentTabName.drawings);
    }
  };

  const onSearchHandler = () => {
    setIsSearchTextAvailable(!!searchText);
    if (searchText.length > 0) fetchSearchTextData();
    else fetchDrawingsAndSpecs();
  };

  return (
    <Container isThumbnailsOpen={isThumbnailsOpen}>
      {isThumbnailsOpen ? (
        <Flex vertical gap={30} style={{ height: '100%' }}>
          {!isThumbnailsDrawerMinimised && (
            <Flex gap={4} justify='flex-end'>
              <Button onClick={() => setIsThumbnailsOpen(!isThumbnailsOpen)}>Close</Button>
              <Button
                onClick={() => {
                  setIsThumbnailsOpen(false);
                  setIsThumbnailsDrawerMinised(true);
                }}
                icon={<DownOutlined />}
              />
            </Flex>
          )}
          <SearchHeaderContainer>
            <Flex align='center' justify='space-between' gap={24}>
              <SearchContainer align='center' gap={20}>
                <SearchBar
                  placeholder='Search here'
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={handleSearchChange}
                  size='large'
                  variant='outlined'
                  onPressEnter={onSearchHandler}
                />
                <Button
                  variant='solid'
                  type='primary'
                  size='middle'
                  style={{ width: 'fit-content' }}
                  onClick={onSearchHandler}
                >
                  Search
                </Button>
              </SearchContainer>
              <Flex align='center' gap={10}>
                <FilterContainer>
                  <CheckboxStyle
                    isActive={!showSpecs && showDrawings}
                    checked={showDrawings}
                    onChange={event => setShowDrawings(event.target.checked)}
                    disabled={!showSpecs && showDrawings}
                  />
                  <Select
                    variant='borderless'
                    options={[
                      { label: 'All', value: filterType.all },
                      { label: 'Current Scope', value: filterType.currentScope }
                    ]}
                    dropdownStyle={{ width: 'fit-content' }}
                    value={`${DocumentTabName.drawings} (${drawingsFilterType})`}
                    onChange={value => setDrawingsFilterType(value)}
                    disabled={!showDrawings}
                  />
                </FilterContainer>
                <FilterContainer>
                  <CheckboxStyle
                    isActive={!showDrawings && showSpecs}
                    checked={showSpecs}
                    onChange={event => setShowSpecs(event.target.checked)}
                    disabled={!showDrawings && showSpecs}
                  />
                  <Select
                    variant='borderless'
                    options={[
                      { label: 'All', value: filterType.all },
                      { label: 'Current Scope', value: filterType.currentScope }
                    ]}
                    dropdownStyle={{ width: 'fit-content' }}
                    value={`${DocumentTabName.specs} (${specificationsFilterType})`}
                    onChange={value => setSpecificationsFilterType(value)}
                    disabled={!showSpecs}
                  />
                </FilterContainer>
              </Flex>
            </Flex>
            {isSearchTextAvailable && !isFetchingDrawingsAndSpecs && !isFetching && (
              <SearchResultsText>
                <BoldText>{drawingsThumbnails.length + specificationsThumbnails.length}</BoldText>{' '}
                items found{' '}
                <span>
                  - <BoldText>{drawingsThumbnails?.length}</BoldText> drawings{' '}
                </span>
                <span>
                  and <BoldText>{specificationsThumbnails?.length}</BoldText> specifications
                </span>
              </SearchResultsText>
            )}
          </SearchHeaderContainer>
          {(isFetching || isFetchingDrawingsAndSpecs) && (
            <Spin indicator={<LoadingOutlined spin />} size='small' />
          )}
          <ThumbnailsWrapper>
            {drawingsThumbnails.length > 0 && <Headingwrapper>Drawings</Headingwrapper>}
            <GridContainer>
              {drawingsThumbnails?.map(file => {
                const isSheetActive =
                  file?.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId;
                return (
                  <StyledImageContainer key={file?.sheet?.sheetId}>
                    <StyledImage
                      src={file.thumbnail}
                      alt={file?.sheet?.title}
                      onClick={() => !isSheetActive && OnClickThumbnailHandler(file)}
                      isActive={isSheetActive}
                    />
                    <div>{file?.sheet?.sheetNumber}</div>
                    <div>{file?.sheet?.title}</div>
                  </StyledImageContainer>
                );
              })}
            </GridContainer>
            {specificationsThumbnails.length > 0 && <Headingwrapper>Specifications</Headingwrapper>}
            <GridContainer>
              {specificationsThumbnails?.map(file => {
                const isSpecsActive =
                  file?.specification?.specificationId ===
                  selectedThumbnailInfo?.specification?.specificationId;
                return (
                  <StyledImageContainer key={file?.specification?.specificationId}>
                    <StyledImage
                      src={file.thumbnail}
                      alt={file?.specification?.specificationTitle}
                      onClick={() => !isSpecsActive && OnClickThumbnailHandler(file)}
                      isActive={isSpecsActive}
                    />
                    <div>{file?.specification?.specificationCode}</div>
                    <div>{file?.specification?.specificationTitle}</div>
                  </StyledImageContainer>
                );
              })}
            </GridContainer>
          </ThumbnailsWrapper>
        </Flex>
      ) : (
        <>
          {activeTab === DocumentTabName.drawings && (
            <StyledCarousel
              dots={false}
              arrows
              infinite={false}
              slidesToShow={8}
              slidesToScroll={1}
              key={drawingsThumbnails ? drawingsThumbnails.length : 'empty'}
              prevArrow={<IoIosArrowBack color='black' size={25} />}
              nextArrow={<IoIosArrowForward color='black' size={25} />}
            >
              {drawingsThumbnails?.map(file => {
                const isActive = file?.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId;
                return (
                  <StyledImageContainer key={file?.sheet?.sheetId}>
                    <StyledImage
                      isActive={isActive}
                      src={file.thumbnail}
                      alt={file?.sheet?.title}
                      onClick={() => !isActive && setSelectedThumbnailInfo(file)}
                    />
                    <Ellipsis>
                      <Tooltip title={`${file?.sheet?.sheetNumber} - ${file?.sheet?.title}`}>
                        {file?.sheet?.sheetNumber} - {file?.sheet?.title}
                      </Tooltip>
                    </Ellipsis>
                  </StyledImageContainer>
                );
              })}
            </StyledCarousel>
          )}
          {activeTab === DocumentTabName.specs && (
            <StyledCarousel
              dots={false}
              arrows
              infinite={false}
              slidesToShow={8}
              slidesToScroll={1}
              key={specificationsThumbnails ? specificationsThumbnails.length : 'empty'}
              prevArrow={<IoIosArrowBack color='black' size={25} />}
              nextArrow={<IoIosArrowForward color='black' size={25} />}
            >
              {specificationsThumbnails?.map(file => {
                const isActive =
                  file?.specification?.specificationId ===
                  selectedThumbnailInfo?.specification?.specificationId;

                return (
                  <StyledImageContainer key={file?.specification?.specificationId}>
                    <StyledImage
                      isActive={isActive}
                      src={file.thumbnail}
                      alt={file?.specification?.specificationTitle}
                      onClick={() => !isActive && setSelectedThumbnailInfo(file)}
                    />
                    <Ellipsis>
                      <Tooltip
                        title={`${file?.specification?.specificationCode} - ${file?.specification?.specificationTitle}`}
                      >
                        {file?.specification?.specificationCode} -{' '}
                        {file?.specification?.specificationTitle}
                      </Tooltip>
                    </Ellipsis>
                  </StyledImageContainer>
                );
              })}
            </StyledCarousel>
          )}
        </>
      )}
    </Container>
  );
};

export default FileViewer;
