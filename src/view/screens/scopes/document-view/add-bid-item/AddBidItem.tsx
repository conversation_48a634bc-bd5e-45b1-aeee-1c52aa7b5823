import { CaretLeftFilled } from '@ant-design/icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Form, Input, App, Button } from 'antd';
import React, { useEffect } from 'react';
import { RiCloseLine } from 'react-icons/ri';
import { useParams } from 'react-router-dom';
import { BidItemDTO } from 'src/api';
import { bidItemAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { queryKeys } from 'src/modules/utils/constant';
import useScopesStore from '../../store/useScopesStore';

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
`;

const Title = styled.div`
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 20px;
`;

const Container = styled.div`
  width: 100%;
  height: fit-content;
  background-color: ${themeTokens.whiteBg};
  padding: 20px;
  position: relative;
  border-radius: 4px;
`;

const LeftPointer = styled(CaretLeftFilled)`
  position: absolute;
  left: -24px;
  top: 50%;
  font-size: 34px;
  color: white;
`;

const CloseButton = styled(RiCloseLine)`
  position: absolute;
  right: 12px;
  top: 12px;
  cursor: pointer;
  font-size: 24px;
`;

interface AddBidModalProps {
  onClose: () => void;
  bidItemName?: string;
  onBidItemAdded: () => void;
}

const AddBidItem: React.FC<AddBidModalProps> = ({ onClose, bidItemName, onBidItemAdded }) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { selectedThumbnailInfo } = useScopesStore();
  const { selectedProjectId, selectedVersion } = useGlobalStore();
  const { scopeId } = useParams();
  const { notification } = App.useApp();

  const {
    mutateAsync: createBidItem,
    reset: resetCreate,
    isPending
  } = useMutation({
    mutationFn: (data: BidItemDTO) => bidItemAPI.createBidItem(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] });
      notification.success({ message: 'Bid Item created successfully' });
    }
  });

  useEffect(() => {
    if (bidItemName) form.setFieldValue('bidItemName', bidItemName);
  }, [bidItemName]);

  const handleSave = async () => {
    if (!selectedThumbnailInfo || !selectedProjectId || !scopeId) return;
    try {
      const values = await form.validateFields();
      const updatedItem: BidItemDTO = {
        name: values.bidItemName,
        bidItemSheets: selectedThumbnailInfo?.sheet ? [selectedThumbnailInfo?.sheet] : [],
        bidItemSpecificationDTOList: selectedThumbnailInfo?.specification
          ? [selectedThumbnailInfo?.specification]
          : [],
        projectId: Number(selectedProjectId),
        scopeId: Number(scopeId)
      };
      await createBidItem(updatedItem);
      resetCreate();
      onBidItemAdded();
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  if (!scopeId) return null;

  return (
    <Container>
      <CloseButton onClick={onClose} />
      <LeftPointer />
      <Form form={form} layout='vertical'>
        <Title>Add Bid Item</Title>
        <Form.Item
          label='Bid Item Name'
          name='bidItemName'
          rules={[{ required: true, message: 'Please add bid item name' }]}
        >
          <Input />
        </Form.Item>
        <ButtonsContainer>
          <Button onClick={onClose}>Cancel</Button>
          <Button type='primary' onClick={handleSave} loading={isPending} disabled={isPending}>
            Add
          </Button>
        </ButtonsContainer>
      </Form>
    </Container>
  );
};

export default AddBidItem;
