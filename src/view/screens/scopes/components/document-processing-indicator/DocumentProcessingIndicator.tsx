import { Flex } from 'antd';
import styled from 'styled-components';
import noScopesFoundIcon from 'src/assets/images/noScopesFoundIcon.svg';
import { DocumentProcessingIndicatorText } from '../../../../../modules/utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 50px;
  height: 100%;
  overflow: hidden scroll;
  scrollbar-width: none;
`;

const Header = styled.div`
  font-family: inter;
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const Description = styled.div`
  font-weight: 300;
  font-size: 27px;
  margin: 0;
  font-family: inter;
  color: #4e4e4e;
  text-align: center;
`;

const DocumentProcessingIndicator = () => {
  return (
    <Container>
      <Flex vertical align='center' justify='center' gap={60}>
        <Header>{DocumentProcessingIndicatorText.title}</Header>
        <Flex vertical align='center' justify='center' gap={32}>
          <img src={noScopesFoundIcon} alt='processing' />
          <Description>
            {DocumentProcessingIndicatorText.bodyText}
            <br /> {DocumentProcessingIndicatorText.bodySubText}
          </Description>
        </Flex>
      </Flex>
    </Container>
  );
};

export default DocumentProcessingIndicator;
