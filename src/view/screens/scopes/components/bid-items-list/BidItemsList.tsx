import { useQuery } from '@tanstack/react-query';
import { List } from 'antd';
import React from 'react';
import { useParams } from 'react-router-dom';
import { bidItemAPI } from 'src/api/apiClient';
import { queryKeys } from 'src/modules/utils/constant';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

const Container = styled.div`
  flex: 1;
  width: 100%;
  height: 100%;
  background-color: ${themeTokens.whiteBg};
  padding: 20px;
  border-radius: 4px;
  overflow-y: auto;
  scrollbar-width: thin;
`;

const Title = styled.div`
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 20px;
`;

const BidItemsList: React.FC = () => {
  const { scopeId } = useParams();

  const { data: bidItemsList } = useQuery({
    queryKey: [queryKeys.allBidItems, scopeId],
    queryFn: () => bidItemAPI.getBidItemsByScopeId(Number(scopeId), 0, 1000),
    select: res => res.data.content,
    enabled: !!scopeId
  });

  if (!scopeId) return null;

  return (
    <Container>
      <Title>Current Bid Items</Title>
      <List
        size='small'
        dataSource={bidItemsList}
        renderItem={(item, index) => <List.Item>{`${index + 1}. ${item.name}`}</List.Item>}
      />
    </Container>
  );
};

export default BidItemsList;
