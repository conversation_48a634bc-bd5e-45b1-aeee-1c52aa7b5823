import { But<PERSON>, Checkbox, Flex, Modal } from 'antd';
import React, { useState } from 'react';
import AddBid from 'src/assets/images/addBid.png';
import DragSelect from 'src/assets/images/dragSelect.png';
import ViewBid from 'src/assets/images/viewBid.png';
import { StorageKey } from 'src/modules/utils/constant';
import { setItem } from 'src/modules/utils/storage';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

const Container = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 60px;
  padding: 60px 20px;
`;

const ImageContainer = styled.div`
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: ${themeTokens.modalBg};
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const Image = styled.img`
  width: 90%;
  height: auto;
  object-fit: contain;
`;

const Title = styled.div`
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  color: ${themeTokens.textBlack};
`;

const Description = styled.div`
  font-size: 16px;
  text-align: center;
  color: ${themeTokens.textGray};
`;

const StyledFlex = styled(Flex)`
  width: 300px;
`;

interface AddBidModalProps {
  visible: boolean;
  onClose: () => void;
  onContinue: () => void;
}

const AddBidInstructionsModal: React.FC<AddBidModalProps> = ({ visible, onClose, onContinue }) => {
  const [dontShowAgain, setDontShowAgain] = useState(false);

  const handleClose = () => {
    onClose();
    setDontShowAgain(false);
  };

  const onGetStarted = () => {
    if (dontShowAgain) {
      setItem(StorageKey.instructionsModalChecked, true);
    }
    handleClose();
    onContinue();
  };
  return (
    <Modal
      width='fit-content'
      open={visible}
      onCancel={handleClose}
      footer={
        <Flex align='center' justify='space-between'>
          <Checkbox checked={dontShowAgain} onChange={e => setDontShowAgain(e.target.checked)}>
            Don’t show me this again
          </Checkbox>
          <Button type='primary' onClick={onGetStarted}>
            Get Started
          </Button>
        </Flex>
      }
      centered
    >
      <Container>
        <StyledFlex vertical gap={20} align='center'>
          <ImageContainer>
            <Image src={DragSelect} alt='Drag and Select Text' />
          </ImageContainer>
          <Flex vertical align='center'>
            <Title>1. Drag and Select Text</Title>
            <Description>
              To add a bid item, drag and select the relevant text and click on the add icon
            </Description>
          </Flex>
        </StyledFlex>
        <StyledFlex vertical gap={20} align='center'>
          <ImageContainer>
            <Image src={AddBid} alt='Add Bid Item' />
          </ImageContainer>
          <Flex vertical align='center'>
            <Title>2. Add Bid Item</Title>
            <Description>Verify the bid item data and click Save to add Bid Item</Description>
          </Flex>
        </StyledFlex>
        <StyledFlex vertical gap={20} align='center'>
          <ImageContainer>
            <Image src={ViewBid} alt='View Bid Item' />
          </ImageContainer>
          <Flex vertical align='center'>
            <Title>3. View Bid Item</Title>
            <Description>Created Bid Item is displayed under the associated Scope</Description>
          </Flex>
        </StyledFlex>
      </Container>
    </Modal>
  );
};

export default AddBidInstructionsModal;
