import { Button, Flex, Tooltip } from 'antd';
import { useMemo, useState } from 'react';
import { MdOutlineFileUpload } from 'react-icons/md';
import { useParams } from 'react-router';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import noScopesFoundIcon from 'src/assets/images/noScopesFoundIcon.svg';
import { DocProcessingJobStatus } from '../../../../../modules/utils/constant';
import UploadDocuments from '../../../project-documents/upload-documents/UploadDocuments';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 50px;
  height: 100%;
  overflow: hidden scroll;
  scrollbar-width: none;
`;

const Header = styled.div`
  font-family: inter;
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const Description = styled.div`
  font-weight: 300;
  font-size: 27px;
  margin: 0;
  font-family: inter;
  color: #4e4e4e;
`;

const NoScopeComponent = () => {
  const { projectId } = useParams();
  const { documentProcessingListeners } = useGlobalStore();
  const [isUploadDocumentModalOpen, setIsUploadDocumentModalOpen] = useState(false);

  const documentProcessingStatus = useMemo(() => {
    if (documentProcessingListeners && projectId) {
      return documentProcessingListeners[Number(projectId)];
    }
    return null;
  }, [documentProcessingListeners, projectId]);

  return (
    <Container>
      <Flex vertical align='center' justify='center' gap={60}>
        <Header>No scopes found</Header>
        <Flex vertical align='center' justify='center' gap={32}>
          <img src={noScopesFoundIcon} alt='No scopes found' />
          <Description>Please upload documents</Description>
        </Flex>
      </Flex>
      {documentProcessingStatus === DocProcessingJobStatus.inProgress ? (
        <Tooltip title='Documents are currently being processed'>
          <Button type='primary' disabled={true}>
            <MdOutlineFileUpload size={22} />
            Upload Documents
          </Button>
        </Tooltip>
      ) : (
        <Button type='primary' onClick={() => setIsUploadDocumentModalOpen(true)}>
          <MdOutlineFileUpload size={22} />
          Upload Documents
        </Button>
      )}
      {isUploadDocumentModalOpen && (
        <UploadDocuments
          isDocumentsScreen={false}
          isUploadDocumentModalOpen={isUploadDocumentModalOpen}
          setIsUploadDocumentModalOpen={setIsUploadDocumentModalOpen}
        />
      )}
    </Container>
  );
};

export default NoScopeComponent;
