import { <PERSON><PERSON>, Modal, Space, Table } from "antd";
import styled from "styled-components";
import { themeTokens } from "src/theme/tokens";
import { GetProjectDocumentsWithVersionsInputDocumentTypeEnum } from "src/api";

interface DataType {
  title: string;
  inputDocumentType: GetProjectDocumentsWithVersionsInputDocumentTypeEnum;
  documentSetVersion: string;
  createdAt: string;
  createdByName: string;
}

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
`;

export const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

export const HeaderWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const StyledModal = styled(Modal)`
  & .ant-modal-content {
    left: 125px;
  }
`;
export const FileNameEllipsis = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: '100%';
`;

export const DocumentTypeWrapper = styled.span`
  text-transform: capitalize;
`;

export const DateAndButtonsWrapper = styled(Space)`
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
`;

export const DocumentName = styled.a`
  cursor: 'pointer';
`;

export const StyledTable = styled(Table<DataType>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: ${themeTokens.primaryDark};
    }
  }

  /*Don't have properties in theme config for filter and icons bg color /*
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: ${themeTokens.textLight};
  }
  .ant-table-filter-trigger.active {
    color: ${themeTokens.textLight};
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: ${themeTokens.textLight};
  }
`;

export const StyledButton = styled(Button)`
  width: 90px;
`;