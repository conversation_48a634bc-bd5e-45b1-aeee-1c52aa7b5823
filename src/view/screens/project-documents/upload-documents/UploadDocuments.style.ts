import { Button } from "antd";
import styled from "styled-components";
import { themeTokens } from "src/theme/tokens";

export const StyledButton = styled(Button)`
  border: 1px solid ${themeTokens.buttonBorder};
`;

export const StyledSaveButton = styled(Button)`
  background-color: ${themeTokens.buttonDark};
`;

export const LoaderContainer = styled.div`
  width: 100%;
  height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
`;

export const LoadingInfoTitle = styled.p<{ isTitleAvailable?: boolean }>`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: ${({ isTitleAvailable }) => (isTitleAvailable ? 500 : 400)};
  font-size: ${({ isTitleAvailable }) => (isTitleAvailable ? 26 : 20)}px;
  line-height: 25px;
  text-align: center;
`;

export const LoadingInfoDescription = styled.p`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
`;

export const StyledLabel = styled.div`
  font-family: inter;
  color: ${themeTokens.textGray};
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
`;
