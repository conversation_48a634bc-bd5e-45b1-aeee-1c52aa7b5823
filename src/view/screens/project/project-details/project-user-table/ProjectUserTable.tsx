import { DeleteOutlined, ShareAltOutlined, EditOutlined, FilterFilled } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { App, Button, Empty, Modal, Popconfirm, Select, Tooltip, Form, Flex } from 'antd';
import type { ColumnType } from 'antd/es/table';
import { useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { ProjectUserCreateDTO, ProjectUserDTO, ProjectUserUpdateDTO, UserDTO } from 'src/api';
import { projectUserAPI, userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';

import ColumnFilter from 'src/modules/common/columnFilter';
import HasProjectPermission from 'src/modules/guards/HasProjectPermission';
import { queryKeys } from 'src/modules/utils/constant';
import { extractErrorMessage } from 'src/modules/utils/errorHandler';
import { UserProjectPermission } from 'src/modules/utils/permissions';
import { Container, StyledTable, Title } from './ProjectUserTable.style';

const ProjectUserTable: React.FC = () => {
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<ProjectUserDTO | null>(null);

  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const queryClient = useQueryClient();
  const { projectId } = useParams();

  // Fetch project users
  const { data: projectUsers, isLoading } = useQuery({
    queryKey: [queryKeys.projectUsers, projectId],
    queryFn: () => projectUserAPI.getProjectUsersByProject(Number(projectId)),
    select: res => res.data?.content || [],
    enabled: !!projectId
  });

  // Fetch all users
  const { data: users, isLoading: isLoadingUsers } = useQuery({
    queryKey: [queryKeys.usersList],
    //TODO: need to change
    queryFn: () => userAPI.getAllUsers(0, 1000),
    select: res => res.data?.content || [],
    enabled: isShareModalOpen
  });

  // Add project user mutation
  const { mutate: addProjectUser, isPending: isAddingUser } = useMutation({
    mutationFn: (data: ProjectUserCreateDTO) => projectUserAPI.createProjectUser(data),
    onSuccess: () => {
      notification.success({ message: 'User added to project successfully' });
      queryClient.invalidateQueries({ queryKey: [queryKeys.projectUsers, projectId] });
      setIsShareModalOpen(false);
      form.resetFields();
    },
    onError: error => {
      notification.error({
        message: 'Failed to add user',
        description: extractErrorMessage(error, 'Failed to add user to project')
      });
    }
  });

  // Delete project user mutation
  const { mutate: deleteProjectUser } = useMutation({
    mutationFn: (id: number) => projectUserAPI.deleteProjectUser(id),
    onSuccess: () => {
      notification.success({ message: 'User removed from project successfully' });
      queryClient.invalidateQueries({ queryKey: [queryKeys.projectUsers, projectId] });
    },
    onError: error => {
      notification.error({
        message: 'Failed to remove user',
        description: extractErrorMessage(error, 'Failed to remove user from project')
      });
    }
  });

  // Add update project user mutation
  const { mutate: updateProjectUser, isPending: isUpdatingUser } = useMutation({
    mutationFn: (args: { id: number; data: ProjectUserUpdateDTO }) =>
      projectUserAPI.updateProjectUser(args.id, args.data),
    onSuccess: () => {
      notification.success({ message: 'User role updated successfully' });
      queryClient.invalidateQueries({ queryKey: [queryKeys.projectUsers, projectId] });
      setIsShareModalOpen(false);
      form.resetFields();
      setEditingUser(null);
    },
    onError: error => {
      notification.error({
        message: 'Failed to update user role',
        description: extractErrorMessage(error, 'Failed to update user role')
      });
    }
  });

  const handleOpenShareModal = useCallback(
    (user?: ProjectUserDTO) => {
      if (user) {
        setEditingUser(user);
        form.setFieldsValue({
          userId: user.userId,
          projectRole: user.projectRole
        });
      } else {
        setEditingUser(null);
        form.resetFields();
      }
      setIsShareModalOpen(true);
    },
    [form]
  );

  const handleCloseShareModal = useCallback(() => {
    setIsShareModalOpen(false);
    form.resetFields();
    setEditingUser(null);
  }, [form]);

  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (editingUser?.projectUserId) {
        // Update existing user
        updateProjectUser({
          id: editingUser.projectUserId,
          data: {
            projectRole: values.projectRole
          }
        });
      } else {
        // Add new user
        const projectUserData: ProjectUserCreateDTO = {
          projectId: Number(projectId),
          userId: values.userId,
          projectRole: values.projectRole
        };
        addProjectUser(projectUserData);
      }
    } catch {
      notification.error({ message: 'Validation failed' });
    }
  }, [form, editingUser, updateProjectUser, projectId, addProjectUser, notification]);

  const columns: ColumnType<ProjectUserDTO>[] = [
    {
      title: 'User',
      dataIndex: 'name',
      sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
      ...ColumnFilter('name')
    },
    {
      title: 'Email',
      dataIndex: 'email',
      sorter: (a, b) => (a.email || '').localeCompare(b.email || ''),
      ...ColumnFilter('email')
    },
    {
      title: 'Role',
      dataIndex: 'projectRole',
      sorter: (a, b) => (a.projectRole || '').localeCompare(b.projectRole || ''),
      filters: [
        { text: 'Admin', value: 'Admin' },
        { text: 'Editor', value: 'Editor' },
        { text: 'Viewer', value: 'Viewer' }
      ],
      filterIcon: (filtered: boolean) => (
        <FilterFilled style={{ color: filtered ? themeTokens.lightBlue : undefined }} />
      ),
      onFilter: (value, record: ProjectUserDTO) => record.projectRole === value
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: unknown, record: ProjectUserDTO) => (
        <Flex gap={8}>
          <Tooltip title='Edit role'>
            <EditOutlined
              style={{ cursor: 'pointer' }}
              onClick={() => handleOpenShareModal(record)}
            />
          </Tooltip>
          <HasProjectPermission requiredPermissions={[UserProjectPermission.REMOVE_PROJECT_USER]}>
            <Tooltip title='Remove user'>
              <Popconfirm
                title='Are you sure you want to remove this user from the project?'
                onConfirm={() => record.projectUserId && deleteProjectUser(record.projectUserId)}
                okText='Yes'
                cancelText='No'
                placement='left'
              >
                <DeleteOutlined style={{ cursor: 'pointer' }} />
              </Popconfirm>
            </Tooltip>
          </HasProjectPermission>
        </Flex>
      )
    }
  ];

  return (
    <Container vertical gap={24}>
      <Flex justify='space-between' align='center'>
        <Title>Project Users</Title>
        <HasProjectPermission requiredPermissions={[UserProjectPermission.INVITE_PROJECT_USER]}>
          <Button type='primary' icon={<ShareAltOutlined />} onClick={() => handleOpenShareModal()}>
            Share Project
          </Button>
        </HasProjectPermission>
      </Flex>

      <StyledTable
        bordered
        columns={columns}
        dataSource={projectUsers}
        rowKey='projectUserId'
        loading={isLoading}
        pagination={false}
        locale={{ emptyText: <Empty description='No users assigned to this project' /> }}
      />

      <Modal
        title={editingUser ? 'Edit Project Role' : 'Share Project'}
        open={isShareModalOpen}
        maskClosable={false}
        onCancel={handleCloseShareModal}
        onOk={handleSubmit}
        confirmLoading={isAddingUser || isUpdatingUser}
        okText={editingUser ? 'Update' : 'Share'}
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            name='userId'
            label='User'
            rules={[{ required: true, message: 'Please select a user' }]}
          >
            <Select
              placeholder='Select a user'
              loading={isLoadingUsers}
              showSearch
              optionFilterProp='label'
              disabled={!!editingUser}
            >
              {users?.map((user: UserDTO) => (
                <Select.Option key={user.id} value={user.id} label={`${user.name} (${user.email})`}>
                  {user.name} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name='projectRole'
            label='Role'
            rules={[{ required: true, message: 'Please select a role' }]}
          >
            <Select placeholder='Select a role'>
              <Select.Option value='Admin'>Project Admin</Select.Option>
              <Select.Option value='Editor'>Project Editor</Select.Option>
              <Select.Option value='Viewer'>Project Viewer</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default ProjectUserTable;
