import styled from 'styled-components';
import { Flex, Table } from 'antd';
import type { TableProps } from 'antd';

export const Container = styled(Flex)`
  padding-bottom: 48px;
`;

export const StyledTable = styled(Table)<TableProps>`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    display: none;
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }

  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

export const Title = styled.h3`
  font-weight: 500;
  font-size: 20px;
`;
