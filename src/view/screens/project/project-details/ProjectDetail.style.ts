import styled from "styled-components";
import { Card, Row, Col } from 'antd';

export const ContainerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
  position: relative;
`;

export const Container = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

export const Header = styled.div`
  font-weight: 500;
  font-size: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
`;

export const ProjectCard = styled(Card)`
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  .ant-card-body {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
`;

export const InfoRow = styled(Row)`
  display: flex;
  flex-wrap: wrap;
`;

export const InfoItem = styled(Col)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const DocumentInfoItem = styled(Col)`
  display: flex;
  align-items: flex-start;
  gap: 8px;
`;

export const InfoIcon = styled.div`
  font-size: 16px;
  color: #5f6368;
`;

export const InfoContent = styled.div`
  display: flex;
  flex-direction: column;
`;

export const InfoLabel = styled.div`
  font-weight: 600;
  font-size: 16px;
`;

export const InfoValue = styled.div`
  font-weight: 400;
  font-size: 16px;
  color: #4e4e4e;
`;

export const FileList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-top: 4px;
`;

export const FileItem = styled.div`
  font-size: 14px;
`;

export const ContentWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32px;
`;