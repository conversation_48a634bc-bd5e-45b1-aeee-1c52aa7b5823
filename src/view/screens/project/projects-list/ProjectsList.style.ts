import styled from 'styled-components';
import { Button, Input } from 'antd';
import { themeTokens } from 'src/theme/tokens';

export const Container = styled.div`  
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 24px;
  font-family: 'Inter';
`;

export const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

export const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

export const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 24px 0;
  background: ${themeTokens.pageBg};
`;

export const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

export const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

export const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;