import { Button, Input, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 24px;
  font-family: 'Inter';
`;

export const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

export const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

export const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 24px 0;
  background: ${themeTokens.pageBg};
`;

export const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

export const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

export const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;

export const ViewToggleContainer = styled.div`
  display: flex;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
`;

export const ViewToggleButton = styled.div<{ active: boolean }>`
  padding: 8px 12px;
  cursor: pointer;
  background-color: ${({ active }) => (active ? themeTokens.primaryColor : 'white')};
  color: ${({ active }) => (active ? 'white' : '#666')};
  border-right: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:last-child {
    border-right: none;
  }

  &:hover {
    background-color: ${({ active }) => (active ? themeTokens.primaryColor : '#f5f5f5')};
  }
`;

export const StyledTable = styled(Table)`
  margin-bottom: 20px;
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: white;
    font-weight: 600;
  }

  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;
