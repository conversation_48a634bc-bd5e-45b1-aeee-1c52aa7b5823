import { Button, DatePicker, Flex, Input ,Col} from "antd";
import { themeTokens } from "src/theme/tokens";
import styled from "styled-components";

export const StyledDatePicker = styled(DatePicker)`
  border-radius: 10px;
  padding: 8px 10px;
  width: 100%;
`;

export const StyledDescription = styled(Input.TextArea)`
  resize: none !important;
`;

export const StyledFlex = styled(Flex)`
  width: 100%;
`;

export const StyledButton = styled(Button)`
  font-family: Roboto;
  font-weight: 400;
  font-size: 17px;
  border: 1px solid ${themeTokens.buttonBorder};
  padding: 16px 8px;
  border-radius: 4px;
`;

export const StyledSaveButton = styled(Button)`
  font-family: Roboto;
  font-weight: 400;
  font-size: 17px;
  padding: 16px 10px;
  border-radius: 4px;
  background-color: ${themeTokens.buttonDark};
  color: ${themeTokens.textLight};
`;

export const StyledInput = styled(Input)`
  border-radius: 4px;
  border: 1px solid ${themeTokens.inputBorder};
  padding: 8px 10px;
`;

export const LoaderContainer = styled.div`
  width: 100%;
  height: 100%;
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
`;

export const LoadingInfoTitle = styled.p<{ isTitleAvailable?: boolean }>`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: ${({ isTitleAvailable }) => (isTitleAvailable ? 500 : 400)};
  font-size: 26px;
  line-height: 40px;
  text-align: center;
`;

export const LoadingInfoDescription = styled.p`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
`;

export const UploadFilesWrapper = styled(Col)`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;