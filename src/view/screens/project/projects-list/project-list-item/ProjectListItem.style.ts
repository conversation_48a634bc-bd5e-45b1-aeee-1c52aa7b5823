import { Card } from "antd";
import styled from "styled-components";
import { themeTokens } from "src/theme/tokens";
import { Link } from "react-router-dom";

export const StyledCard = styled(Card)`
    width: 100%;
    position: relative;
    height: 100%;
  
    .action-icons {
      display: none;
      position: absolute;
      right: 24px;
    }
  
    &:hover .action-icons {
      display: flex;
    }
  
    .project-status {
      position: absolute;
      right: 24px;
      top: 10px;
    }
  `;
  
  export const ActionIcon = styled.span`
    font-size: 18px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.65);
  `;
  
  export const ProjectTitle = styled.div`
    font-family: 'Inter';
    font-weight: 500;
    font-size: 27px;
    line-height: 28px;
  `;
  
  export const ProjectLabel = styled.span`
    font-weight: 500;
    font-size: 16px;
  `;
  
  export const ProjectDescription = styled.span`
    font-weight: 600;
    font-size: 16px;
  `;
  
  export const LoaderContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 10px;
  `;
  
  export const ProjectName = styled(Link)`
    font-family: 'Inter';
    font-size: 27px;
    line-height: 28px;
    font-weight: 500;
    color: ${themeTokens.linkBlue};
    text-decoration: none;
  
    &:hover {
      color: ${themeTokens.linkBlue};
      text-decoration: underline;
    }
  `;
  
  export const ProjectDetails = styled.div`
    font-weight: 400;
    font-size: 16px;
    line-height: 18px;
  `;
  
  export const Ellipsis = styled.span<{ width?: string }>`
    max-width: ${({ width }) => width || '100%'};
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  `;