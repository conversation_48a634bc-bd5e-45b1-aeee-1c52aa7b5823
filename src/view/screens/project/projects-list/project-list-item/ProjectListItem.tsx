import {
  EyeOutlined,
  DeleteOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { Card, Divider, Flex, Tag, Tooltip } from 'antd';
import { useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ProjectDTO } from 'src/api';
import HasProjectPermission from 'src/modules/guards/HasProjectPermission';
import { DocProcessingJobStatus, appRoutes } from 'src/modules/utils/constant';
import { UserProjectPermission } from 'src/modules/utils/permissions';
import useGlobalStore from 'src/store/useGlobalStore';
import {
  StyledCard,
  Ellipsis,
  ProjectName,
  ProjectDetails,
  ProjectLabel,
  ActionIcon,
  ProjectDescription,
  ProjectTitle,
  LoaderContainer
} from './ProjectListItem.style';

enum ProjectDisplayStatus {
  processing = 'Processing',
  processed = 'Processed',
  error = 'Error'
}

const ProjectListItem: React.FC<{
  project: ProjectDTO;
  onProjectClickHandler: (id?: number) => void;
  handleDeleteProject: (id?: number) => void;
}> = ({ project, onProjectClickHandler, handleDeleteProject }) => {
  const navigate = useNavigate();
  const { documentProcessingListeners } = useGlobalStore();
  const status = useMemo(() => {
    if (documentProcessingListeners && project.projectId) {
      return documentProcessingListeners[project.projectId];
    }
    return null;
  }, [documentProcessingListeners, project.projectId]);

  const projectDocumentsStatus = useMemo(() => {
    switch (status) {
      case DocProcessingJobStatus.inProgress:
      case DocProcessingJobStatus.queued:
        return {
          status: ProjectDisplayStatus.processing,
          icon: <SyncOutlined spin />,
          color: 'processing'
        };
      case DocProcessingJobStatus.failed:
        return {
          status: ProjectDisplayStatus.error,
          icon: <CloseCircleOutlined />,
          color: 'error'
        };
      case DocProcessingJobStatus.completed:
        return {
          status: ProjectDisplayStatus.processed,
          icon: <CheckCircleOutlined />,
          color: 'success'
        };
      default:
        return null;
    }
  }, [status]);

  return (
    <StyledCard>
      <Flex vertical>
        <Flex gap={15} vertical>
          <Ellipsis width='90%'>
            <ProjectName
              to={
                (project?.scopeCount ? project?.scopeCount : 0) > 0
                  ? `${project?.projectId}/${appRoutes.scopes}`
                  : `${project?.projectId}/${appRoutes.documents}`
              }
              onClick={() => onProjectClickHandler(project?.projectId)}
            >
              <Tooltip title={project?.name}> {project?.name}</Tooltip>
            </ProjectName>
          </Ellipsis>
          <Flex gap={10} vertical>
            <ProjectDetails>
              <Ellipsis width='90%'>
                <ProjectLabel>Address:</ProjectLabel>
                &nbsp;{project?.location}
              </Ellipsis>
            </ProjectDetails>
            <ProjectDetails>
              <ProjectLabel>Owner Contact Info:</ProjectLabel>
              &nbsp;{project?.owner}
            </ProjectDetails>
            <LoaderContainer>
              <ProjectLabel>Documents status:</ProjectLabel>
              <Tag icon={projectDocumentsStatus?.icon} color={projectDocumentsStatus?.color}>
                {projectDocumentsStatus?.status}
              </Tag>
            </LoaderContainer>
          </Flex>
        </Flex>
        <Divider />
        <ProjectDetails>
          <Ellipsis>
            <ProjectDescription>Description:</ProjectDescription>
            <Tooltip title={project?.description}>&nbsp;{project?.description || '-'}</Tooltip>
          </Ellipsis>
        </ProjectDetails>
        <Flex gap={16} align='center' className='action-icons'>
          <Tooltip title='View Project'>
            <ActionIcon
              onClick={() => {
                onProjectClickHandler(project?.projectId);
                navigate(`${project?.projectId}`);
              }}
            >
              <EyeOutlined />
            </ActionIcon>
          </Tooltip>
          <HasProjectPermission requiredPermissions={[UserProjectPermission.DELETE_PROJECT]}>
            <Tooltip title='Delete Project'>
              <ActionIcon onClick={() => handleDeleteProject(project?.projectId)}>
                <DeleteOutlined />
              </ActionIcon>
            </Tooltip>
          </HasProjectPermission>
        </Flex>
      </Flex>
    </StyledCard>
  );
};

export default ProjectListItem;
