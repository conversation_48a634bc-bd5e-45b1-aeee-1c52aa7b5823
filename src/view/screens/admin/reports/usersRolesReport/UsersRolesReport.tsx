import { ArrowLeftOutlined, DownloadOutlined, UserOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  ExportButton,
  StyledTable,
  StatusBadge,
  RoleBadge,
  ProjectInfo,
  ProjectName,
  ProjectNumber,
  UserName,
  UserEmail,
  RolesContainer,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext
} from './UsersRolesReport.style';
import { appRoutes } from '../../../../../modules/utils/constant';

interface UserRoleData {
  key: string;
  name: string;
  email: string;
  active: boolean;
  roles: string[];
  projectName: string;
  projectNumber: string;
}

const UsersRolesReport: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handleExport = () => {
    notification.success({
      message: 'Export Started',
      description: 'Users & Roles report is being exported. You will receive an email when ready.'
    });
  };

  // Mock data for the table
  const mockData: UserRoleData[] = [
    {
      key: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      active: true,
      roles: ['Admin', 'Project Manager'],
      projectName: 'Office Building A',
      projectNumber: 'PRJ-001'
    },
    {
      key: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      active: true,
      roles: ['Manager'],
      projectName: 'Residential Complex B',
      projectNumber: 'PRJ-002'
    },
    {
      key: '3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      active: true,
      roles: ['User'],
      projectName: 'Shopping Mall C',
      projectNumber: 'PRJ-003'
    },
    {
      key: '4',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      active: false,
      roles: ['User'],
      projectName: 'Warehouse D',
      projectNumber: 'PRJ-004'
    },
    {
      key: '5',
      name: 'David Brown',
      email: '<EMAIL>',
      active: true,
      roles: ['Project Manager', 'User'],
      projectName: 'Office Building A',
      projectNumber: 'PRJ-001'
    },
    {
      key: '6',
      name: 'Lisa Garcia',
      email: '<EMAIL>',
      active: true,
      roles: ['Manager'],
      projectName: 'Shopping Mall C',
      projectNumber: 'PRJ-003'
    },
    {
      key: '7',
      name: 'Tom Anderson',
      email: '<EMAIL>',
      active: true,
      roles: ['Admin'],
      projectName: 'Residential Complex B',
      projectNumber: 'PRJ-002'
    },
    {
      key: '8',
      name: 'Emily Davis',
      email: '<EMAIL>',
      active: false,
      roles: ['User'],
      projectName: 'Warehouse D',
      projectNumber: 'PRJ-004'
    }
  ];

  const columns: ColumnsType<UserRoleData> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string, record: UserRoleData) => (
        <div>
          <UserName>{name}</UserName>
          <UserEmail>{record.email}</UserEmail>
        </div>
      )
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 250
    },
    {
      title: 'Active',
      dataIndex: 'active',
      key: 'active',
      width: 100,
      render: (active: boolean) => (
        <StatusBadge active={active}>{active ? 'Active' : 'Inactive'}</StatusBadge>
      )
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (roles: string[]) => (
        <RolesContainer>
          {roles.map((role, index) => (
            <RoleBadge key={index} role={role}>
              {role}
            </RoleBadge>
          ))}
        </RolesContainer>
      )
    },
    {
      title: 'Project Name',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200
    },
    {
      title: 'Project Number',
      dataIndex: 'projectNumber',
      key: 'projectNumber',
      width: 150,
      render: (projectNumber: string, record: UserRoleData) => (
        <ProjectInfo>
          <ProjectName>{record.projectName}</ProjectName>
          <ProjectNumber>{projectNumber}</ProjectNumber>
        </ProjectInfo>
      )
    }
  ];

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Users & Roles Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <ExportButton type='primary' icon={<DownloadOutlined />} onClick={handleExport}>
            Export Report
          </ExportButton>
        </HeaderActions>
      </Header>

      <StyledTable
        columns={columns}
        dataSource={mockData}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`
        }}
        rowKey='key'
        bordered
        rowHoverable
        locale={{
          emptyText: (
            <EmptyState>
              <EmptyIcon>
                <UserOutlined />
              </EmptyIcon>
              <EmptyText>No users found</EmptyText>
              <EmptySubtext>There are no users to display in this report</EmptySubtext>
            </EmptyState>
          )
        }}
        scroll={{ y: 'calc(100vh - 300px)' }}
      />
    </Container>
  );
};

export default UsersRolesReport;
