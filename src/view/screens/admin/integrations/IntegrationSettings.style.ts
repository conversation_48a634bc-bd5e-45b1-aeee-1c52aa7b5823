import { <PERSON><PERSON>, Card, Flex, Typography } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;
export const Container = styled.div`
  padding: 32px;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const IntegrationCard = styled(Card)`
  max-width: 420px;
  border: 1px solid ${themeTokens.inputBorder};
  display: flex;
`;

export const IntegrationCardWrapper = styled(Flex)`
  flex-direction: column;
  gap: 32px;
`;

export const IntegrationRow = styled(Flex)`
  align-items: center;
  gap: 16px;
`;

export const TextLabel = styled(Typography.Text)`
  width: 180px;
`;

export const UpdateButtonRow = styled(Flex)`
  width: 420px;
  justify-content: flex-end;
`;
