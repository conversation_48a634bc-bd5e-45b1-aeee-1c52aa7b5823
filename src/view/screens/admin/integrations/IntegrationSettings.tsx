import { ArrowLeftOutlined } from '@ant-design/icons';
import { Switch, Button, Typography, Flex } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { appRoutes } from 'src/modules/utils/constant';
import {
  Container,
  BackButton,
  HeaderLeft,
  IntegrationCard,
  IntegrationCardWrapper,
  IntegrationRow,
  PageTitle,
  TextLabel,
  UpdateButtonRow
} from './IntegrationSettings.style';

const { Text } = Typography;

const IntegrationsSettings: React.FC = () => {
  const [procoreEnabled, setProcoreEnabled] = useState(true);
  const [autodeskEnabled, setAutodeskEnabled] = useState(true);
  const [saving, setSaving] = useState(false);
  const navigate = useNavigate();

  const handleUpdate = () => {
    setSaving(true);
    // TODO: Call API to save integration settings
    setTimeout(() => {
      setSaving(false);
    }, 1000);
  };
  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };
  return (
    <Container>
      <HeaderLeft>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'></BackButton>
        <PageTitle>Integration Settings</PageTitle>
      </HeaderLeft>
      <IntegrationCardWrapper>
        <IntegrationCard>
          <Flex gap={16} vertical>
            <Text strong>Enable Integrations</Text>
            <IntegrationRow>
              <TextLabel>Procore</TextLabel>
              <Switch checked={procoreEnabled} onChange={setProcoreEnabled} />
            </IntegrationRow>
            <IntegrationRow>
              <TextLabel>Autodesk</TextLabel>
              <Switch checked={autodeskEnabled} onChange={setAutodeskEnabled} />
            </IntegrationRow>
          </Flex>
        </IntegrationCard>
      </IntegrationCardWrapper>
      <UpdateButtonRow>
        <Button type='primary' onClick={handleUpdate} loading={saving}>
          Update
        </Button>
      </UpdateButtonRow>
    </Container>
  );
};

export default IntegrationsSettings;
