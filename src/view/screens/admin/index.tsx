import { Card, Col, Row } from 'antd';
import React, { useState } from 'react';
import { FaUsers, FaBuilding, FaPlug } from 'react-icons/fa';
import { GrTemplate } from 'react-icons/gr';
import { useNavigate } from 'react-router-dom';
import projectIcon from 'src/assets/images/project.svg';
import { appRoutes } from 'src/modules/utils/constant';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CompanySettingsModal from './company/CompanySettingsModal';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 32px;
`;

const StyledCard = styled(Card)`
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${themeTokens.inputBorder};

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: ${themeTokens.primaryColor};
  }

  .ant-card-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
  }
`;

const IconWrapper = styled.div`
  font-size: 48px;
  color: ${themeTokens.primaryColor};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const TileTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 8px;
`;

const TileDescription = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin: 0;
  line-height: 1.4;
`;

const ProjectIcon = styled.img`
  width: 48px;
  height: 48px;
  filter: brightness(59%) saturate(155%) invert(27%) sepia(51%) saturate(3278%) hue-rotate(338deg)
    contrast(100%);
`;

interface AdminTile {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  route?: string;
  comingSoon?: boolean;
}

const Admin: React.FC = () => {
  const navigate = useNavigate();
  const [companySettingsOpen, setCompanySettingsOpen] = useState(false);

  const adminTiles: AdminTile[] = [
    {
      key: 'user-management',
      title: 'User Management',
      description: 'Manage companies, users, and license provisioning',
      icon: <FaUsers />,
      route: `/${appRoutes.admin}/user-management`
    },
    {
      key: 'projects',
      title: 'Projects',
      description: 'View & manage projects',
      icon: <ProjectIcon src={projectIcon} alt='Projects' />,
      route: `/${appRoutes.admin}/projects`
    },
    {
      key: 'company-settings',
      title: 'Company Settings',
      description: 'View and configure company-level settings and logo',
      icon: <FaBuilding />
      // No route, handled by modal
    },
    {
      key: 'integrations',
      title: 'Integrations',
      description: 'Enable or disable third-party integrations for your company',
      icon: <FaPlug />,
      route: `/${appRoutes.admin}/integrations`
    },
    {
      key: 'templates',
      title: 'Templates',
      description: 'Upload and manage company-specific templates',
      icon: <GrTemplate />,
      route: `/${appRoutes.admin}/templates`
    }
    // will be used in future
    // {
    //   key: 'reports',
    //   title: 'Reports',
    //   description:
    //     'Generate, view, and export detailed project data summaries with customizable filters',
    //   icon: <FaChartBar />,
    //   route: `/${appRoutes.admin}/reports`
    // }
  ];

  const handleTileClick = (tile: AdminTile) => {
    if (tile.comingSoon) {
      return;
    }
    if (tile.key === 'company-settings') {
      setCompanySettingsOpen(true);
      return;
    }
    if (tile.route) {
      navigate(tile.route);
    }
  };

  return (
    <Container>
      <PageTitle>Admin</PageTitle>
      <Row gutter={[24, 24]}>
        {adminTiles.map(tile => (
          <Col xs={24} sm={12} lg={8} key={tile.key}>
            <StyledCard onClick={() => handleTileClick(tile)}>
              <IconWrapper>{tile.icon}</IconWrapper>
              <TileTitle>{tile.title}</TileTitle>
              <TileDescription>{tile.description}</TileDescription>
            </StyledCard>
          </Col>
        ))}
      </Row>
      {companySettingsOpen && (
        <CompanySettingsModal
          open={companySettingsOpen}
          onClose={() => setCompanySettingsOpen(false)}
        />
      )}
    </Container>
  );
};

export default Admin;
