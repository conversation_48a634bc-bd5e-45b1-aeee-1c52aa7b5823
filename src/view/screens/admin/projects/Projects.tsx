import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Space,
  Tag,
  Tooltip,
  Select,
  Modal,
  Form,
  Popconfirm,
  notification,
  Flex,
  Pagination
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectDTO } from 'src/api';
import { projectAPI, userAPI } from 'src/api/apiClient';
import ExportFile from 'src/modules/common/exportFile';
import { themeTokens } from 'src/theme/tokens';

import {
  TooltipContent,
  StyledTableContainer,
  FilterButton,
  FilterButtonContainer,
  FilterDropdownContainer,
  FilterInput,
  GlobalSearchInput,
  StyledTable,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontaine<PERSON>
} from './Projects.style';
import { queryKeys, appRoutes } from '../../../../modules/utils/constant';
import { extractErrorMessage } from '../../../../modules/utils/errorHandler';
import { formatDate } from '../../../../modules/utils/util';

interface ExtendedProjectDTO extends ProjectDTO {
  companyName?: string;
  subscriptionType?: string;
  subscriptionStatus?: string;
  bidCaptainName?: string;
  numberOfVersions?: number;
}

const AdminProjects: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});
  const [changeOwnerModalVisible, setChangeOwnerModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ExtendedProjectDTO | null>(null);

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  // Fetch projects data
  const { data: projectsData, isLoading } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        undefined, // companyId - fetch all projects
        debouncedSearchText || undefined,
        undefined, // description
        undefined, // location
        columnFilters.status || undefined,
        undefined, // owner
        currentPage - 1,
        pageSize,
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined
      ),
    select: response => response.data
  });

  // Fetch users for change owner modal
  const { data: usersData } = useQuery({
    queryKey: [queryKeys.usersList],
    queryFn: () =>
      userAPI.getAllUsers(undefined, undefined, undefined, undefined, undefined, 0, 1000),
    select: response => response.data.content
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    const newFilters: Record<string, any> = {};
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        newFilters[key] = filters[key][0];
      }
    });
    setColumnFilters(newFilters);

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  const handleChangeOwner = useCallback((project: ExtendedProjectDTO) => {
    setSelectedProject(project);
    setChangeOwnerModalVisible(true);
  }, []);

  const handleChangeOwnerModalClose = useCallback(() => {
    setChangeOwnerModalVisible(false);
    setSelectedProject(null);
    form.resetFields();
  }, [form]);

  const handleChangeOwnerSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (selectedProject) {
        // API call to update project owner
        // await projectAPI.updateProject(selectedProject.id, { bidCaptainId: values.bidCaptain });
        notification.success({ message: 'Project owner changed successfully' });
        handleChangeOwnerModalClose();
      }
    } catch (error) {
      console.error('Failed to change owner:', error);
    }
  }, [form, selectedProject]);

  // Delete project mutation
  const { mutate: deleteProject } = useMutation({
    mutationFn: (projectId: number) => projectAPI.softDeleteProject(projectId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message: 'Project deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project deletion failed') });
    }
  });

  const handleDeleteProject = useCallback(
    (projectId: number) => {
      deleteProject(projectId);
    },
    [deleteProject]
  );

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedProjectDTO> = useMemo(
    () => [
      {
        title: 'Project Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'project name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Number',
        dataIndex: 'projectNumber',
        key: 'projectNumber',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('projectNumber', 'project number'),
        render: (projectNumber: string) => projectNumber || '-'
      },
      {
        title: 'Project Type',
        dataIndex: 'projectType',
        key: 'projectType',
        width: 120,
        render: (projectType: string) => projectType || '-'
      },
      {
        title: 'Document Status',
        dataIndex: 'documentStatus',
        key: 'documentStatus',
        width: 120,
        render: (documentStatus: string) => documentStatus || '-'
      },
      {
        title: 'Subscription',
        dataIndex: 'subscriptionType',
        key: 'subscriptionType',
        width: 90,
        render: (subscriptionType: string) => (
          <Tag
            color={
              subscriptionType === 'Enterprise' ? themeTokens.successGreen : themeTokens.infoBlue
            }
          >
            {subscriptionType || 'Non-Enterprise'}
          </Tag>
        )
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 150,
        ...getColumnSelectProps('subscriptionStatus', [
          { text: 'Active', value: 'Active' },
          { text: 'Expired', value: 'Expired' },
          { text: 'Pending', value: 'Pending' }
        ]),
        render: (status: string) => {
          let color = themeTokens.warningOrange;
          if (status === 'Active') {
            color = themeTokens.successGreen;
          } else if (status === 'Expired') {
            color = themeTokens.dangerRed;
          }
          return <Tag color={color}>{status || 'Unknown'}</Tag>;
        }
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Bid Captain',
        dataIndex: 'bidCaptainName',
        key: 'bidCaptainName',
        width: 150,
        render: (bidCaptainName: string) => bidCaptainName || '-'
      },
      {
        title: 'Bid Due Date',
        dataIndex: 'bidDueDate',
        key: 'bidDueDate',
        width: 120,
        render: (bidDueDate: string) => (bidDueDate ? formatDate(bidDueDate) : '-')
      },
      {
        title: 'No.of Versions',
        dataIndex: 'numberOfVersions',
        key: 'numberOfVersions',
        width: 80,
        render: (numberOfVersions: number) => numberOfVersions || 0
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, record) => (
          <Space size='small'>
            <EditOutlined
              className='action-icon'
              onClick={() => handleChangeOwner(record)}
              title='Change Owner'
            />
            <Popconfirm
              title='Delete Project'
              description='Are you sure you want to delete this project?'
              onConfirm={() => handleDeleteProject(record.projectId!)}
              okText='Yes, Delete'
              cancelText='Cancel'
              okType='danger'
              placement='left'
            >
              <DeleteOutlined
                className='action-icon'
                title='Delete Project'
                style={{ cursor: 'pointer', color: themeTokens.primaryColor }}
              />
            </Popconfirm>
          </Space>
        )
      }
    ],
    [getColumnSearchProps, getColumnSelectProps, handleChangeOwner, handleDeleteProject]
  );

  const exportColumns = [
    { label: 'Project Name', key: 'name' },
    { label: 'Project Number', key: 'projectNumber' },
    { label: 'Project Type', key: 'projectType' },
    { label: 'Document Status', key: 'documentSatus' },
    { label: 'Subscription', key: 'subscriptionType' },
    { label: 'Subscription Status', key: 'subscriptionStatus' },
    { label: 'Created Date', key: 'createdAt' },
    { label: 'Bid Captain', key: 'bidCaptainName' },
    { label: 'Bid Due Date', key: 'bidDueDate' },
    { label: 'No.of Versions', key: 'numberOfVersions' }
  ];

  return (
    <Container>
      <HeaderContainer>
        <Header>
          <HeaderLeft>
            <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
              Back
            </BackButton>
            <PageTitle>Projects Management</PageTitle>
          </HeaderLeft>
        </Header>
        <Flex gap={24} align='center'>
          <GlobalSearchInput
            placeholder='Search across all projects...'
            allowClear
            enterButton={<SearchOutlined />}
            size='large'
            onSearch={handleGlobalSearch}
            onChange={e => {
              if (!e.target.value) {
                handleGlobalSearch('');
              }
            }}
          />
          <ExportFile
            data={
              projectsData?.content?.map(project => ({
                ...project,
                createdAt: formatDate(project.createdAt),
                bidDueDate: formatDate(project.endDate)
              })) || []
            }
            columns={exportColumns}
            filename={`projectList`}
          />
        </Flex>
      </HeaderContainer>
      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={projectsData?.content || []}
          rowKey='projectId'
          loading={isLoading}
          onChange={handleTableChange}
          rowClassName={() => 'editable-row'}
          pagination={false}
          scroll={{ y: 'calc(100vh - 350px)' }}
        />
      </StyledTableContainer>
      {projectsData && projectsData.totalElements && projectsData.totalElements > 0 ? (
        <Flex justify='center' align='center'>
          <Pagination
            current={currentPage + 1}
            pageSize={pageSize}
            total={projectsData.totalElements}
            showSizeChanger
            pageSizeOptions={['5', '10', '20', '50']}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }}
            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} projects`}
          />
        </Flex>
      ) : null}
      {/* Change Owner Modal */}
      <Modal
        title='Change bid captain'
        open={changeOwnerModalVisible}
        onOk={handleChangeOwnerSubmit}
        onCancel={handleChangeOwnerModalClose}
        okText='Update'
        cancelText='Cancel'
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            name='bidCaptain'
            label='New Bid Captain'
            rules={[{ required: true, message: 'Please select a new bid captain' }]}
          >
            <Select placeholder='Select new bid captain' showSearch optionFilterProp='children'>
              {usersData?.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default AdminProjects;
