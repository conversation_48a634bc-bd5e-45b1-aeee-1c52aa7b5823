import { Flex, Typography, Upload } from 'antd';
import styled from 'styled-components';

const { Text } = Typography;

export const ModalContainer = styled(Flex)`
  flex-direction: column;
  gap: 20px;
  padding: 16px 0;
`;

export const FieldRow = styled(Flex)`
  align-items: center;
  gap: 16px;
`;

export const Label = styled(Text)`
  width: 200px;
  font-weight: 500;
`;

export const StyledUploadWrapper = styled.div`
  width: 150px;
`;
export const UploadIcon = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
`;
export const StyledUpload = styled(Upload)<{ isImageUploaded: boolean }>`
  border: ${({ isImageUploaded }) => (isImageUploaded ? '1px dashed gray' : 'none')};
  border-radius: 10px;
`;
