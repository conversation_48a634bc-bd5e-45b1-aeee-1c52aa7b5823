import { PlusOutlined } from '@ant-design/icons';
import { Modal, Input, Flex, Typography, Image, message, GetProp, UploadProps } from 'antd';
import { RcFile, UploadChangeParam, UploadFile } from 'antd/es/upload/interface';
import React, { useState } from 'react';
import Logo from 'src/assets/images/wyreAIIcon.png';
import useGlobalStore from 'src/store/useGlobalStore';
import {
  ModalContainer,
  FieldRow,
  Label,
  StyledUploadWrapper,
  StyledUpload,
  UploadIcon
} from './CompanySettingsModal.style';

const { Text } = Typography;

interface CompanySettingsModalProps {
  open: boolean;
  onClose: () => void;
}
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const CompanySettingsModal: React.FC<CompanySettingsModalProps> = ({ open, onClose }) => {
  const [logoPreview, setLogoPreview] = useState<RcFile | undefined | null>(undefined);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const { currentUser } = useGlobalStore();

  // Fetch company info
  const companyData = {
    name: currentUser?.companyName,
    projectLimit: 10000,
    projectsUsed: 2500,
    projectsAvailable: 7500,
    subscriptionStatus: 'Active',
    logo: Logo
  };

  const onImageChangeHandler = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length === 0) {
      setLogoPreview(null);
      setFileList([]);
    } else {
      setLogoPreview(info.fileList[0].originFileObj);
      setFileList(info.fileList);
    }
  };

  const getBase64 = (file: FileType): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };
  const handleClose = () => {
    onClose();
    setLogoPreview(undefined);
  };

  const handleSave = async () => {
    try {
      message.success('Company settings updated');
      handleClose();
    } catch {
      message.error('Failed to update company settings');
    }
  };

  return (
    <Modal
      open={open}
      onCancel={handleClose}
      title={<b>Company Settings</b>}
      width='40%'
      maskClosable={false}
      okText='Save'
      onOk={handleSave}
    >
      <ModalContainer>
        <FieldRow>
          <Label>Company Name</Label>
          <Input value={companyData?.name || 'Wyre AI'} disabled style={{ width: 220 }} />
        </FieldRow>
        <FieldRow>
          <Label>Company Subscription Status</Label>
          <Input
            value={companyData?.subscriptionStatus || 'Active'}
            disabled
            style={{ width: 220 }}
          />
        </FieldRow>
        <FieldRow>
          <Label>Project Limit</Label>
          <Input value={companyData?.projectLimit || '10000'} disabled style={{ width: 80 }} />
          <Text>Used</Text>
          <Input value={companyData?.projectsUsed || '2500'} disabled style={{ width: 60 }} />
          <Text>Available</Text>
          <Input value={companyData?.projectsAvailable || '7500'} disabled style={{ width: 60 }} />
        </FieldRow>
        <Flex vertical gap={8}>
          <Flex gap={16} align='center'>
            <Label>Company Logo</Label>
            <StyledUploadWrapper>
              <StyledUpload
                isImageUploaded={!logoPreview}
                listType='picture-card'
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true
                }}
                beforeUpload={() => false}
                onPreview={handlePreview}
                onChange={info => onImageChangeHandler(info)}
                onRemove={() => {
                  setLogoPreview(null);
                  return true;
                }}
                fileList={fileList}
              >
                {!logoPreview && (
                  <UploadIcon>
                    <PlusOutlined />
                    Upload
                  </UploadIcon>
                )}
              </StyledUpload>
            </StyledUploadWrapper>
          </Flex>
          {previewImage && (
            <Image
              wrapperStyle={{ display: 'none' }}
              preview={{
                visible: previewOpen,
                onVisibleChange: visible => setPreviewOpen(visible),
                afterOpenChange: visible => !visible && setPreviewImage('')
              }}
              src={previewImage}
            />
          )}
        </Flex>
      </ModalContainer>
    </Modal>
  );
};

export default CompanySettingsModal;
