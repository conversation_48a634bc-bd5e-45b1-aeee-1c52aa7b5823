import { ArrowLeftOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Tag, Tooltip, Flex, Modal, Switch } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { MdModeEdit } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import { UserRole } from 'src/modules/utils/permissions';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import EditUser from 'src/view/screens/user/user-list/add-user/AddUser';
import {
  Container,
  Header,
  HeaderLeft,
  BackButton,
  PageTitle,
  GlobalSearchContainer,
  StyledTable,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  TooltipContent,
  EmailTooltipContent,
  GlobalSearchInput,
  ModalContainer,
  ModalTitle,
  ModalDescription
} from './Users.style';
import { queryKeys, appRoutes } from '../../../../modules/utils/constant';
import { formatDate } from '../../../../modules/utils/util';

export interface ExtendedUserDTO extends UserDTO {
  companyName?: string;
}

const AdminUserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserDTO | null>(null);
  const [isActivateModalOpen, setIsActivateModalOpen] = useState(false);
  const [isActiveUser, setIsActiveUser] = useState(true);

  const { currentUser } = useGlobalStore();

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  // Fetch users data
  const { data: usersData, isLoading } = useQuery({
    queryKey: [
      queryKeys.usersList,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      userAPI.getAllUsers(
        currentPage - 1, // page (number)
        pageSize, // size (number)
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined, // sort
        debouncedSearchText || undefined, // search text
        undefined, // email
        columnFilters.isActive || undefined, // isActive
        undefined // companyId - fetch all users
      ),
    select: response => response.data
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    const newFilters: Record<string, any> = {};
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        newFilters[key] = filters[key][0];
      }
    });
    setColumnFilters(newFilters);

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  const handleOpenEditModal = useCallback((user: UserDTO) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  }, []);

  const handleToggleActive = () => {
    setIsActivateModalOpen(true);
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedUserDTO> = useMemo(
    () => [
      {
        title: 'User ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        width: 250,
        sorter: true,
        ...getColumnSearchProps('email', 'email'),
        render: (email: string) => (
          <Tooltip title={email}>
            <EmailTooltipContent>{email || '-'}</EmailTooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <TooltipContent>{companyName || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Active User',
        dataIndex: 'isActive',
        key: 'isActive',
        width: 120,
        sorter: true,
        ...getColumnSelectProps('isActive', [
          { text: 'Active', value: 'true' },
          { text: 'Inactive', value: 'false' }
        ]),
        render: (isActive: boolean) => (
          <Tag color={isActive ? themeTokens.successGreen : themeTokens.dangerRed}>
            {isActive ? 'Yes' : 'No'}
          </Tag>
        )
      },
      {
        title: 'Create Time',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Current Role',
        dataIndex: ['role', 'name'],
        key: 'role',
        width: 150,
        render: (roleName: string) => <Tag color={themeTokens.infoBlue}>{roleName || '-'}</Tag>
      },
      {
        title: 'Last Login',
        dataIndex: 'lastLoginDate',
        key: 'lastLoginDate',
        width: 150,
        render: (lastLoginDate: string) => (lastLoginDate ? formatDate(lastLoginDate) : '-')
      },
      ...(currentUser?.role?.name === UserRole.companyAdmin ||
      currentUser?.role?.name === UserRole.superAdmin
        ? [
            {
              title: 'Actions',
              key: 'actions',
              width: 160,
              render: (_: unknown, record: UserDTO) => (
                <Flex gap={8} align='center'>
                  <Tooltip title='Edit User'>
                    <MdModeEdit
                      cursor='pointer'
                      size={20}
                      onClick={() => handleOpenEditModal(record)}
                    />
                  </Tooltip>
                  <Tooltip title={record.isActive ? 'Deactivate User' : 'Activate User'}>
                    <Switch checked={record.isActive} onClick={handleToggleActive} />
                  </Tooltip>
                </Flex>
              )
            }
          ]
        : [])
    ],
    [currentUser?.role?.name, getColumnSearchProps, getColumnSelectProps, handleOpenEditModal]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>User Management</PageTitle>
        </HeaderLeft>
      </Header>
      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all users...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
      </GlobalSearchContainer>
      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={usersData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          rowClassName={() => 'editable-row'}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: usersData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1200 }}
        />
      </StyledTableContainer>
      {isEditModalOpen && selectedUser && (
        <EditUser
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={true}
          selectedUser={selectedUser}
        />
      )}
      {isActivateModalOpen && (
        <Modal
          open={isActivateModalOpen}
          onCancel={() => setIsActivateModalOpen(false)}
          title={<ModalTitle>{'Deactivate User'}</ModalTitle>}
          width='30%'
          maskClosable={false}
          okText='Confirm'
        >
          <ModalContainer>
            <ModalDescription>
              {isActiveUser
                ? 'Are you sure you want to deactivate this user? They will lose access to the system'
                : 'Do you want to activate this user? They will regain access.'}
            </ModalDescription>
          </ModalContainer>
        </Modal>
      )}
    </Container>
  );
};

export default AdminUserManagement;
