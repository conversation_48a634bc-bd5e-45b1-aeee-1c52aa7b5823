import { ArrowLeftOutlined, UploadOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Button, Tabs, Modal, Upload, Select, Switch, Flex, Tooltip, Popconfirm } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';
import { MdPreview, MdDelete } from 'react-icons/md';
import { TbReplace } from 'react-icons/tb';
import { useNavigate } from 'react-router-dom';
import { scopeAPI } from 'src/api/apiClient';
import ColumnFilter from 'src/modules/common/columnFilter';
import { appRoutes, queryKeys } from 'src/modules/utils/constant';
import { formatDate } from 'src/modules/utils/util';
import useGlobalStore from 'src/store/useGlobalStore';
import {
  BackButton,
  Container,
  Header,
  HeaderLeft,
  PageTitle,
  StyledTable
} from './Templates.style';

const { TabPane } = Tabs;

const Templates: React.FC = () => {
  const [activeTab, setActiveTab] = useState('bidform');
  const [bidFormEnabled, setBidFormEnabled] = useState(true);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedScope, setSelectedScope] = useState<string | undefined>();
  const [fileList, setFileList] = useState<any[]>([]);

  const navigate = useNavigate();
  const { selectedProjectId, selectedVersion } = useGlobalStore();

  const { data: scopesList } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId, selectedVersion],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        String(selectedVersion?.version),
        0,
        10,
        ['division,asc']
      ),
    select: res => res.data,
    enabled: !!selectedProjectId,
    retry: false
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const initialTemplates = [
    {
      key: '1',
      scope: 'Drywall',
      fileName: 'drywall.xlsx',
      lastUpdated: 'May 12, 2025'
    },
    {
      key: '1',
      scope: 'Concrete',
      fileName: 'concrete.xlsx',
      lastUpdated: 'May 13, 2025'
    }
  ];

  const columns: ColumnsType<any> = [
    {
      title: 'Scope Name',
      dataIndex: 'scope',
      key: 'scope',
      sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
      ...ColumnFilter('scope')
    },
    {
      title: 'Bid Form Name',
      dataIndex: 'fileName',
      key: 'fileName',
      sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
      ...ColumnFilter('fileName')
    },
    {
      title: 'Last Updated',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
      render: (createdAt: string) => formatDate(createdAt)
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,

      render: (_, record) => (
        <Flex gap={8} align='center'>
          <Tooltip trigger={'hover'} title='Preview'>
            <MdPreview size={18} cursor='pointer' />
          </Tooltip>
          <Tooltip trigger={'hover'} title='Replace'>
            <TbReplace size={18} cursor='pointer' onClick={() => setUploadModalVisible(true)} />
          </Tooltip>
          <Tooltip trigger={'hover'} title='Delete'>
            <Popconfirm
              title='Delete Project'
              description='Are you sure you want to delete this template?'
              okText='Yes, Delete'
              cancelText='Cancel'
              okType='danger'
              placement='left'
            >
              <MdDelete size={18} cursor='pointer' />
            </Popconfirm>
          </Tooltip>
        </Flex>
      )
    }
  ];

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Templates</PageTitle>
        </HeaderLeft>
      </Header>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab='Bid Form' key='bidform'>
          <Flex vertical gap={24}>
            <Flex gap={16} align='center' justify='flex-end'>
              <span>Enable Bid Form</span>
              <Switch checked={bidFormEnabled} onChange={setBidFormEnabled} />
              <Button type='primary' onClick={() => setUploadModalVisible(true)}>
                Upload Template
              </Button>
            </Flex>
            <StyledTable columns={columns} dataSource={initialTemplates} pagination={false} />
          </Flex>
          {uploadModalVisible && (
            <Modal
              title='Upload Template'
              open={uploadModalVisible}
              onCancel={() => setUploadModalVisible(false)}
              closeIcon={null}
              maskClosable={false}
              okText='Update'
              cancelText='Cancel'
              okButtonProps={{ disabled: !selectedScope }}
            >
              <Flex vertical gap={16}>
                <Select
                  showSearch
                  style={{ width: '100%' }}
                  placeholder='Select Scope'
                  value={selectedScope}
                  options={
                    scopesList?.content
                      ? scopesList.content?.map(scope => ({
                          label: scope?.scopeNameWithSpecCode || scope.name || '',
                          value: String(scope.id)
                        }))
                      : []
                  }
                  onChange={value => setSelectedScope(value)}
                />
                <Upload
                  beforeUpload={file => {
                    setFileList([file]);
                    return false;
                  }}
                  fileList={fileList}
                  accept='.xlsx'
                  maxCount={1}
                  onRemove={() => setFileList([])}
                >
                  <Button icon={<UploadOutlined />}>Select File</Button>
                </Upload>
              </Flex>
            </Modal>
          )}
        </TabPane>
        <TabPane tab={<span style={{ color: '#aaa' }}>Checklist</span>} key='checklist' disabled />
        <TabPane
          tab={<span style={{ color: '#aaa' }}>Scope Of Work</span>}
          key='scopeofwork'
          disabled
        />
      </Tabs>
    </Container>
  );
};

export default Templates;
