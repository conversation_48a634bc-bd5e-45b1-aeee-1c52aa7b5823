export type TabType = 'drawings' | 'specifications';

export type FilterType = '' | 'discipline' | 'scope';

export interface DSArray {
    data: any[];
    indexes: {
        [key: string]: any;
    },
};

export interface IFilter {
    isInit: boolean;
    view1: DSArray;
    view2: DSArray;
    sortBy: DSArray;
    disciplines: DSArray;
    divisions: DSArray;
    scopes: DSArray;
};

export interface ResponseData {
    isLoading: boolean;
    disciplines: any[],
    divisions: any[],
    scopes: any[];
    scopeNames: string[];
    sheetNames: string[];
    specNames: any;
    sheetColumns: any[];
    scopeSheets: any;
    specColumns: any[];
    scopeSpecs: any;
};

export interface IPDFProps {
    projectId: string;
    drawingId: string;
    specificationId: string;
    scopeId: string;
};