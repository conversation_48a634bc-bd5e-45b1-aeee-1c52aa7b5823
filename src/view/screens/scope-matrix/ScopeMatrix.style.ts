import styled from "styled-components";
import { themeTokens } from 'src/theme/tokens';

export const Container = styled.div`
  /* Div */  
  height: 100%;
  background-color: #fff;
  
  /* Inner Div */
  display: flex;
  flex-direction: column;
`;

export const ToolContainer = styled.div`
  /* Div */    
  padding: 18px 24px;

  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const TabContainer = styled.div`
  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
`;

export const ToolFeatureContainer = styled.div`
  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
`;

export const HeaderContainer = styled.div<{ customStyle?: string }>`
  /* Div */  
  height: 100%;
  width: 100%;
  padding: 0px 24px;
  
  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  ${({ customStyle }) => customStyle && customStyle};
`;

export const HeaderContainerLabel = styled.span`
  /* Div */ 
  width: 254px;
`;

export const HeaderContainerDefault = styled.div`
    /* Div */
    height: 100%;
    width: 100%;
    
    /* Inner Div */
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #f7f7f7;
    overflow: visible;
`;

export const HeaderContainerLabelDefault = styled.span`
    padding: 0px 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

export const HeaderContainerIcon = styled.div`
  /* Div */ 
  height: 22px;
  width: 22px;
`;

export const WhiteContainer = styled.div<{ customStyle?: string }>`
  /* Div */  
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #FFFFFF;
  padding: 0px 24px;

  /* Inner Div */
  display: flex;
  align-items: center;
  gap: 12px;
  ${({ customStyle }) => customStyle && customStyle};
`;

export const WhiteContainerIcon = styled.div`
  /* Div */ 
  height: 22px;
  width: 22px;
`;

export const WhiteContainerLabel = styled.span`
  /* Div */ 
  height: 22px;
  width: 254px;
  overflow: hidden;
  
  /* Font */ 
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const IconBorder = styled.div`
  /* Div */  
  height: 24px;
  width: 24px;
  border-width: 1px;
  border-color: #D5D7DA;
  border-style: solid;
  border-radius: 8px;

  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const GrayContainer = styled.div`
  /* Div */  
  height: 100%;
  width: 100%;
  background-color: #F9FAFB;
`;

export const FilterKey = styled.span`
  color: ${themeTokens.Gray_500};
`;

export const FilterValue = styled.span`
  color: ${themeTokens.Gray_700};
`;

export const BtnOpen = styled.div`
  // ## Position
  position: absolute;
  right: 5%;
  z-index: 2;
  
  //  ## Div
  width: 47px;
  height: 24px;
  background-color: ${themeTokens.whiteBg};
  border: 1px solid ${themeTokens.Gray_300};
  border-radius: 8px;
  align-self: center;

  // ## Inner Div
  display: flex;
  align-items: center;
  justify-content: center;
  
  // ## Font
  font-size: 12px;

  // ## Other
  cursor: pointer;
  
  // ## Hover
  &:hover {
    color: ${themeTokens.primaryColor};
    background: #FFE6E0;
  };
`;