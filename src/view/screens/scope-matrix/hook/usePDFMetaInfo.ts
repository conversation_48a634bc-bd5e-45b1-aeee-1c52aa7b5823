import { useQuery } from '@tanstack/react-query';
import { DocumentWithPresignedUrlDTO } from 'src/api';
import { projectAPI, sheetAPI, specificationAPI, } from 'src/api/apiClient';
import { IPDFProps } from '../ScopeMatrix.type';

export function usePDFMetaInfo(prorps: IPDFProps) {
    const { projectId, drawingId, specificationId, scopeId } = prorps;
    // ## Get projectDetails
    const { data: project = null, isLoading: isLoadingProject } = useQuery({
        queryKey: [projectId],
        enabled: !!projectId,
        queryFn: async () => {
            try {
                const res = await projectAPI.getProjectWithDocuments(Number(projectId));
                const { data: { documents } } = res;
                const objDocuments: Record<string, DocumentWithPresignedUrlDTO> = {};
                for (const document of documents || []) {
                    if (!document.id) continue;
                    objDocuments[document.id] = document;
                };
                return { details: res.data, documents: objDocuments };
            } catch (error) {
                return { details: null, documents: null, };
            };
        },
    });

    // ## Get Drawing Sheet
    const { data: drawingSheet = null, isLoading: isLoadingDrawingSheet } = useQuery({
        queryKey: [drawingId, scopeId],
        enabled: !!drawingId && !!scopeId,
        queryFn: () => sheetAPI.getSheetById(Number(drawingId), Number(scopeId)),
        select: res => res.data,
    });

    // ## Get Specification Sheet
    const { data: specificationSheet = null, isLoading: isLoadingSpecificationSheet } = useQuery({
        queryKey: [specificationId],
        enabled: !!specificationId,
        queryFn: () => specificationAPI.getSpecificationById(Number(specificationId)),
        select: res => res.data,
    });

    const isLoading = isLoadingProject
        || isLoadingDrawingSheet
        || isLoadingSpecificationSheet;
    return {
        isLoading,

        isLoadingProject,
        project: project?.details ?? null,
        documents: project?.documents,

        isLoadingDrawingSheet,
        drawingSheet,
        drawingSheetDocument: project?.documents?.[drawingSheet?.documentId || 0] || null,

        isLoadingSpecificationSheet,
        specificationSheet,
        specificationSheetDocument: null,
    };
};