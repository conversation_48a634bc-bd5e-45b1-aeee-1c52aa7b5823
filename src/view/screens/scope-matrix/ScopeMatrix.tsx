import React, { useEffect, useRef, useState } from 'react';
import { ITableColumn, ITableColumnRow, Table, Filter, Loader, } from 'src/view/components';
import { Container, GrayContainer, } from './ScopeMatrix.style';
import { mockResponse, } from './ScopeMatrix.mock';
import ScopeMatrixToolbar from './component/ScopeMatrix.Toolbar';
import { FilterType, IFilter, IPDFProps, TabType } from './ScopeMatrix.type';
import { sortByOptions, viewOptions1, viewOptions2, getDisciplineFilterProps, getScopeFilterProps, getDivisionFilterProps, } from './ScopeMatrix.data';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { deepFilterBySheetName, filterByScope, processSelection, sortBy, sortByScopeName, transformApiResponse, transformDataForExport, getExportColumns, } from './ScopeMatrix.utils';
import { useNavigate, useParams } from 'react-router';
import { ScopeMatrixCellSheetName } from './component/ScopeMatrix.CellSheetName';
import { ScopeMatrixColSheetName } from './component/ScopeMatrix.ColSheetName';
import { ScopeMatrixRowHeader } from './component/ScopeMatrix.RowHeader';
import { ScopeMatrixRowCheck } from './component/ScopeMatrix.RowCheck';
import { ScopeMatrixPdfViewer, ScopeMatrixPdfViewerHandles } from './component/ScopeMatrix.PdfViewer';
import useGlobalStore from 'src/store/useGlobalStore';

export const ScopeMatrix: React.FC = () => {
  // ## Hooks
  const pdfViewerRef = useRef<ScopeMatrixPdfViewerHandles>(null);
  const { projectId = '' } = useParams();
  // const projectId = '122';
  const navigate = useNavigate();
  const { selectedProjectId, selectedProjectName, } = useGlobalStore();
  // ## States
  const [activeTab, setActiveTab] = useState<TabType>('drawings');
  const [activeFilter, setActiveFilter] = useState<FilterType>('');
  const [search, setSearch] = useState<string>('');
  const [isAllRowExpanded, setIsAllRowExpanded] = useState<boolean>(false);
  const [filter, setFilter] = useState<IFilter>({
    isInit: false,
    ...processSelection({
      view1: [viewOptions1[0]],
      view2: [viewOptions2[0]],
      sortBy: [sortByOptions[0]],
      disciplines: [],
      divisions: [],
      scopes: [],
    }),
  });

  // ## APIs
  const { isLoading, data: apiResponse } = useQuery({
    queryKey: ["scope-matrix"],
    queryFn: () => axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/sheet-scope-matrix?projectId=${projectId}&includeSpecs=true`),
    select: res => res.data,
  });
  // const isLoading = false;
  // const apiResponse = mockResponse;
  const data = transformApiResponse(apiResponse);

  useEffect(() => {
    if (!filter.isInit && !data?.isLoading) {
      setFilter({
        isInit: true,
        ...processSelection({
          view1: [viewOptions1[0]],
          view2: [viewOptions2[0]],
          sortBy: [sortByOptions[0]],
          disciplines: data.disciplines,
          divisions: data.divisions,
          scopes: data.scopes,
        }),
      });
    };
  }, [data]);

  // ## Computation

  if (isLoading) {
    return (
      <Loader
        text="Loading Scope Matrix..."
        size="small"
        variant="default"
      />
    );
  };

  const rawColumns = activeTab === 'drawings' ? data?.sheetColumns : data?.specColumns;
  const rawRows = activeTab === 'drawings' ? data?.scopeSheets : data?.scopeSpecs;
  const columns = sortByScopeName(filterByScope(rawColumns, filter), filter);
  const rows = deepFilterBySheetName(
    rawRows,
    search,
    filter,
    activeTab == 'drawings' ? 'discipline' : 'division'
  ).sort(
    (a, b) => sortBy(a, b, 'sheetName', 'asce')
  );

  // Prepare export data
  const exportData = transformDataForExport(rows, columns, activeTab);
  const exportColumns = getExportColumns(columns);

  // ## Functional Methods 
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  const handleFilterChange = (filter?: FilterType) => {
    setActiveFilter(filter ?? '');
  };

  const handleSearch = (search: string) => {
    setSearch(search);
  };

  const handleFilterApply = (selected: any) => {
    const newFilter = processSelection(selected);
    setFilter({ ...filter, ...newFilter });
  };

  const handleOnClickOpen = ({ drawingId, specificationId, scopeId }: Partial<IPDFProps>) => {
    const pdfProps = {
      projectId: projectId,
      drawingId: drawingId,
      specificationId: specificationId,
      scopeId: scopeId,
    };
    const linkTo = `/projects/${pdfProps.projectId}/scopes/${pdfProps.scopeId}`;
    if (true) {
      console.log("linkTo", linkTo);
      window.open(linkTo, "_blank");
    } else {
      pdfViewerRef.current?.open(pdfProps)
    };
  };

  // ## Render Methods
  const renderColumn = (props: ITableColumn) => {
    const onClick = () => setIsAllRowExpanded(!isAllRowExpanded);
    if (props.column.key == "sheetName") {
      return (
        <ScopeMatrixCellSheetName
          column={props.column}
          columnIndex={props.columnIndex}
          isAllRowExpanded={isAllRowExpanded}
          onClick={onClick}
        />
      );
    } else {
      return (
        <ScopeMatrixRowHeader
          column={props.column}
          columnIndex={props.columnIndex}
        />
      );
    };
  };

  const renderRow = (props: ITableColumnRow) => {
    const { column, row, rowLevel } = props;
    switch (column.key) {
      case "sheetName":
        return (
          <ScopeMatrixColSheetName
            {...props}
            handleOnClickOpen={rowLevel == 1 ? handleOnClickOpen : undefined}
          />
        );
      default:
        const value = parseInt(row[column.key]);
        if (value > 0) {
          return (
            <ScopeMatrixRowCheck
              handleOnClickOpen={rowLevel == 1 ? handleOnClickOpen : undefined}
              {...props}
            />
          );
        } else {
          return <GrayContainer />;
        };
    };
  };

  return (
    <Container>
      <ScopeMatrixToolbar
        activeTab={activeTab}
        handleTabChange={handleTabChange}
        search={search}
        handleSearch={handleSearch}
        handleFilterChange={handleFilterChange}
        exportData={exportData}
        exportColumns={exportColumns}
        projectName={`${selectedProjectId} ${selectedProjectName}`}
      />
      <Table
        columns={columns}
        rows={rows}
        renderColumn={renderColumn}
        renderRow={renderRow}
        isAllRowExpanded={isAllRowExpanded}
        stickyConfig={{ header: true, column: true }}
        styles={{
          tableWrapper: { height: "85%", padding: '0px 24px', margin: '0px' },
          tableHeaderWrapper: { height: '55px' }
        }}
      />
      <Filter
        isVisible={activeFilter == 'discipline'}
        onClose={handleFilterChange}
        onApply={handleFilterApply}
        onReset={(reset) => console.log("Filter onReset", reset)}
        {...(
          activeTab == 'specifications' ?
            getDivisionFilterProps(
              viewOptions2,
              [viewOptions2[0]],
              filter.view2.data,
              data.divisions,
              data.divisions,
              filter.divisions.data
            )
            :
            getDisciplineFilterProps(
              viewOptions1,
              [viewOptions1[0]],
              filter.view1.data,
              data.disciplines,
              data.disciplines,
              filter.disciplines.data
            ))}
      />
      <Filter
        isVisible={activeFilter == 'scope'}
        onClose={handleFilterChange}
        onApply={handleFilterApply}
        onReset={(reset) => console.log("Filter onReset", reset)}
        {...getScopeFilterProps(
          sortByOptions,
          [sortByOptions[0]],
          filter.sortBy.data,
          data.scopes,
          data.scopes,
          filter.scopes.data
        )}
      />
      <ScopeMatrixPdfViewer
        ref={pdfViewerRef}
        projectId={projectId}
      />
    </Container>
  );
};
