import { IDynamicElement } from "src/view/components";

export const disciplines: any = {
    "A": { isVisible: true, key: "A", value: "Architectural" },
    "C": { isVisible: true, key: "C", value: "Civil" },
    "D": { isVisible: false, key: "D", value: "Architectural" },
    "E": { isVisible: true, key: "E", value: "Electrical" },
    "F": { isVisible: true, key: "F", value: "Fire Protection" },
    "G": { isVisible: true, key: "G", value: "General" },
    "L": { isVisible: true, key: "L", value: "Landscape" },
    "M": { isVisible: true, key: "M", value: "Mechanical" },
    "P": { isVisible: true, key: "P", value: "Plumbing" },
    "S": { isVisible: true, key: "S", value: "Structural" },
    "CS": { isVisible: false, key: "CS", value: "Architectural" },
    "FP": { isVisible: false, key: "FP", value: "Fire Protection" },
    "FS": { isVisible: false, key: "FS", value: "Fire Protection" },
    "ID": { isVisible: true, key: "ID", value: "Interiors" },
    "OTHER": { isVisible: true, key: "OTHER", value: "Other" },
};

export const divisions: any = {
    "01": { isVisible: true, key: "01", value: "01 - General Requirements", },
    "02": { isVisible: true, key: "02", value: "02 - Existing Conditions", },
    "03": { isVisible: true, key: "03", value: "03 - Concrete", },
    "04": { isVisible: true, key: "04", value: "04 - Masonry", },
    "05": { isVisible: true, key: "05", value: "05 - Metals", },
    "06": { isVisible: true, key: "06", value: "06 - Wood, Plastics & Composites", },
    "07": { isVisible: true, key: "07", value: "07 - Thermal and Moisture Protection", },
    "08": { isVisible: true, key: "08", value: "08 - Doors and Windows", },
    "09": { isVisible: true, key: "09", value: "09 - Finishes", },
    "10": { isVisible: true, key: "10", value: "10 - Specialties", },
    "11": { isVisible: true, key: "11", value: "11 - Equipment", },
    "12": { isVisible: true, key: "12", value: "12 - Furnishings", },
    "13": { isVisible: true, key: "13", value: "13 - Special Construction", },
    "14": { isVisible: true, key: "14", value: "14 - Conveying Systems", },
    "21": { isVisible: true, key: "21", value: "21 - Fire Suppression", },
    "22": { isVisible: true, key: "22", value: "22 - Plumbing", },
    "23": { isVisible: true, key: "23", value: "23 - Mechanical", },
    "25": { isVisible: true, key: "25", value: "25 - Integrated Automation", },
    "26": { isVisible: true, key: "26", value: "26 - Electrical", },
    "27": { isVisible: true, key: "27", value: "27 - Communications", },
    "28": { isVisible: true, key: "28", value: "28 - Electronic Safety & Security", },
    "31": { isVisible: true, key: "31", value: "31 - Earthwork", },
    "32": { isVisible: true, key: "32", value: "32 - Exterior Improvements", },
    "33": { isVisible: true, key: "33", value: "33 - Utilities", },
    "34": { isVisible: true, key: "34", value: "34 - Transportation", },
    "35": { isVisible: true, key: "35", value: "35 - Waterway & Marine construction", },
    "40": { isVisible: true, key: "40", value: "40 - Process Interconnections", },
    "41": { isVisible: true, key: "41", value: "41 - Material Processing and Handling Equipment", },
    "42": { isVisible: true, key: "42", value: "42 - Process Heating, Cooling, and Drying Equipment", },
    "43": { isVisible: true, key: "43", value: "43 - Process Gas and Liquid Handling, Purification, and Storage Equipment", },
    "44": { isVisible: true, key: "44", value: "44 - Pollution and Waste Control Equipment", },
    "45": { isVisible: true, key: "45", value: "45 - Industry-Specific Manufacturing Equipment", },
    "46": { isVisible: true, key: "46", value: "46 - Water and Wastewater Equipment", },
    "48": { isVisible: true, key: "48", value: "48 - Electrical Power Generation", },
    "XX": { isVisible: true, key: "XX", value: "XX - Other", },
};

export const viewOptions1 = [
    { key: 'All sheets', value: 'All sheets' },
    { key: 'Sheets with Scope', value: 'Sheets with Scope' },
    { key: 'Sheets without Scope', value: 'Sheets without Scope' },
];

export const viewOptions2 = [
    { key: 'All specs', value: 'All specs' },
    { key: 'Specs with Scope', value: 'Specs with Scope' },
    { key: 'Specs without Scope', value: 'Specs without Scope' },
];

export const sortByOptions = [
    { key: 'Alphabetical (A-Z)', value: 'Alphabetical (A-Z)' },
    { key: 'Division numbers', value: 'Division numbers' },
];

export const getDisciplineFilterProps = (
    viewOptions: any,
    viewDefault: any,
    viewSelected: any,
    disciplineOptions: any,
    disciplineDefault: any,
    disciplineSelected: any,
) => {
    const data: IDynamicElement[] = [
        {
            renderType: 'check-box',
            options: viewOptions,
            defaultValue: viewDefault,
            resetValue: [],
            value: viewSelected,
            multi: false,
            min: 1,
            setKey: "view1",
            returnKey: "view1",
            componentType: "Primary",
            label: 'View',
        },
        {
            renderType: 'spliter',
        },
        {
            renderType: 'selection-box',
            options: disciplineOptions,
            defaultValue: disciplineDefault,
            resetValue: [],
            value: disciplineSelected,
            multi: true,
            reset: true,
            min: 1,
            max: 999,
            setKey: "disciplines",
            returnKey: "disciplines",
            componentType: "Primary",
            inputLabel: 'Search',
            componentProps: {
                optionsStyle: 'height: 380px;'
            },
        },
    ];

    return {
        labels: {
            title: "Discipline filter",
            subTitle: "Apply filters to table data.",
        },
        data: data,
    };
};

export const getDivisionFilterProps = (
    viewOptions: any,
    viewDefault: any,
    viewSelected: any,
    divisionOptions: any,
    divisionDefault: any,
    divisionSelected: any,
) => {
    const data: IDynamicElement[] = [
        {
            renderType: 'check-box',
            options: viewOptions,
            defaultValue: viewDefault,
            resetValue: [],
            value: viewSelected,
            multi: false,
            min: 1,
            setKey: "view2",
            returnKey: "view2",
            componentType: "Primary",
            label: 'View',
        },
        {
            renderType: 'spliter',
        },
        {
            renderType: 'selection-box',
            options: divisionOptions,
            defaultValue: divisionDefault,
            resetValue: [],
            value: divisionSelected,
            multi: true,
            reset: true,
            min: 1,
            max: 999,
            setKey: "divisions",
            returnKey: "divisions",
            componentType: "Primary",
            inputLabel: 'Search',
            componentProps: {
                optionsStyle: 'height: 380px;'
            },
        },
    ];

    return {
        labels: {
            title: "Division filter",
            subTitle: "Apply filters to table data.",
        },
        data: data,
    };
};

export const getScopeFilterProps = (
    sortByOptions: any,
    sortByDefault: any,
    sortBySelected: any,
    scopeOptions: any,
    scopeDefault: any,
    scopeSelected: any,
) => {
    const data: IDynamicElement[] = [
        {
            renderType: 'check-box',
            options: sortByOptions,
            defaultValue: sortByDefault,
            resetValue: [],
            value: sortBySelected,
            multi: false,
            min: 1,
            setKey: "sortBy",
            returnKey: "sortBy",
            componentType: "Primary",
            label: 'Sort by',
        },
        {
            renderType: 'spliter',
        },
        {
            renderType: 'selection-box',
            options: scopeOptions,
            defaultValue: scopeDefault,
            resetValue: [],
            value: scopeSelected,
            multi: true,
            reset: true,
            min: 1,
            max: 999,
            setKey: "scopes",
            returnKey: "scopes",
            componentType: "Primary",
            inputLabel: 'Search',
            componentProps: {
                optionsStyle: 'height: 380px;'
            },
        },
    ];

    return {
        labels: {
            title: "Scope filter",
            subTitle: "Apply filters to table data.",
        },
        data: data,
    };
};