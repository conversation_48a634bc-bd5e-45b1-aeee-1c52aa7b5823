import React, { useState } from 'react';
import { ITableColumnRow } from 'src/view/components/table/Table.type';
import { BtnOpen, WhiteContainer, WhiteContainerIcon, WhiteContainerLabel } from '../ScopeMatrix.style';
import { FaCaretDown, FaCaretRight } from 'react-icons/fa';
import { ToolTip } from 'src/view/components';
import { extarctId, extarctName } from '../ScopeMatrix.utils';
import { IPDFProps } from '../ScopeMatrix.type';

interface ColSheetNameProps extends ITableColumnRow {
    handleOnClickOpen?: (props: Partial<IPDFProps>) => void
};

export const ScopeMatrixColSheetName: React.FC<ColSheetNameProps> = ({
    column,
    rowLevel,
    isRowExpanded,
    row,
    handleOnClickOpen,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const customStyle = rowLevel == 0 ? `cursor: pointer;` : ``;
    const label = extarctName(row[column.key]);
    const drawingId = '';
    const specificationId = '';
    const scopeId = extarctId(row[column.key], { returnWhenNoId: '0' });
    return (
        <WhiteContainer
            onClick={() => handleOnClickOpen?.({ drawingId, specificationId, scopeId })}
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
            customStyle={customStyle}
        >
            <WhiteContainerIcon>
                {isRowExpanded ?
                    <FaCaretDown
                        color={!row.children?.length ? 'transparent' : undefined}
                        size={'20px'}
                        style={{ marginBottom: '4px' }}
                    />
                    :
                    <FaCaretRight
                        color={!row.children?.length ? 'transparent' : undefined}
                        size={'20px'}
                        style={{ marginBottom: '2px' }}
                    />
                }
            </WhiteContainerIcon>
            <WhiteContainerLabel>
                {label}
            </WhiteContainerLabel>
            {rowLevel == 1 && isOpen && (
                <BtnOpen>Open</BtnOpen>
            )}
            <ToolTip
                isVisible={isOpen}
                info={label}
            />
        </WhiteContainer>
    );
};