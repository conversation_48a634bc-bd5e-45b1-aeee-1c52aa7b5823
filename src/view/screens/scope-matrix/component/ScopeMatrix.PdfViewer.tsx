import { useState, useImperativeHandle, forwardRef, } from 'react';
import { Modal } from '../../../components/modal/Modal';
import { usePDFMetaInfo } from '../hook';
import { IPDFProps } from '../ScopeMatrix.type';
import { PDFViewer, usePDFFetch, usePDFSlice } from 'src/view/components';

export interface ScopeMatrixPdfViewerHandles {
    open: (props: Partial<IPDFProps>) => void;
    close: (props: Partial<IPDFProps>) => void;
};

interface ScopeMatrixPdfViewerProps {
    projectId: string;
};

const initialState = ({ projectId }: Partial<IPDFProps>) => ({
    projectId: projectId ?? '',
    drawingId: '',
    specificationId: '',
    scopeId: '',
} as IPDFProps);

export const ScopeMatrixPdfViewer = forwardRef<ScopeMatrixPdfViewerHandles, ScopeMatrixPdfViewerProps>(
    ({ projectId }, ref) => {
        // ## State
        const [state, setState] = useState<IPDFProps>(initialState({ projectId }));
        const [isVisible, setIsVisible] = useState(false);
        // ## Hooks
        // ## PDF MetaInfo
        const pdfMetaInfo = usePDFMetaInfo(state);
        const { drawingSheet, drawingSheetDocument, } = pdfMetaInfo;
        const originalPDF = usePDFFetch(drawingSheetDocument?.presignedUrl);
        const slicedPDF = usePDFSlice(originalPDF, [Number(drawingSheet?.pageNumber) || 0]);

        // ## ForwardRef
        useImperativeHandle(ref, () => ({
            open: (props) => onShow(props),
            close: (props) => onHide(props),
        }));

        // ## Methods
        const onShow = (newState: Partial<IPDFProps>) => {
            setState({ ...state, ...newState });
            setIsVisible(true);
        };

        const onHide = (newState: Partial<IPDFProps>) => {
            setIsVisible(false);
            setState({ ...initialState({ projectId }), ...newState });
        };

        console.log("## PDFViewer >>> Context", { pdfMetaInfo, originalPDF, slicedPDF });
        return (
            <Modal type={'PDFViewer'} isVisible={isVisible} onClose={() => setIsVisible(false)}>
                <PDFViewer
                    isLoading={pdfMetaInfo.isLoading}
                    pdfMeta={{
                        pdfId: drawingSheetDocument?.id ?? 0,
                        pdfName: drawingSheetDocument?.title ?? '0',
                        pdfUrl: slicedPDF,
                    }}
                    isPreviousPageDisabled={false}
                    isNextPageDisabled={false}
                    onToolBarClick={args => {
                        if (args.item.id === 'add') {
                            alert('Add clicked!');
                        }
                    }}
                />
            </Modal>
        );
    }
);
