import React from 'react';
import { ITableColumn } from 'src/view/components/table/Table.type';
import { <PERSON>er<PERSON><PERSON>r, HeaderContainerLabel, HeaderContainerIcon } from '../ScopeMatrix.style';
import { MdOutlineFormatLineSpacing } from 'react-icons/md';
import { RxTextAlignMiddle } from 'react-icons/rx';

interface CellSheetNameProps extends ITableColumn {
    isAllRowExpanded: boolean;
    onClick: () => void;
}

export const ScopeMatrixCellSheetName: React.FC<CellSheetNameProps> = ({
    column,
    isAllRowExpanded,
    onClick
}) => {
    return (
        <HeaderContainer>
            <HeaderContainerLabel>{column.title}</HeaderContainerLabel>
            <HeaderContainerIcon onClick={onClick}>
                {isAllRowExpanded ?
                    <RxTextAlignMiddle size={'20px'} />
                    :
                    <MdOutlineFormatLineSpacing size={'20px'} />
                }
            </HeaderContainerIcon>
        </HeaderContainer>
    );
};