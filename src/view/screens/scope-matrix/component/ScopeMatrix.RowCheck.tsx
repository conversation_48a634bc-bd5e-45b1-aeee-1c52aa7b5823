import React from 'react';
import { ITableColumnRow } from 'src/view/components/table/Table.type';
import { IconBorder, WhiteContainer, } from '../ScopeMatrix.style';
import { FaCheck } from 'react-icons/fa';
import { extarctId } from '../ScopeMatrix.utils';
import { IPDFProps } from '../ScopeMatrix.type';

interface RowCheckProps extends ITableColumnRow {
    handleOnClickOpen?: (props: Partial<IPDFProps>) => void;
};

export const ScopeMatrixRowCheck: React.FC<RowCheckProps> = ({
    column,
    row,
    handleOnClickOpen,
}) => {
    const drawingId = extarctId(row.sheetName, { splitKey: ' - ' });
    const specificationId = extarctId(row.sheetName, { splitKey: ' || ' });
    const scopeId = extarctId(column.key);
    return (
        <WhiteContainer
            onClick={() => handleOnClickOpen?.({ drawingId, specificationId, scopeId })}
        >
            <IconBorder>
                <FaCheck color={'#084967'} size={'12px'} />
            </IconBorder>
        </WhiteContainer>
    );
};