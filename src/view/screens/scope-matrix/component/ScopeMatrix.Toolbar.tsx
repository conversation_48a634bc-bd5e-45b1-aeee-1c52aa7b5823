import React from 'react';
import { BsReverseLayoutTextSidebarReverse } from 'react-icons/bs';
import { FaChevronDown, FaRegFileLines } from 'react-icons/fa6';
import { FiTarget } from 'react-icons/fi';
import { Button, TextInput } from 'src/view/components';
import { Tool<PERSON>ontainer, TabContainer, ToolFeatureContainer, FilterKey, FilterValue } from '../ScopeMatrix.style';
import { themeTokens } from 'src/theme/tokens';
import { FilterType, TabType } from '../ScopeMatrix.type';
import { HiOutlinePencil } from 'react-icons/hi';
import { IoSearch } from 'react-icons/io5';
import ExportFile from 'src/modules/common/exportFile';

interface ScopeMatrixToolbarProps {
  activeTab: TabType;
  handleTabChange: (tab: TabType) => void;
  search: string;
  handleSearch: (text: string) => void;
  handleFilterChange: (text: FilterType) => void;
  exportData?: any[];
  exportColumns?: { label: string; key: string }[];
  projectName?: string;
};

const ScopeMatrixToolbar: React.FC<ScopeMatrixToolbarProps> = ({
  activeTab,
  handleTabChange,
  search,
  handleSearch,
  handleFilterChange,
  exportData,
  exportColumns,
  projectName,
}) => (
  <ToolContainer>
    <TabContainer>
      <Button
        type={'Secondary'}
        text="Drawings"
        leftIcon={HiOutlinePencil}
        onClick={() => handleTabChange('drawings')}
        isDisabled={false}
        isActive={activeTab === 'drawings'}
      />
      <Button
        type={'Secondary'}
        text="Specifications"
        leftIcon={FaRegFileLines}
        onClick={() => handleTabChange('specifications')}
        isDisabled={false}
        isActive={activeTab === 'specifications'}
      />
    </TabContainer>
    <ToolFeatureContainer>
      <TextInput
        type={'Search'}
        placeholder='Search'
        value={search}
        onChangeValue={handleSearch}
        leftIcon={IoSearch}
        customStyle='width: 296px; '
      />
      <Button
        type={'Primary'}
        text={
          <FilterKey>
            {activeTab == 'drawings' ? "Discipline" : "Division"}: <FilterValue>All</FilterValue>
          </FilterKey>
        }
        leftIcon={() =>
          <BsReverseLayoutTextSidebarReverse
            size={'18px'}
            color={themeTokens.Gray_500}
          />
        }
        rightIcon={() =>
          <FaChevronDown
            size={'20px'}
            color={themeTokens.Gray_400}
          />
        }
        onClick={() => handleFilterChange('discipline')}
        isDisabled={false}
        isActive={false}
        isLoading={false}
      />
      <Button
        type={'Primary'}
        text={<FilterKey>Scope: <FilterValue>All</FilterValue></FilterKey>}
        leftIcon={() =>
          <FiTarget
            size={'18px'}
            color={themeTokens.Gray_500}
          />
        }
        rightIcon={() =>
          <FaChevronDown
            size={'20px'}
            color={themeTokens.Gray_400}
          />
        }
        onClick={() => handleFilterChange('scope')}
        isDisabled={false}
        isActive={false}
        isLoading={false}
      />
      {exportData && exportColumns && (
        <ExportFile
          columns={exportColumns}
          data={exportData}
          filename={`scope-matrix-${activeTab}`}
          projectName={projectName}
          visibleItem={{ 'xlsx': 1 }}
        />
      )}
    </ToolFeatureContainer>
  </ToolContainer>
);

export default ScopeMatrixToolbar;