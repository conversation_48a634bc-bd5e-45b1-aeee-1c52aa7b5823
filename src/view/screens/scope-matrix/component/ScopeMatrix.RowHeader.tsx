import React, { useRef, useState } from 'react';
import { ITableColumn, ToolTipAnchor } from 'src/view/components';
import { HeaderContainerDefault, HeaderContainerLabelDefault } from '../ScopeMatrix.style';
import { extarctName } from '../ScopeMatrix.utils';

interface RowHeaderProps extends ITableColumn { };

export const ScopeMatrixRowHeader: React.FC<RowHeaderProps> = ({
    column,
}) => {
    const cellRef = useRef<HTMLDivElement>(null);
    const [isToolTipVisible, setIsToolTipVisible] = useState(false);
    const label = extarctName(column.title);
    return (
        <HeaderContainerDefault
            ref={cellRef}
            onMouseEnter={() => setIsToolTipVisible(true)}
            onMouseLeave={() => setIsToolTipVisible(false)}
        >
            <HeaderContainerLabelDefault>
                {'10 00 01'}
            </HeaderContainerLabelDefault>
            <HeaderContainerLabelDefault>
                {label}
            </HeaderContainerLabelDefault>
            {isToolTipVisible &&
                <ToolTipAnchor
                    isVisible={isToolTipVisible}
                    info={label}
                    anchorEl={cellRef.current}
                />
            }
        </HeaderContainerDefault>
    );
};