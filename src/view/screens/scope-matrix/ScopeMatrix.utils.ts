import { disciplines, divisions } from './ScopeMatrix.data';
import { IFilter, ResponseData } from './ScopeMatrix.type';

// ## Entity
export const disciplineColumn = {
    key: "sheetName",
    title: "Discipline",
    uiConfig: {
        width: '326px',
        renderDynamicColumn: true,
        renderDynamicRow: true,
        showToolTip: false,
    },
};

export const specColumn = {
    key: "sheetName",
    title: "Discipline",
    uiConfig: {
        width: '326px',
        renderDynamicColumn: true,
        renderDynamicRow: true,
        showToolTip: false,
    },
};

const otherColumn = (key: string, title: string) => ({
    key, title,
    uiConfig: {
        width: '155px',
        renderDynamicColumn: true,
        renderDynamicRow: true,
        showToolTip: true,
    },
});

export const transformApiResponse = (apiResponse: any): ResponseData => {
    // ## API Response [Init]
    const response: ResponseData = {
        isLoading: true,
        disciplines: [],
        divisions: [],
        scopes: [],
        scopeNames: [],
        sheetNames: [],
        specNames: {},
        // ## Table data
        sheetColumns: [disciplineColumn],
        scopeSheets: {},
        specColumns: [specColumn],
        scopeSpecs: {},
    };
    if (!apiResponse)
        return response;

    processDrawings(apiResponse, response);

    // ## API Response [Return]
    return {
        ...response,
        isLoading: false,
        disciplines: response.disciplines.sort((a, b) => a.value.localeCompare(b.value)),
        divisions: response.divisions.sort((a, b) => a.value.localeCompare(b.value)),
        scopes: response.scopeNames.map((e) => ({ key: e, value: extarctName(e) })),
        scopeSheets: Object.values(response.scopeSheets),
        scopeSpecs: Object.values(response.scopeSpecs).map((e: any) => ({
            ...e, children: Object.values(e.children),
        })),
    };
};

// ## Sheet Vertical <---> Scope Horizontal
const processDrawings = (apiResponse: any, response: ResponseData) => {
    const sheetNames = Object.keys(apiResponse);
    response.sheetNames = sheetNames;                                       // ## Sheet Entity
    sheetNames.reduce((a, sheetName, sheetIndex) => {
        // ## Create Discipline Level and Sheet Level with `SheetName`
        const { isVisible, key, value: disciplineName } = getDisciplineBySheetName(sheetName);
        if (!a.scopeSheets[disciplineName]) {
            if (isVisible) {
                a.disciplines.push(disciplines[key]);
            };
            a.scopeSheets[disciplineName] = { 'sheetName': disciplineName, 'children': [] };
        };
        const scopeSheet: any = { 'sheetName': sheetName };

        // ## Process `Scope` by `sheetName`
        const scopeNames = Object.keys(apiResponse[sheetName]);
        scopeNames.map((scopeName) => {
            if (!a.scopeNames.includes(scopeName)) {
                a.scopeNames.push(scopeName);                               // ## Scope Entity
                a.sheetColumns.push(otherColumn(scopeName, scopeName));     // ## Scope Columns Entity
                a.specColumns.push(otherColumn(scopeName, scopeName));      // ## Scope Columns Entity
            };
            // ## Check scope existance
            const scope = apiResponse?.[sheetName]?.[scopeName];
            const isSheetContainsScope = scope == 0 ? 0 : 1;
            // ## Create Scope for both `Discipline Level` and `Sheet Level`
            scopeSheet[scopeName] = isSheetContainsScope;
            if (a.scopeSheets[disciplineName][scopeName] != 1) {
                a.scopeSheets[disciplineName][scopeName] = isSheetContainsScope;
                if (isSheetContainsScope && !a.scopeSheets[disciplineName]['atleastOne'])
                    a.scopeSheets[disciplineName]['atleastOne'] = isSheetContainsScope;
            };

            if (isSheetContainsScope) {
                const specNames = Object.keys(scope.specifications);
                specNames.forEach((specName) => {
                    const { isVisible, key, value: disciplineName } = getDivisionBySpec(specName);
                    if (!a.scopeSpecs[disciplineName]) {
                        a.scopeSpecs[disciplineName] = { 'sheetName': disciplineName, 'children': [] };
                        if (isVisible) {
                            a.divisions.push(divisions[key]);
                        };
                    };

                    if (!a.scopeSpecs[disciplineName])
                        a.scopeSpecs[disciplineName] = { 'sheetName': disciplineName, 'children': {}, };
                    a.scopeSpecs[disciplineName][scopeName] = 1;
                    a.scopeSpecs[disciplineName]['atleastOne'] = 1;

                    if (!a.scopeSpecs[disciplineName]["children"][specName])
                        a.scopeSpecs[disciplineName]["children"][specName] = { 'sheetName': specName };
                    a.scopeSpecs[disciplineName]["children"][specName][scopeName] = 1;
                });
            };
        });

        a.scopeSheets[disciplineName].children.push(scopeSheet);
        return a;
    }, response);
};

const getDisciplineBySheetName = (sheetName: string) => {
    const sheetNumber = extarctId(sheetName, { splitKey: " - ", postion: 1 });
    const key = sheetNumber?.charAt(0);
    return disciplines[key] ?? disciplines["OTHER"];
};

const getDivisionBySpec = (rawSpecName: string) => {
    const specName = rawSpecName.split(' || ');
    const key = specName[1]?.slice(0, 2);
    return divisions[key] ?? divisions["OTHER"];
};

export const filterByScope = (columns: any[], filter: IFilter) => {
    return columns.filter((column) => {
        return column.key == "sheetName" || filter.scopes.indexes.key[column.key];
    });
};

export const deepFilterBySheetName = (
    rows: any[],
    search: string,
    filter?: IFilter,
    type?: 'discipline' | 'division',
) => {
    const searchLower = search.toLowerCase();
    return rows.map((row: any) => {
        // ## Filter
        if (filter) {
            if (type == 'discipline' && !filter.disciplines.indexes.value[row.sheetName])
                return null;

            if (type == 'division' && !filter.divisions.indexes.value[row.sheetName])
                return null;

            if (type == 'discipline') {
                const viewSelected = filter.view1.data[0].value
                if (viewSelected != 'All sheets') {
                    const isSheetsWithScope = viewSelected == "Sheets with Scope" && !row['atleastOne'];
                    const isSheetsWithoutScope = viewSelected == "Sheets without Scope" && row['atleastOne'];
                    if (isSheetsWithScope || isSheetsWithoutScope)
                        return null;
                };
            };

            if (type == 'division') {
                const viewSelected = filter.view2.data[0].value
                if (viewSelected != 'All specs') {
                    const isSheetsWithScope = viewSelected == "Specs with Scope" && !row['atleastOne'];
                    const isSheetsWithoutScope = viewSelected == "Specs without Scope" && row['atleastOne'];
                    if (isSheetsWithScope || isSheetsWithoutScope)
                        return null;
                };
            }

        };

        // ## Search
        const isParentMatch = row.sheetName.toLowerCase().includes(searchLower);
        const children: any = deepFilterBySheetName(row.children || [], search);
        if (!isParentMatch && children.length == 0)
            return null;

        return {
            ...row,
            children: children, // ## isParentMatch? row.children : children;
        };
    }).filter(Boolean);
};

export const sortBy = (a: any, b: any, key: string, mode?: 'asce' | 'desc') => {
    if (mode == 'asce')
        return a?.[key].localeCompare(b?.[key]);
    else if (mode == 'desc')
        return b?.[key].localeCompare(a?.[key]);
    else
        return 0;
};

export const sortByScopeName = (columns: any[], filter: IFilter) => {
    const sortKey = filter.sortBy.data[0].value == "Alphabetical (A-Z)" ?
        'asce' : 'desc';
    const firstItem = columns.find(column => column.key === "sheetName");
    const remainingItem = columns.filter(column => column.key !== "sheetName")
        .sort((a, b) => sortBy(a, b, 'title', sortKey));
    return [firstItem, ...remainingItem];
};

export const processSelection = (selected: any) => {
    const keys = Object.keys(selected);
    const selectedObject: any = {};
    keys.map((key) =>
        selectedObject[key] = dsArray(selected[key])
    );
    return selectedObject;
};

export const dsArray = (data: any[]) => {
    const intialIndexes = { key: {}, value: {} };
    return {
        data: data,
        indexes: data.reduce((a: any, e: any) => {
            a.key[e.key] = 1;
            a.value[e.value] = 1;
            return a;
        }, intialIndexes),
    };
};

// Export utility functions
export const transformDataForExport = (rows: any[], columns: any[], activeTab: string) => {
    const exportData: any[] = [];
    // Helper function to flatten nested rows
    const flattenRows = (rowList: any[], level: number = 0) => {
        rowList.forEach((row) => {
            const rowData: any = {};
            // Add sheet name with indentation for hierarchy
            const indent = '  '.repeat(level);
            rowData['Sheet Name'] = indent + row.sheetName;
            // Add scope columns
            columns.forEach((col) => {
                if (col.key !== 'sheetName') {
                    const value = row[col.key];
                    if (value === 1) {
                        rowData[col.title] = '✓';
                    } else if (value === 0) {
                        rowData[col.title] = '';
                    } else {
                        rowData[col.title] = value || '';
                    }
                }
            });
            exportData.push(rowData);
            // Recursively process children
            if (row.children && row.children.length > 0) {
                flattenRows(row.children, level + 1);
            }
        });
    };
    flattenRows(rows);
    return exportData;
};

export const getExportColumns = (columns: any[]) => {
    return columns.map((col) => ({
        label: col.title,
        key: col.key === 'sheetName' ? 'Sheet Name' : col.title
    }));
};

export const extarctName = (label: string) => {
    return label.split(' || ')?.[1] ?? label;
};

interface IExtractOptions {
    splitKey?: string;
    returnWhenNoId?: string;
    postion?: number;
};

export const extarctId = (
    rawLabel: string,
    options: IExtractOptions = {},
) => {
    const {
        splitKey = ' || ',
        returnWhenNoId = '',
        postion = 0
    } = options;
    const label = rawLabel.split(splitKey);
    if (label.length == 1)
        return returnWhenNoId;
    return label[postion];
};