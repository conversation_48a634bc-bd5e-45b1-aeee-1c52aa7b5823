import React, { useState } from 'react';
import { IconType } from 'react-icons';
import { IoCheckbox, IoSearch } from 'react-icons/io5';
import { MdCheckBoxOutlineBlank } from 'react-icons/md';
import CustomIcon from 'src/modules/common/customIcon';
import { TextInput } from '../text-input/TextInput';
import {
    IconWrapper,
    Label,
    LabelWrapper,
    OptionConatiner,
    OptionsConatiner,
    SelectionContainers,
    TextInputContainer,
} from './Selection.style';
import { RiResetLeftFill } from 'react-icons/ri';
import { themeTokens } from 'src/theme/tokens';
import { Button } from '../button/Button';

interface IOption {
    key: string;
    value: string;
};

export interface SelectionProps {
    type: "Primary";
    label?: string;
    inputLabel?: string;
    options: IOption[];
    values: IOption[];
    handleReset?: () => void;
    handleChange: (value: IOption) => void;
    // isDisabled?: boolean;
    // isActive?: boolean;

    activeIcon?: IconType;
    inActiveIcon?: IconType;
    style?: string;
    optionsStyle?: string;
};

interface IRenderOption {
    item: IOption;
    index: number;
};

export const Selection: React.FC<SelectionProps> = ({
    type,
    label = '',
    inputLabel = '',
    options = [],
    values = [],
    handleReset,
    handleChange,
    // isDisabled = false,
    // isActive = true,
    activeIcon = IoCheckbox,
    inActiveIcon = MdCheckBoxOutlineBlank,
    style = '',
    optionsStyle = '',
}) => {
    const SelectionContainer = SelectionContainers[type];
    if (!Selection)
        return null;

    // ## State
    const [search, setSearch] = useState('');

    // ## Functional Methods
    const handleSearch = (search: string) => {
        setSearch(search)
    };

    // ## Render Methods
    const RenderOption = ({ item, index }: IRenderOption) => {
        const value1 = item.value.toLocaleLowerCase();
        const value2 = search.toLocaleLowerCase();
        if (search && !value1.includes(value2))
            return null;

        const isActive = !!values.find((value) => value.key == item.key);
        return (
            <OptionConatiner key={`selection-option-${index}`} onClick={() => handleChange(item)}>
                <IconWrapper isActive={isActive}>
                    <CustomIcon Icon={isActive ? activeIcon : inActiveIcon} />
                </IconWrapper>
                <LabelWrapper>{item.value}</LabelWrapper>
            </OptionConatiner>
        );
    };

    return (
        <SelectionContainer customStyle={style}>
            {label &&
                <Label>{label}</Label>
            }
            <TextInputContainer>
                {inputLabel &&
                    <TextInput
                        type={'Search'}
                        placeholder={inputLabel}
                        value={search}
                        onChangeValue={handleSearch}
                        leftIcon={IoSearch}
                        customStyle='width: 340px'
                    />
                }
                {/* {handleReset &&
                    <IconWrapper onClick={handleReset}>
                        <CustomIcon Icon={RiResetLeftFill} color={themeTokens.Gray_800} />
                    </IconWrapper>
                } */}
            </TextInputContainer>
            {/* {handleReset &&
                <Button
                    type={'Primary'}
                    text={"Reset Values"}
                    onClick={handleReset}
                    isDisabled={false}
                    isActive={false}
                    isLoading={false}
                    customStyle={"height: 36px; width: 340px;"}
                />
            } */}
            <OptionsConatiner customStyle={optionsStyle}>
                {options.map((option: IOption, index: number) => {
                    return (
                        <RenderOption
                            item={option}
                            index={index}
                        />
                    );
                })}
            </OptionsConatiner>
        </SelectionContainer>
    );
};