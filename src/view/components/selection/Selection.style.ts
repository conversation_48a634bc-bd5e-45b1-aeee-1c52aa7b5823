import { themeTokens } from "src/theme/tokens";
import styled from "styled-components";
import { StyledScroll } from "../utils";

interface SelectionContainerProps {
  isActive?: boolean;
  customStyle?: string;
};

export const SelectionContainer = styled.div<SelectionContainerProps>`
  /* Div */
  height: max-content;
  width: max-content;
  background-color: #FFFFFF;
  
  /* Inner Div */
  display: flex;
  flex-direction: column;
  gap: 9px;

  /* Conditional */
  ${({ customStyle }) => customStyle && customStyle};
`;

export const TextInputContainer = styled.div<SelectionContainerProps>`
  height: max-content;
  width: max-content;
  
  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 9px;
`;

export const Label = styled.span<SelectionContainerProps>`
  /* Div */
  height: max-content;
  width: max-content;

  /* Font */
  font-size: 14px;
  font-weight: 500;
  color: #4D5761;

  /* Conditional */
  ${({ customStyle }) => customStyle && customStyle};
`;


interface OptionsConatinerProps {
  customStyle?: string;
};

export const OptionsConatiner = styled.div<OptionsConatinerProps>`
  /* Div */
  height: max-content;
  width: 100%;
  background-color: transparent;
  
  /* Inner Div */
  display: flex;
  flex-direction: column;
  overflow: scroll;

  /* Conditional */
  ${StyledScroll()}
  ${({ customStyle }) => customStyle && customStyle};
`;

interface OptionConatinerProps {
  customStyle?: string;
};

export const OptionConatiner = styled.div<OptionConatinerProps>`
  /* Div */
  height: 36px;
  width: 100%;
  background-color: transparent;
  padding: 9px 0px;
  
  /* Inner Div */
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;

  /* Other */
  transition: background 0.2s ease;
  cursor: pointer;

  /* Conditional */
  ${({ customStyle }) => customStyle && customStyle};
`;

export const IconWrapper = styled.div<{ isActive?: boolean }>`
  display: flex;
  color: ${({ isActive }) => isActive ? themeTokens.primaryColor : themeTokens.Gray_300};
`;

export const LabelWrapper = styled.div`
  display: flex;
  color: ${themeTokens.Gray_800};
`;

/*
 * SelectionContainer contains all Variants
 */
export const SelectionContainers = {
  Primary: SelectionContainer,
};
