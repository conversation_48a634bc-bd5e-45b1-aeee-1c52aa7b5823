export interface StickyProps {
    sticky?: boolean;
    zIndex?: string;
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
    width?: string;
};

export const StyledSticky = (props: StickyProps) => `
    position: sticky; 
    top: ${props.top || undefined};
    bottom: ${props.bottom || undefined};
    left: ${props.left || undefined};
    right: ${props.right || undefined};
    z-index: ${props.zIndex || undefined};
`;