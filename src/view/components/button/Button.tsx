import React from 'react';
import { IconType } from 'react-icons';
import { BtnWrapper, IconWrapper } from './Button.style';
import CustomIcon from 'src/modules/common/customIcon';

interface ButtonProps {
    type: "Primary" | "Secondary";
    text?: string | React.ReactNode;
    leftIcon?: IconType;
    rightIcon?: IconType;
    onClick?: () => void;
    isDisabled?: boolean;
    isLoading?: boolean;
    isActive?: boolean;
    customStyle?: string;
    leftIconStyle?: string;
    rightIconStyle?: string;
}

export const Button: React.FC<ButtonProps> = ({
    type,
    text,
    leftIcon: LeftIcon,
    rightIcon: RightIcon,
    onClick,
    isDisabled = false,
    isLoading = false,
    isActive = true,
    customStyle = '',
    leftIconStyle = '',
    rightIconStyle = '',
}) => {
    const Button = BtnWrapper[type];
    if (!Button)
        return null;

    const renderLoader = () => {
        return (
            <span className="activity-indicator" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: 20, height: 20 }}>
                <svg width="20" height="20" viewBox="0 0 50 50">
                    <circle
                        cx="25"
                        cy="25"
                        r="20"
                        fill="none"
                        stroke="#FFFFFF"
                        strokeWidth="5"
                        strokeDasharray="31.4 31.4"
                        strokeLinecap="round"
                    >
                        <animateTransform
                            attributeName="transform"
                            type="rotate"
                            from="0 25 25"
                            to="360 25 25"
                            dur="1s"
                            repeatCount="indefinite"
                        />
                    </circle>
                </svg>
            </span>
        );
    };

    const renderContent = () => {
        return (
            <>
                {LeftIcon && (
                    <IconWrapper customStyle={leftIconStyle}>
                        <CustomIcon Icon={LeftIcon} />
                    </IconWrapper>
                )}
                {text && <span>{text}</span>}
                {RightIcon && (
                    <IconWrapper customStyle={rightIconStyle}>
                        <CustomIcon Icon={RightIcon} />
                    </IconWrapper>
                )}
            </>
        );
    };

    return (
        <Button
            onClick={onClick}
            isActive={isActive}
            disabled={isDisabled || isLoading}
            customStyle={customStyle}
        >
            {isLoading ? renderLoader() : renderContent()}
        </Button>
    );
};