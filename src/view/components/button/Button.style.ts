import styled from "styled-components";

/*
 * ## Primary Variant
 */
interface BtnWrapperProps {
  isActive?: boolean;
  customStyle?: string;
};

interface BtnPrimaryWrapperProps extends BtnWrapperProps { };

export const BtnPrimaryWrapperActive = `
  background-color: #FF6A34;
  color: #FFFFFF;
  border-color: #FF6A34;
`

export const BtnPrimaryWrapper = styled.button<BtnPrimaryWrapperProps>`
  /* Div */
  height: 36px;
  width: max-content;
  padding: 0 10px;
  background-color: #FFFFFF;
  border-width: 1px;
  border-color: #D2D6DB;
  border-style: solid;
  border-radius: 10px;

  /* Inner Div */
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  gap: 8px; /* Note: No fixed width then 'gap' works instead of 'justify-content' */

  /* Font */
  font-size: 16px;
  color: #6C737F;

  /* Other */
  transition: background 0.2s ease;
  cursor: pointer;

  /* Conditional */
  ${({ isActive }) => isActive && BtnPrimaryWrapperActive};
  ${({ customStyle }) => customStyle && customStyle};
`;

/*
 * Secondary Variant
 */
interface BtnSecondaryWrapperProps extends BtnWrapperProps { };

export const BtnSecondaryWrapperActive = `
  background-color: #FFF9F8;
  color: #FF6A34;
  border-color: #FFE6E0;
`

export const BtnSecondaryWrapper = styled.button<BtnSecondaryWrapperProps>`
  /* Div */
  height: 36px;
  width: max-content;
  padding: 0 10px;
  background-color: #FFFFFF;
  border-width: 1px;
  border-color: #D2D6DB;
  border-style: solid;
  border-radius: 999px;

  /* Inner Div */
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  gap: 8px; /* Note: No fixed width then 'gap' works instead of 'justify-content' */

  /* Font */
  font-size: 16px;
  color: #6C737F;

  /* Other */
  transition: background 0.2s ease;
  cursor: pointer;

  /* Conditional */
  ${({ isActive }) => isActive && BtnSecondaryWrapperActive};
  ${({ customStyle }) => customStyle && customStyle};
`;

/*
 * BtnWrapper contains all Variants
 */
export const BtnWrapper = {
  Primary: BtnPrimaryWrapper,
  Secondary: BtnSecondaryWrapper,
};

export const IconWrapper = styled.span<{ customStyle?: string }>`
  display: flex;
  ${({ customStyle }) => customStyle && customStyle};
`;