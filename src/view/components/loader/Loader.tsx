import React from 'react';
import { LoaderContainer, LogoContainer, LoadingText, PulseRing } from './Loader.style';
import WyreLogo from '../../../assets/images/wyreAIIcon.png';

interface LoaderProps {
    text?: string;
    size?: 'small' | 'medium' | 'large';
    variant?: 'default' | 'overlay' | 'overlay-absolute' | 'inline';
}

const Loader: React.FC<LoaderProps> = ({
    text = 'Loading...',
    size = 'medium',
    variant = 'default'
}) => {
    return (
        <LoaderContainer variant={variant}>
            <LogoContainer size={size}>
                <PulseRing size={size} />
                <img src={WyreLogo} alt="Wyre AI Logo" />
            </LogoContainer>
            <LoadingText size={size}>
                {text}
            </LoadingText>
        </LoaderContainer>
    );
};

export default Loader; 