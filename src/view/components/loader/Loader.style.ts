import styled, { keyframes } from 'styled-components';
import { themeTokens } from 'src/theme/tokens';

// Animations
const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const fadeIn = keyframes`
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

const bounce = keyframes`
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
`;

interface LoaderContainerProps {
  variant: 'default' | 'overlay' | 'overlay-absolute' | 'inline';
}

interface SizeProps {
  size: 'small' | 'medium' | 'large';
}

export const LoaderContainer = styled.div<LoaderContainerProps>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  animation: ${fadeIn} 0.6s ease-out;

  ${({ variant }) => {
    switch (variant) {
      case 'overlay':
      case 'overlay-absolute':
        const position = variant == 'overlay' ? 'fixed' : 'absolute';
        return `
          position: ${position};
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(8px);
          z-index: 9999;
        `;
      case 'inline':
        return `
          padding: 20px;
        `;
      default:
        return `
          height: 100vh;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        `;
    }
  }}
`;

export const LogoContainer = styled.div<SizeProps>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: ${bounce} 2s infinite;
  z-index: 2;

  img {
    ${({ size }) => {
      switch (size) {
        case 'small':
          return 'width: 60px; height: 60px;';
        case 'large':
          return 'width: 120px; height: 120px;';
        default:
          return 'width: 80px; height: 80px;';
      }
    }}
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    z-index: 3;
  }
`;

export const PulseRing = styled.div<SizeProps>`
  position: absolute;
  border: 2px solid ${themeTokens.primaryColor};
  border-radius: 50%;
  animation: ${pulse} 2s infinite;
  background: ${themeTokens.primaryDark};
  z-index: 1;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return `
          width: 80px;
          height: 80px;
        `;
      case 'large':
        return `
          width: 160px;
          height: 160px;
        `;
      default:
        return `
          width: 120px;
          height: 120px;
        `;
    }
  }}
`;

export const SpinnerContainer = styled.div<SizeProps>`
  display: flex;
  align-items: center;
  justify-content: center;

  .spinner-ring {
    ${({ size }) => {
      switch (size) {
        case 'small':
          return 'width: 40px; height: 40px;';
        case 'large':
          return 'width: 80px; height: 80px;';
        default:
          return 'width: 60px; height: 60px;';
      }
    }}
    position: relative;
    
    div {
      box-sizing: border-box;
      display: block;
      position: absolute;
      border: 3px solid ${themeTokens.primaryColor};
      border-radius: 50%;
      animation: ${spin} 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
      border-color: ${themeTokens.primaryColor} transparent transparent transparent;
      
      &:nth-child(1) {
        animation-delay: -0.45s;
      }
      &:nth-child(2) {
        animation-delay: -0.3s;
      }
      &:nth-child(3) {
        animation-delay: -0.15s;
      }
    }
  }
`;

export const LoadingText = styled.div<SizeProps>`
  font-family: 'Inter', sans-serif;
  font-weight: bold;
  color: ${themeTokens.Gray_700};
  text-align: center;
  letter-spacing: 0.5px;
  margin-top: 25px;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return 'font-size: 14px;';
      case 'large':
        return 'font-size: 20px;';
      default:
        return 'font-size: 16px;';
    }
  }}
  
  &::after {
    content: '';
    animation: ${pulse} 1.5s infinite;
  }
`; 