import styled from "styled-components";
import { StyledScroll } from "../utils";

export const FilterHeaderContainer = styled.div`
  // ## Div
  width: 400px;
  height: 98px;
  padding: 24px;
  
  // ## Inner Div
  display: flex;
  flex-direction: row;
  gap: 9px;
`;

export const FilterTitleContainer = styled.div`
  // ## Div
  flex: 1;
  
  // ## Inner Div
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

export const Title = styled.span`
  color: #111927;
  font-size: 18px;
  font-weight: 600;
`;

export const SubTitle = styled.span`
  color: #4D5761;
  font-size: 14px;
  font-weight: 400;
`;

export const FilterActionContainer = styled.div`
  // ## Div
  width: 50px;
  
  // ## Inner Div
  display: flex;
  justify-content: flex-end;

  // ## Other
  cursor: pointer;
`;

export const FilterContentContainer = styled.div`
  flex-grow: 1;
  padding: 0px 24px;
  overflow: scroll;
  ${StyledScroll()}
`;

export const FilterContentSpliter = styled.div`
  width: 100%;
  border-top: 1px solid #E5E7EB;
  margin: 18px 0px 24px 0px;
`;

export const FilterFooterContainer = styled.div`
  display: flex;
  height: 72px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-top: 1px solid #E5E7EB;
`;
