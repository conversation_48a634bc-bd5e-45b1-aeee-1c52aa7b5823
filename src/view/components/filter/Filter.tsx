import React, { useEffect, useState, } from 'react';
import CustomIcon from 'src/modules/common/customIcon';
import { themeTokens } from 'src/theme/tokens';
import { Button, Modal, } from '../';
import {
    FilterActionContainer,
    FilterContentContainer,
    FilterFooterContainer,
    FilterHeaderContainer,
    FilterTitleContainer,
    SubTitle,
    Title,
} from './Filter.style';
import { IoClose } from 'react-icons/io5';
import { FilterProps, IDynamicElement } from './Filter.type';
import { doPushOrPop, doPushOrPopAtIndex, extractValueFromSrc } from './Filter.utils';
import { RenderDynamicElement } from './Filter.dynamic';

export const Filter: React.FC<FilterProps> = ({
    isVisible,
    onClose,
    labels: { title, subTitle, },
    data,
    onApply,
    onReset,
}) => {
    // ## State
    const [state, setState] = useState<{ [key: string]: any }>({});

    // ## Lifecycle Methods
    useEffect(() => {
        if (isVisible)
            setInitialValues();
    }, [isVisible]);

    // ## Methods
    const setInitialValues = () => {
        const newValue = extractValueFromSrc({ data, setKeyRef: 'setKey', getKey: 'value' });
        setState(newValue);
    };

    const resetValues = () => {
        const newValue = extractValueFromSrc({ data, setKeyRef: 'setKey', getKey: 'defaultValue' });
        setState(newValue);
    };

    const handleArrayChange = (item: IDynamicElement, value: any) => {
        const setKey = item.setKey;
        if (!setKey)
            return;
        setState((state) => {
            const newState = { ...state };
            if (item.multi) {
                const newValues = doPushOrPop(state[setKey], value, item.min, item.max);
                newState[setKey] = newValues;
            } else {
                const newValues = doPushOrPopAtIndex(state[setKey], value, undefined, item.min);
                newState[setKey] = newValues;
            };
            return newState;
        });
    };

    const handleReset = (item: IDynamicElement) => {
        const setKey = item.setKey;
        if (!setKey)
            return;
        setState((state) => {
            const newState = { ...state };
            newState[setKey] = [];
            return newState;
        });
    };

    const apply = () => {
        onApply(state);
        onClose();
    };

    const reset = () => {
        resetValues();
        onReset();
    };

    // ## Render Component
    return (
        <Modal
            isVisible={isVisible}
            onClose={onClose}
            type={'Filter'}
        >
            <FilterHeaderContainer>
                <FilterTitleContainer>
                    <Title>{title}</Title>
                    <SubTitle>{subTitle}</SubTitle>
                </FilterTitleContainer>
                <FilterActionContainer onClick={onClose}>
                    <CustomIcon Icon={IoClose} color={themeTokens.Gray_400} />
                </FilterActionContainer>
            </FilterHeaderContainer>
            <FilterContentContainer>
                {data.map((item, index) => (
                    <RenderDynamicElement
                        key={`dynamic-${index}`}
                        item={item}
                        value={item.setKey ? state[item.setKey] : undefined}
                        handleArrayChange={handleArrayChange}
                        handleReset={handleReset}
                    />
                ))}
            </FilterContentContainer>
            <FilterFooterContainer>
                <Button
                    type={'Primary'}
                    text="Reset"
                    onClick={reset}
                    isDisabled={false}
                    isActive={false}
                    isLoading={false}
                    customStyle={'width: 71px'}
                />
                <Button
                    type={'Primary'}
                    text="Apply"
                    onClick={apply}
                    isDisabled={false}
                    isActive={true}
                    isLoading={false}
                    customStyle={'width: 269px'}
                />
            </FilterFooterContainer>
        </Modal>
    );
};
