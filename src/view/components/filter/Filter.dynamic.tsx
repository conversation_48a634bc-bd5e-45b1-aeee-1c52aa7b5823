import { memo } from "react";
import { FilterContentSpliter } from "./Filter.style";
import { IDynamicElement } from "./Filter.type";
import { Selection, SelectionProps } from "../selection/Selection";
import { FaCircleDot, FaRegCircle } from "react-icons/fa6";

interface IRenderDynamicElement {
    item: IDynamicElement;
    value: any;
    handleArrayChange: (item: IDynamicElement, value: any) => void;
    handleReset: (item: IDynamicElement) => void;
}

export const RenderDynamicElement = memo(
    ({ item, value, handleArrayChange, handleReset }: IRenderDynamicElement) => {
        switch (item.renderType) {
            case "spliter":
                return <FilterContentSpliter />;
            case "check-box": {
                const options = item.options ?? [];
                return (
                    <Selection
                        type={item.componentType as SelectionProps['type']}
                        label={item.label}
                        activeIcon={FaCircleDot}
                        inActiveIcon={FaRegCircle}
                        options={options}
                        values={value}
                        handleChange={(value) => handleArrayChange(item, value)}
                        handleReset={item.reset ? () => handleReset(item) : undefined}
                        style='width: 330px;'
                    />
                );
            }
            case "selection-box": {
                const options = item.options ?? [];
                return (
                    <Selection
                        type={item.componentType as SelectionProps['type']}
                        inputLabel={item.inputLabel}
                        options={options}
                        values={value}
                        handleChange={(newValue) => handleArrayChange(item, newValue)}
                        handleReset={item.reset ? () => handleReset(item) : undefined}
                        style='width: 330px;'
                        {...item.componentProps}
                    />
                );
            }
            default:
                return null;
        }
    },
    (prevProps, nextProps) => {
        // Only rerender if item or value changes
        return (
            prevProps.item === nextProps.item &&
            prevProps.value === nextProps.value
        );
    }
);