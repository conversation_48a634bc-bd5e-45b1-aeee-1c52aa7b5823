export interface IDynamicElement {
    // ## Render props
    renderType: 'spliter' | 'check-box' | 'selection-box';
    setKey?: string;
    returnKey?: string;
    // ## Component props
    options?: any[];
    value?: any;
    defaultValue?: any;
    resetValue?: any;
    componentType?: string;
    label?: string;
    inputLabel?: string;
    componentProps?: object;
    [key: string]: any;
};

export interface FilterProps {
    isVisible: boolean;
    onClose: () => void;
    labels: { title: string; subTitle: string; };
    data: IDynamicElement[];
    onApply: (props?: { [key: string]: any }) => void;
    onReset: (props?: { [key: string]: any }) => void;
};