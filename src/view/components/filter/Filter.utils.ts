import { IDynamicElement } from "./Filter.type";


interface IExtractValueFromSrc {
    data: IDynamicElement[];
    setKey?: string;
    setKeyRef?: string;
    getKey?: string;
    getKeyRef?: string;
};

export const extractValueFromSrc = ({
    data,
    setKey,
    setKeyRef,
    getKey,
    getKeyRef,
}: IExtractValueFromSrc) => {
    return data.reduce((a: any, e) => {
        const key = setKey ? setKey : setKeyRef && e[setKeyRef];
        const value = getKey ? e[getKey] : getKeyRef && e[getKeyRef];
        if (key && value) a[key] = value;
        return a;
    }, {});
};

export const doPushOrPopAtIndex = (oldValues: any, value: any, index: number = 0, min?: number) => {
    if (oldValues[index] != value) return [value];
    else if (min) return [value];
    else return [];
};

export const doPushOrPop = (oldValues: any[], value: any, min?: number, max?: number) => {
    const values = [...oldValues];
    const index = values.findIndex((e) => e.key == value.key);
    if (index == -1) values.push(value);
    else values.splice(index, 1);

    // ## Validation
    const length = values.length;
    const minValid = !min || min <= length;
    const maxValid = !max || max >= length;

    if (minValid && maxValid) {
        return values;
    }
    return oldValues;
};
