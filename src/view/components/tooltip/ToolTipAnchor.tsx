import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { ToolTipConatinerAnchor } from './ToolTip.style';

interface ToolTipAnchorProps {
    info: string;
    isVisible?: boolean;
    anchorEl?: HTMLElement | null;
}

export const ToolTipAnchor: React.FC<ToolTipAnchorProps> = ({ info, isVisible = false, anchorEl }) => {
    const [position, setPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });

    useEffect(() => {
        if (isVisible && anchorEl) {
            const rect = anchorEl.getBoundingClientRect();
            setPosition({
                top: rect.top - 10,
                left: rect.left,
            });
        };
    }, [isVisible, anchorEl]);

    if (!isVisible || !anchorEl) return null;

    return ReactDOM.createPortal(
        <ToolTipConatinerAnchor
            isVisible={isVisible}
            style={{
                top: position.top,
                left: position.left,
            }}
        >
            {info}
        </ToolTipConatinerAnchor>,
        document.body
    );
}; 