import { themeTokens } from "src/theme/tokens";
import styled from "styled-components";

export const ToolTipConatiner = styled.div<{ isVisible?: boolean; customStyle?: string; }>`
  // ## Position
  position: absolute;
  top: -8px;
  z-index: 9999;

  //  ## Div
  width: 75%;
  background-color: ${themeTokens.BALCK_1};
  border: 1px solid ${themeTokens.transparent};
  border-radius: 8px;
  padding: 8px 12px;
  transform: translate(10%, -100%);
  
  // ## Font
  font-size: 12px;
  font-weight: 600;
  color: ${themeTokens.whiteBg};

  // ## Animation
  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  transition: opacity 0.5s ease-in-out;
  visibility: ${({ isVisible }) => (isVisible ? 'visible' : 'hidden')};
  ${({ customStyle }) => customStyle && customStyle};
`;

export const ToolTipConatinerAnchor = styled.div<{ isVisible?: boolean; customStyle?: string; }>`
  // ## Position
  position: fixed;
  z-index: 9999;

  //  ## Div
  width: max-content;
  background-color: ${themeTokens.BALCK_1};
  border: 1px solid ${themeTokens.transparent};
  border-radius: 8px;
  padding: 8px 12px;
  transform: translate(10%, -100%);
  
  // ## Font
  font-size: 12px;
  font-weight: 600;
  color: ${themeTokens.whiteBg};

  // ## Animation
  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  transition: opacity 0.5s ease-in-out;
  visibility: ${({ isVisible }) => (isVisible ? 'visible' : 'hidden')};
  ${({ customStyle }) => customStyle && customStyle};

  // ## Other
  pointerEvents: none;
`;