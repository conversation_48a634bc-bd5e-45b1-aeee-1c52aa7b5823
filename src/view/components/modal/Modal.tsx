import React, { useEffect, useState } from 'react';
import { OverlayContainer, ModalContainers, } from './Modal.style';

interface ModalProps {
    type: "Default" | "Filter" | "PDFViewer";
    isVisible: boolean;
    onClose: () => void;
    children: React.ReactNode;
    overlayStyle?: string;
    containerStyle?: string;
}

export const Modal: React.FC<ModalProps> = ({
    type = "Default",
    isVisible,
    onClose,
    children,
    overlayStyle = '',
    containerStyle = ''
}) => {
    // ## State
    const [isOpen, setIsOpen] = useState(false);

    // ## Lifecycle Methods
    useEffect(() => {
        if (isVisible) setIsOpen(true);
        else setTimeout(() => setIsOpen(false), 500);
    }, [isVisible]);

    const ModalContainer = ModalContainers[type];
    if (!isOpen || !ModalContainer)
        return null;
    return (
        <OverlayContainer onClick={onClose} customStyle={overlayStyle}>
            <ModalContainer
                onClick={e => e.stopPropagation()}
                customStyle={containerStyle}
                isClosing={!isVisible}
            >
                {children}
            </ModalContainer>
        </OverlayContainer>
    );
};