import styled, { css, keyframes } from "styled-components";

interface ContainerProps {
  isClosing?: boolean;
  customStyle?: string;
};

export const OverlayContainer = styled.div<ContainerProps>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  ${({ customStyle }) => customStyle && customStyle};
`;

export const ModalContainerDefault = styled.div<ContainerProps>`
  min-width: 320px;
  min-height: 120px;
  background: #fff;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  position: relative;
  ${({ customStyle }) => customStyle && customStyle};
`;

const slideIn = (rtFrom: string, rtTo: string) => keyframes`
  from {
    right: ${rtFrom};
  }
  to {
    right: ${rtTo};
  }
`;

export const ModalContainerFilter = styled.div<ContainerProps>`
  /* Position at right end, vertically centered */
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  width: 400px;
  background-color: #FFFFFF;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  /* Inner Div */  
  display: flex;
  flex-direction: column;
  ${({ isClosing }) =>
    isClosing
      ? css`animation: ${slideIn('0px', '-400px')} 0.5s cubic-bezier(0.23, 1, 0.32, 1) forwards;`
      : css`animation: ${slideIn('-400px', '0px')} 0.5s cubic-bezier(0.23, 1, 0.32, 1) forwards;`
  }
  ${({ customStyle }) => customStyle && customStyle};
`;

export const ModalContainerPDFViewer = styled.div<ContainerProps>`
  position: fixed;
  bottom: 0;
  left: 0;  
  min-width: 100%;
  min-height: 90%;
  background: #fff;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  overflow: hidden;
  ${({ customStyle }) => customStyle && customStyle};
`;

/*
 * ModalContainer contains all Variants
 */
export const ModalContainers = {
  Default: ModalContainerDefault,
  Filter: ModalContainerFilter,
  PDFViewer: ModalContainerPDFViewer,
};