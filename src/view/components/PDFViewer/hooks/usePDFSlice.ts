import { useEffect, useState, useRef } from 'react';
import { slicePDF } from 'src/view/components/PDFViewer/utils';

/**
 * Custom hook that only calls a callback function when the presigned URL actually changes
 * @param presignedUrl - The presigned URL to monitor
 */
export const usePDFSlice = (
    originalPDF: ArrayBuffer | null,
    pages: number[],
) => {
    const [slicedPDF, setSlicedPDF] = useState<string>('');
    const prevValuesRef = useRef<{ originalPDF: ArrayBuffer | null; pages: number[] } | null>(null);

    useEffect(() => {
        const prevValues = prevValuesRef.current;
        
        // Check if values have actually changed
        const hasChanged = !prevValues || 
            prevValues.originalPDF !== originalPDF || 
            JSON.stringify(prevValues.pages) !== JSON.stringify(pages);
        
        if (hasChanged && originalPDF) {
            onSlicePDF();
            // Update the ref with current values
            prevValuesRef.current = { originalPDF, pages: [...pages] };
        }
    }, [originalPDF, pages]);

    const onSlicePDF = async () => {
        console.log("## PDFViewer: usePDFSlice", pages);
        const slicedPDF = await slicePDF(originalPDF, pages);
        setSlicedPDF(slicedPDF.pdfUrl);
    };

    return slicedPDF;
}; 