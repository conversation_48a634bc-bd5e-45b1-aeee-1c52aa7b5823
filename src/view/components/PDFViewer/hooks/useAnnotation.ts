import { AnnotationDataFormat, PageInfoModel } from '@syncfusion/ej2-react-pdfviewer';
import { SheetDTO } from 'src/api';
import { themeTokens } from 'src/theme/tokens';

/**
 * Custom hook to fetch PDF meta information for a project and scope.
 * @param projectId - The currently selected project ID
 * @param scopeId - The currently selected scope ID
 */
export function useAnnotation(
    sheets: SheetDTO[],
    target: PageInfoModel | undefined,
    viewerRef: any
) {
    const annotation: any = { pdfAnnotation: {} };
    const highlights: any[] = [];

    sheets.map((sheet, sheetIndex: number) => {
        if (!sheet || !sheet.pageNumber)
            return;

        sheet.regionDetail?.map((quad, index) => {
            const newQuad = rotateCoords(
                quad.quadPx,
                sheet.pageWidth,
                sheet.pageHeight,
                target?.width,
                target?.height,
                target?.rotation
            );

            if (newQuad.str) {
                const highlight = {
                    type: 'Highlight',
                    page: '0',
                    rect: { x: '', y: '', width: '', height: '' },
                    title: 'Guest',
                    subject: 'Highlight',
                    date: "D:20250515151547+05'30'",
                    name: `c55b856e-566c-4f6b-184e-deea5464be1b-${index}`,
                    color: themeTokens.lightGreen,
                    opacity: '1',
                    flags: 'print',
                    width: '1',
                    coords: `${newQuad.str}`
                };
                highlights.push(highlight);
            }
        });
        annotation.pdfAnnotation[sheetIndex] = {
            shapeAnnotation: highlights,
        };
    });

    return annotation;
};

function rotateCoords(
    coords?: number[],
    width?: number,
    height?: number,
    tWidth?: number,
    tHeight?: number,
    angle?: number
): { str: string; arr: number[] } {
    if (coords && width && height && tWidth && tHeight) {
        const rotated: number[] = [];

        for (let i = 0; i < coords.length; i += 2) {
            const x = coords[i];
            const y = coords[i + 1];

            let newX = x;
            let newY = y;

            switch (angle) {
                case 0:
                    // No rotation
                    newX = x;
                    newY = height - y;
                    break;
                case 90:
                    // 90° clockwise
                    newX = y;
                    newY = x;
                    break;
                case 180:
                    // 180°
                    newX = width - x;
                    newY = y;
                    break;
                case 270:
                    // 270° clockwise (or 90° counter-clockwise)
                    newX = height - y;
                    newY = width - x;
                    break;
                default:
                    throw new Error('Unsupported rotation angle. Use 0, 90, 180, or 270.');
            }

            rotated.push(newX, newY);
        }
        let scale = 1;

        if (tWidth > tHeight) {
            scale = tWidth / width;
        } else {
            scale = tHeight / height;
        }
        const scaled = rotated?.map(coord => coord * scale);

        const scaledX = [scaled[0], scaled[2], scaled[4], scaled[6]];
        const scaledY = [scaled[1], scaled[3], scaled[5], scaled[7]];

        const scaledArr = [
            Math.min(...scaledX),
            Math.max(...scaledY),
            Math.max(...scaledX),
            Math.max(...scaledY),
            Math.min(...scaledX),
            Math.min(...scaledY),
            Math.max(...scaledX),
            Math.min(...scaledY)
        ];
        const scaledStr = scaledArr.join(',');
        return { str: scaledStr, arr: scaledArr };
    }
    return { str: '', arr: [] };
};
//     coords?: number[],
//     width?: number,
//     height?: number,
//     tWidth?: number,
//     tHeight?: number
// ) => {
//     if (coords && width && height && tWidth && tHeight) {
//         let scale = 1;

//         if (tWidth > tHeight) {
//             scale = tWidth / width;
//         } else {
//             scale = tHeight / height;
//         }
//         const adjustFactor = tWidth > tHeight ? tWidth * 0.1 : tHeight * 0.1;
//         const adjustScaleX = tWidth / tHeight;
//         const adjustScaleY = tHeight / tWidth;
//         const scaledX = coords[0] * scale * pointToPixel;
//         const scaledY = coords[1] * scale * pointToPixel;

//         return {
//             x:
//                 scaledX > adjustFactor * adjustScaleX
//                     ? scaledX - adjustFactor * adjustScaleX
//                     : scaledX * 0.1,
//             y:
//                 scaledY > adjustFactor * adjustScaleY
//                     ? scaledY - adjustFactor * adjustScaleY
//                     : scaledY * 0.1,
//             width: 100 * adjustScaleY,
//             height: 100 * adjustScaleX
//         };
//     }
//     return null;
// };