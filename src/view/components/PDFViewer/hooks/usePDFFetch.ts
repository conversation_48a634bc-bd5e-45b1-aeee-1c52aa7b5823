import { useEffect, useRef, useState } from 'react';
import { downloadPDF } from 'src/view/components/PDFViewer/utils';

/**
 * Custom hook that only calls a callback function when the presigned URL actually changes
 * @param presignedUrl - The presigned URL to monitor
 */
export const usePDFFetch = (
    presignedUrl: string | undefined,
) => {
    const previousPresignedUrlRef = useRef<string | undefined>(undefined);
    const [originalPDF, setOriginalPDF] = useState<ArrayBuffer | null>(null);

    useEffect(() => {
        // Only call callback if the URL has actually changed
        if (presignedUrl && presignedUrl !== previousPresignedUrlRef.current) {
            previousPresignedUrlRef.current = presignedUrl;
            onDownload();
        }
    }, [presignedUrl]);

    const onDownload = async () => {
        console.log("## PDFViewer: usePDFFetch");
        const originalPDF = await downloadPDF(presignedUrl);
        setOriginalPDF(originalPDF.pdf);
    };

    return originalPDF;
}; 