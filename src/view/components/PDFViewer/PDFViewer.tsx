import React from 'react';
import {
    PdfViewerComponent,
    Toolbar,
    Magnification,
    Navigation,
    LinkAnnotation,
    Print,
    TextSelection,
    Annotation,
    TextSearch,
    FormFields,
    FormDesigner,
    Inject,
    ExtractTextOption,
    PageOrganizer,
} from '@syncfusion/ej2-react-pdfviewer';
import { Conatiner } from './PDFViewer.style';
import { Loader } from '../loader';

export enum CustomToolbarItems {
    add = 'add',
    previousPage = 'previous_page',
    nextPage = 'next_page',
    saveAnnotation = 'save_annotation'
};

export type PDFViewerProps = {
    isLoading: boolean;
    pdfMeta: {
        pdfId: number;
        pdfUrl: string;
        pdfName: string;
    };


    onAddAnnotation?: (e: any) => void;
    onDocumentLoad?: () => void;
    onPageRendered?: () => void;
    onExtractText?: (e: any) => void;
    onToolBarClick?: (args: any) => void;
    onTextSelectionEnd?: (args: any) => void;
    downloadFileName?: string;
    isPreviousPageDisabled?: boolean;
    isNextPageDisabled?: boolean;
};

export const PDFViewer: React.FC<PDFViewerProps> = ({
    isLoading,
    pdfMeta,
    isPreviousPageDisabled = false,
    isNextPageDisabled = false,

    onAddAnnotation,
    onDocumentLoad,
    onPageRendered,
    onExtractText,
    onToolBarClick,
    onTextSelectionEnd,
}) => {
    const { pdfId, pdfUrl, pdfName, } = pdfMeta;
    const key = `${pdfId}-${pdfUrl}`;

    return (
        <Conatiner>
            {(isLoading || !pdfUrl) &&
                <Loader variant={'overlay-absolute'} />
            }
            <PdfViewerComponent
                key={key}
                resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
                downloadFileName={pdfName}
                documentPath={pdfUrl}

                // ## Style
                height={'100%'}
                width={"100%"}




                // ## Other
                maxZoom={800}
                isExtractText
                annotationAdd={onAddAnnotation}
                extractTextOption={ExtractTextOption.TextAndBounds}
                documentLoad={onDocumentLoad}
                pageRenderComplete={onPageRendered}
                extractTextCompleted={onExtractText}
                toolbarClick={onToolBarClick}
                textSelectionEnd={onTextSelectionEnd}
                pageOrganizerSettings={{
                    canCopy: false,
                    canDelete: false,
                    canImport: false,
                    canInsert: false,
                    canRearrange: false,
                    canRotate: true
                }}
                toolbarSettings={{
                    showTooltip: true,
                    toolbarItems: [
                        {
                            tooltipText: 'Add Bid Item',
                            id: CustomToolbarItems.add,
                            align: 'right',
                            text: '+',
                            type: 'Button',
                            cssClass: 'e-pv-add-button'
                        },
                        {
                            tooltipText: 'Save Changes',
                            id: CustomToolbarItems.saveAnnotation,
                            align: 'right',
                            text: 'Save',
                            type: 'Button',
                            prefixIcon: 'e-save'
                        },
                        {
                            tooltipText: 'Previous page',
                            id: CustomToolbarItems.previousPage,
                            align: 'right',
                            text: 'Prev',
                            type: 'Button',
                            prefixIcon: 'e-pv-previous-page-navigation-icon',
                            cssClass: isPreviousPageDisabled ? 'e-pv-previous-page-navigation-icon-disabled' : ''
                        },
                        {
                            tooltipText: 'Next page',
                            id: CustomToolbarItems.nextPage,
                            align: 'right',
                            text: 'Next',
                            type: 'Button',
                            prefixIcon: 'e-pv-next-page-navigation-icon',
                            cssClass: isNextPageDisabled ? 'e-pv-next-page-navigation-icon-diabled' : ''
                        },
                        'UndoRedoTool',
                        'PanTool',
                        'SelectionTool',
                        'CommentTool',
                        'AnnotationEditTool',
                        'SearchOption',
                        'MagnificationTool',
                        'DownloadOption'
                    ]
                }}
            >
                <Inject
                    services={[
                        Toolbar,
                        Magnification,
                        Navigation,
                        Annotation,
                        LinkAnnotation,
                        Print,
                        TextSelection,
                        TextSearch,
                        FormFields,
                        FormDesigner,
                        PageOrganizer
                    ]}
                />
            </PdfViewerComponent>
        </Conatiner>
    );
};