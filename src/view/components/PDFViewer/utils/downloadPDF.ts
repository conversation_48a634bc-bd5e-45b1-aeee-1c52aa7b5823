export const downloadPDF = async (originalPDFUrl?: string) => {
    try {
        if (!originalPDFUrl)
            throw new Error('usePDFFetch: No PDF URL provided');

        const response = await fetch(originalPDFUrl);
        if (!response.ok)
            throw new Error(`usePDFFetch: Failed to fetch PDF: ${response.status} ${response.statusText}`);

        const originalPDFInBytes = await response.arrayBuffer();
        return {
            pdf: originalPDFInBytes,
            error: ''
        };
    } catch (error) {
        return {
            pdf: null,
            error: error instanceof Error ? error.message : 'usePDFFetch: Unknown error occurred.'
        };
    }
};