import { PDFDocument } from 'pdf-lib';

interface SlicePDFResult {
    pdfUrl: string;
    error: string;
}

export const slicePDF = async (
    originalPDF: ArrayBuffer | null,
    pages: number[]
): Promise<SlicePDFResult> => {
    try {
        if (!originalPDF || !pages || pages.length === 0) {
            throw new Error('slicePDF: No PDF or page numbers provided.');
        }

        const originalPDFLoaded = await PDFDocument.load(originalPDF);
        // ## Create and Save
        const pdf = await PDFDocument.create();
        for (const page of pages) {
            if (!(page > -1)) {
                throw new Error('slicePDF: Invalid page number.');
            }
            const [copiedPage] = await pdf.copyPages(originalPDFLoaded, [page]);
            pdf.addPage(copiedPage);
        }
        const childPdfBytes = await pdf.save({
            useObjectStreams: false,
            addDefaultPage: false
        });

        // ## Get access url
        const blob = new Blob([childPdfBytes], { type: 'application/pdf' });
        const pdfUrl = URL.createObjectURL(blob);

        return {
            pdfUrl,
            error: ''
        };
    } catch (error) {
        return {
            pdfUrl: '',
            error: error instanceof Error ? error.message : 'slicePDF: Unknown error occurred.'
        };
    }
}; 