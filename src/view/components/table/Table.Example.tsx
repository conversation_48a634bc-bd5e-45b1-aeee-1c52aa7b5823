import React from 'react';
import { Table } from './Table';
import { TableColumn } from './Table.type';

const TableExample: React.FC = () => {
    const columns: TableColumn[] = [
        {
            key: 'name',
            title: 'Project Name',
            uiConfig: { width: '250px' }
        },
        {
            key: 'type',
            title: 'Type',
            uiConfig: { width: '120px' }
        },
        {
            key: 'status',
            title: 'Status',
            uiConfig: { width: '100px' }
        },
        {
            key: 'progress',
            title: 'Progress',
            uiConfig: { width: '100px' }
        },
        {
            key: 'description',
            title: 'Description',
            uiConfig: { width: '300px' }
        }
    ];

    const data = [
        {
            key: 'parent-1',
            name: '🏗️ Construction Project Alpha',
            type: 'Main Project',
            status: 'Active',
            progress: '75%',
            description: 'Large-scale construction project with multiple phases and subcontractors',
            children: [
                {
                    key: 'child-1-1',
                    name: '📋 Foundation Phase',
                    type: 'Phase',
                    status: 'Completed',
                    progress: '100%',
                    description: 'Foundation and structural work completed successfully'
                },
                {
                    key: 'child-1-2',
                    name: '🏗️ Framing Phase',
                    type: 'Phase',
                    status: 'In Progress',
                    progress: '60%',
                    description: 'Structural framing and roof installation in progress'
                },
                {
                    key: 'child-1-3',
                    name: '🔧 MEP Installation',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Mechanical, electrical, and plumbing installation'
                },
                {
                    key: 'child-1-4',
                    name: '🎨 Interior Finishing',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Interior finishes, painting, and final touches'
                }
            ]
        },
        {
            key: 'parent-2',
            name: '🏢 Office Building Beta',
            type: 'Main Project',
            status: 'Planning',
            progress: '25%',
            description: 'Modern office building with sustainable design features',
            children: [
                {
                    key: 'child-2-1',
                    name: '📐 Design Phase',
                    type: 'Phase',
                    status: 'In Progress',
                    progress: '80%',
                    description: 'Architectural and engineering design completion'
                },
                {
                    key: 'child-2-2',
                    name: '📋 Permitting',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Building permits and regulatory approvals'
                },
                {
                    key: 'child-2-3',
                    name: '🏗️ Construction',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Main construction phase'
                }
            ]
        },
        {
            key: 'parent-3',
            name: '🏭 Industrial Complex Gamma',
            type: 'Main Project',
            status: 'On Hold',
            progress: '15%',
            description: 'Large industrial manufacturing facility',
            children: [
                {
                    key: 'child-3-1',
                    name: '🔍 Site Analysis',
                    type: 'Phase',
                    status: 'Completed',
                    progress: '100%',
                    description: 'Environmental and geotechnical site analysis'
                },
                {
                    key: 'child-3-2',
                    name: '📋 Feasibility Study',
                    type: 'Phase',
                    status: 'In Progress',
                    progress: '30%',
                    description: 'Economic and technical feasibility analysis'
                }
            ]
        },
        {
            key: 'parent-4',
            name: '🏠 Residential Development Delta',
            type: 'Main Project',
            status: 'Active',
            progress: '45%',
            description: 'Mixed-use residential development with commercial spaces',
            children: [
                {
                    key: 'child-4-1',
                    name: '🏗️ Phase 1 - Foundation',
                    type: 'Phase',
                    status: 'Completed',
                    progress: '100%',
                    description: 'Foundation work for all buildings completed'
                },
                {
                    key: 'child-4-2',
                    name: '🏗️ Phase 2 - Structure',
                    type: 'Phase',
                    status: 'In Progress',
                    progress: '70%',
                    description: 'Structural work for residential buildings'
                },
                {
                    key: 'child-4-3',
                    name: '🏗️ Phase 3 - Finishing',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Interior and exterior finishing work'
                },
                {
                    key: 'child-4-4',
                    name: '🌳 Phase 4 - Landscaping',
                    type: 'Phase',
                    status: 'Not Started',
                    progress: '0%',
                    description: 'Landscaping and outdoor amenities'
                }
            ]
        }
    ];

    return (
        <div style={{ height: '700px', width: '100%', padding: '20px', backgroundColor: '#f5f5f5' }}>
            <div style={{ 
                backgroundColor: 'white', 
                borderRadius: '8px', 
                padding: '20px', 
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                height: '100%'
            }}>
                <h2 style={{ margin: '0 0 10px 0', color: '#333' }}>🏗️ Construction Projects - SectionList Demo</h2>
                <p style={{ margin: '0 0 20px 0', color: '#666', fontSize: '14px' }}>
                    Scroll down to experience smooth SectionList behavior. Parent rows become sticky when their children are in view, 
                    just like React Native's SectionList component.
                </p>
                <Table
                    columns={columns}
                    rows={data}
                    renderChildren={[0, 1, 2, 3]} // All parent rows have children
                    stickyConfig={{
                        header: true,
                        column: true
                    }}
                    style={{ height: 'calc(100% - 80px)' }}
                    enableScroll={true}
                />
            </div>
        </div>
    );
};

export default TableExample; 