# Table Component - Optimized Structure

## Overview

The Table component has been refactored into separate, memoized components to improve performance and prevent unnecessary re-renders.

## File Structure

```
table/
├── Table.tsx              # Main table component that orchestrates header and data sections
├── Table.Header.tsx       # Dedicated header component (memoized)
├── Table.Data.tsx         # Dedicated data wrapper component (memoized with forwardRef)
├── Table.Row.tsx          # Individual row component (memoized)
├── Table.type.ts          # TypeScript interfaces and types
├── Table.style.ts         # Styled components
├── Table.Example.tsx      # Demo component
├── Table.util.ts          # Utility functions
├── Table.README.md        # This documentation
├── Table.EXPORTS.md       # Export documentation
└── index.ts               # Module exports
```

## Component Structure

### Main Components

1. **Table.tsx** - Main table component that orchestrates header and data sections
2. **Table.Header.tsx** - Dedicated header component (memoized)
3. **Table.Data.tsx** - Dedicated data wrapper component (memoized with forwardRef)
4. **Table.Row.tsx** - Individual row component (memoized)

### Performance Optimizations

#### 1. Memoization
- All components are wrapped with `React.memo()` to prevent unnecessary re-renders
- Components only re-render when their specific props change

#### 2. Separate Concerns
- **TableHeader**: Only re-renders when columns or stickyConfig changes
- **TableData**: Only re-renders when data, columns, or stickyConfig changes
- **TableRow**: Only re-renders when the specific row data changes

#### 3. Optimized Event Handlers
- Row click handlers use `useCallback` to prevent function recreation on every render

#### 4. Proper Key Management
- Each component has unique, stable keys to help React's reconciliation process

## Usage

### Basic Usage (unchanged)
```tsx
import { Table } from './components/table';

<Table
    columns={columns}
    data={data}
    renderData={renderData}
    stickyConfig={{ header: true, column: true }}
/>
```

### Using Individual Components (if needed)
```tsx
import { TableHeader, TableData, TableRow } from './components/table';

// Custom table structure
<TableWrapper>
    <TableHeaderWrapper>
        <TableHeader
            renderKey="custom"
            columns={columns}
            stickyConfig={stickyConfig}
        />
    </TableHeaderWrapper>
    <TableData
        renderKey="custom"
        columns={columns}
        data={data}
        stickyConfig={stickyConfig}
        renderData={renderData}
        dataLevel={0}
        enableScroll={true}
    />
</TableWrapper>
```

## Performance Benefits

1. **Reduced Re-renders**: Header doesn't re-render when data changes, and vice versa
2. **Granular Updates**: Individual rows only update when their specific data changes
3. **Better Memory Usage**: Memoized components prevent unnecessary function recreations
4. **Improved Scroll Performance**: Separate scroll containers with proper ref forwarding

## Migration Notes

- The public API remains unchanged
- All existing functionality is preserved
- No breaking changes to existing implementations
- Improved performance out of the box 