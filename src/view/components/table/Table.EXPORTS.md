# Table Module Exports

This document lists all available exports from the table module.

## File Structure

```
table/
├── Table.tsx              # Main table component
├── Table.Header.tsx       # Header component
├── Table.Data.tsx         # Data wrapper component
├── Table.Row.tsx          # Individual row component
├── Table.type.ts          # TypeScript interfaces
├── Table.style.ts         # Styled components
├── Table.Example.tsx      # Demo component
├── Table.util.ts          # Utility functions
├── Table.README.md        # Component documentation
├── Table.EXPORTS.md       # This file
└── index.ts               # Module exports
```

## Import Options

### Option 1: Import from main components index
```tsx
import { 
    Table, 
    TableHeader, 
    TableData, 
    TableRow,
    TableExample,
    // ... all other exports
} from '../components';
```

### Option 2: Import directly from table module
```tsx
import { 
    Table, 
    TableHeader, 
    TableData, 
    TableRow,
    TableExample,
    // ... all other exports
} from '../components/table';
```

## Available Exports

### Main Components
- `Table` - Main table component
- `TableHeader` - Header component (memoized)
- `TableData` - Data wrapper component (memoized with forwardRef)
- `TableRow` - Individual row component (memoized)

### Types
- `TableProps` - Main table props interface
- `TableColumn` - Column configuration interface
- `ITableColumn` - Column props interface
- `ITableColumnRow` - Column row props interface

### Styled Components
- `TableWrapper` - Main table container
- `TableHeaderWrapper` - Header container
- `TableHeaderCellWrapper` - Header cell container
- `TableHeaderCell` - Individual header cell
- `TableHeaderCellLabel` - Header cell label
- `TableDataWrapper` - Data container
- `TableDataCellWrapper` - Data row container
- `TableDataCell` - Individual data cell

### Example Component
- `TableExample` - Demo component showing table usage

## Usage Examples

### Basic Table
```tsx
import { Table, TableColumn } from '../components';

const columns: TableColumn[] = [
    { key: 'name', title: 'Name', uiConfig: { width: '200px' } },
    { key: 'status', title: 'Status', uiConfig: { width: '100px' } }
];

<Table columns={columns} data={data} />
```

### Custom Table Structure
```tsx
import { 
    TableWrapper, 
    TableHeaderWrapper, 
    TableHeader, 
    TableData 
} from '../components';

<TableWrapper>
    <TableHeaderWrapper>
        <TableHeader columns={columns} stickyConfig={stickyConfig} />
    </TableHeaderWrapper>
    <TableData 
        columns={columns} 
        data={data} 
        stickyConfig={stickyConfig}
        renderData={renderData}
    />
</TableWrapper>
```

### Using Styled Components
```tsx
import { 
    TableWrapper, 
    TableHeaderCell, 
    TableDataCell 
} from '../components';

<TableWrapper>
    <TableHeaderCell width="200px">Custom Header</TableHeaderCell>
    <TableDataCell width="200px">Custom Data</TableDataCell>
</TableWrapper>
```

### Demo Component
```tsx
import { TableExample } from '../components';

<TableExample />
``` 