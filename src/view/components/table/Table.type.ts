import React from 'react';

// ## Table 
export interface TableProps {
    renderKey?: string;
    columns: TableColumn[];
    rows: Record<string, any>[];
    rowLevel?: number;
    renderColumn?: (props: ITableColumn) => React.ReactElement;
    renderRow?: (props: ITableColumnRow) => React.ReactElement;
    renderChildren?: number[];
    header?: boolean;
    isAllRowExpanded?: boolean;
    stickyConfig?: {
        header?: boolean;
        column?: boolean;
    };
    className?: string;
    styles?: IStyles;
    enableScroll?: boolean;
};

// ## Header
export interface TableHeaderProps {
    renderKey: string;
    columns: TableColumn[];
    stickyConfig?: {
        header?: boolean;
        column?: boolean;
    };
    renderColumn?: (props: ITableColumn) => React.ReactElement;
    styles?: IStyles;
};

export interface TableHeaderColumnProps {
    renderKey: string;
    column: TableColumn;
    columnIndex: number;
    stickyConfig?: {
        header?: boolean;
        column?: boolean;
    };
    renderColumn?: (props: ITableColumn) => React.ReactElement;
};

// ## Data
export interface TableDataProps {
    renderKey: string;
    columns: TableColumn[];
    rows: Record<string, any>[];
    rowLevel: number;
    isAllRowExpanded: boolean;
    stickyConfig?: {
        header?: boolean;
        column?: boolean;
    };
    renderRow?: (props: ITableColumnRow) => React.ReactElement;
    enableScroll: boolean;
};

export interface TableDataRowProps {
    renderKey: string;
    columns: TableColumn[];
    row: Record<string, any>;
    rowIndex: number;
    rowLevel: number;
    isAllRowExpanded: boolean;
    stickyConfig?: {
        header?: boolean;
        column?: boolean;
    };
    renderRow?: (props: ITableColumnRow) => React.ReactElement;
};

// ## Other
export interface TableColumn {
    key: string;
    title: string;
    uiConfig: {
        width?: string;
        renderDynamicColumn?: boolean;
        renderDynamicRow?: boolean;
        showToolTip?: boolean;
    };
};

export interface ITableColumn {
    column: any;
    columnIndex: number;
};

export interface ITableColumnRow extends ITableColumn {
    key: string;
    row: any;
    rowLevel: number;
    rowIndex: number;
    isRowExpanded: boolean;
};

// ## Style
export interface IStyles {
    tableWrapper?: React.CSSProperties;
    tableHeaderWrapper?: React.CSSProperties;
    tableHeaderWrapperCells?: React.CSSProperties;
};