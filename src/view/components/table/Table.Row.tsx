import React, { memo, useEffect, useState } from 'react';
import {
    TableDataCell,
    TableDataCellWrapper,
} from './Table.style';
import { TableDataRowProps } from './Table.type';
import { Table } from './Table';

export const TableRow: React.FC<TableDataRowProps> = memo(({
    renderKey,
    columns,
    row,
    rowLevel,
    rowIndex,
    isAllRowExpanded,
    stickyConfig,
    renderRow,
}) => {
    const [isRowExpanded, setIsRowExpanded] = useState(false);
    const isChildrenExist = row.children.length;

    useEffect(() => {
        setIsRowExpanded(isAllRowExpanded);
    }, [isAllRowExpanded]);

    // ## Methods
    const handleRowClick = () => {
        setIsRowExpanded(!isRowExpanded);
    };

    return (
        <>
            <TableDataCellWrapper onClick={isChildrenExist ? handleRowClick : undefined}>
                {columns.map((col, colIndex) => {
                    const key = `${renderKey}-${colIndex}-${col.key}-${rowIndex}-${row.key}`;
                    const { width, renderDynamicRow } = col.uiConfig;
                    return (
                        <TableDataCell
                            key={key}
                            width={width}
                            sticky={stickyConfig?.column && colIndex === 0}
                            scrolled={true}
                            left={stickyConfig?.column && colIndex === 0 ? '0px' : undefined}
                        >
                            {renderDynamicRow ?
                                (renderRow ?
                                    renderRow({ key, rowLevel, isRowExpanded, column: col, columnIndex: colIndex, row, rowIndex })
                                    : null
                                )
                                : row[col.key]
                            }
                        </TableDataCell>
                    );
                })}
            </TableDataCellWrapper>
            {isRowExpanded && row.children ?
                <Table
                    columns={columns}
                    rows={row.children}
                    renderRow={renderRow}
                    header={false}
                    stickyConfig={{
                        header: false,
                        column: true
                    }}
                    styles={{
                        tableWrapper: { height: 'max-content', width: 'max-content', margin: '0px' }
                    }}
                    enableScroll={false}
                    rowLevel={rowLevel + 1}
                />
                : null
            }
        </>
    );
});

TableRow.displayName = 'TableRow'; 