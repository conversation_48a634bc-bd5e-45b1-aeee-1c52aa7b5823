// components/Table/Table.tsx
import React, { memo, useEffect, useRef } from 'react';
import { TableProps } from './Table.type';
import {
    TableWrapper,
} from './Table.style';
import { TableHeader } from './Table.Header';
import { TableData } from './Table.Data';

export const Table: React.FC<TableProps> = memo(({
    renderKey = 'parent',
    columns,
    rows,
    rowLevel = 0,
    renderColumn,
    renderRow,
    header = true,
    isAllRowExpanded = false,
    stickyConfig,
    className,
    styles,
    enableScroll = true,
}) => {
    const headerScrollRef = useRef(null);
    const bodyScrollRef = useRef(null);

    // ## Lifecycle Method
    useEffect(() => {
        const header: any = headerScrollRef?.current;
        const body: any = bodyScrollRef?.current;
        if (!header || !body)
            return () => null;

        const syncScroll = (e: any) => {
            if (e.target === header) {
                body.scrollLeft = header.scrollLeft;
            } else if (e.target === body) {
                header.scrollLeft = body.scrollLeft;
            };
        };
        header.addEventListener('scroll', syncScroll);
        body.addEventListener('scroll', syncScroll);
        resetScroll();
        return () => {
            header.removeEventListener('scroll', syncScroll);
            body.removeEventListener('scroll', syncScroll);
        };
    }, [rows]);

    const resetScroll = () => {
        const header: any = headerScrollRef.current;
        const body: any = bodyScrollRef.current;
        body.scrollLeft = 0;
        header.scrollLeft = 0;
    };

    return (
        <TableWrapper className={className} style={styles?.tableWrapper}>
            {header ?
                <TableHeader
                    renderKey={renderKey}
                    columns={columns}
                    stickyConfig={stickyConfig}
                    renderColumn={renderColumn}
                    styles={styles}
                    ref={headerScrollRef}
                />
                : null}
            <TableData
                renderKey={renderKey}
                columns={columns}
                rows={rows}
                isAllRowExpanded={isAllRowExpanded}
                stickyConfig={stickyConfig}
                renderRow={renderRow}
                rowLevel={rowLevel}
                enableScroll={enableScroll}
                ref={bodyScrollRef}
            />
        </TableWrapper>
    );
});
