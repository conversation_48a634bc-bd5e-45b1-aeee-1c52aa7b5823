import { memo, forwardRef } from 'react';
import { TableHeaderWrapper, TableHeaderWrapperCells, } from './Table.style';
import { TableHeaderProps } from './Table.type';
import { TableColumn } from './Table.Column';

export const TableHeader = memo(forwardRef<HTMLDivElement, TableHeaderProps>(({
    renderKey,
    columns,
    renderColumn,
    styles,
    stickyConfig,
}, ref) => {
    return (
        <TableHeaderWrapper ref={ref} style={styles?.tableHeaderWrapper}>
            <TableHeaderWrapperCells
                sticky={stickyConfig?.header}
                top={"0px"}
            >
                {columns.map((column, columnIndex) => (
                    <TableColumn
                        renderKey={renderKey}
                        column={column}
                        columnIndex={columnIndex}
                        renderColumn={renderColumn}
                        stickyConfig={stickyConfig}
                    />
                ))}
            </TableHeaderWrapperCells>
        </TableHeaderWrapper>
    );
}));