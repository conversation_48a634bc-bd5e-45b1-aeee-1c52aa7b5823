import styled from 'styled-components';
import { StickyProps, StyledSticky, StyledScroll } from '../utils';
import { css } from 'styled-components';

export const TableWrapper = styled.div<{ margin?: string }>`
    /* Div */
    height: 100%;
    width: 100%;
    background-color: #fff;
    margin: ${(props) => props.margin || '18px'};

    /* Inner Div */
    display: flex;
    flex-direction: column;
`;

export const TableHeaderWrapper = styled.div<StickyProps>`
    /* Div */
    height: 40px;
    width: 100%;
    background-color: transaprent;
    border-radius: 12px;
    ${() => StyledScroll()}

    /* Inner Div */
    display: flex;
    flex-direction: column;

    /* font */
    font-family: Inter;
    font-weight: 500;
    font-size: 12px;
    color: #384250;
    line-height: 16px;
    letter-spacing: 0%;
`;

export const TableHeaderWrapperCells = styled.div<StickyProps>`
    /* Div */
    height: 100%;
    min-width: max-content;
    background-color: transaprent;
    ${(props) => props.sticky && StyledSticky({ ...props, zIndex: '2' })};

    /* Inner Div */
    display: flex;
    flex-direction: row;
`;

export const TableHeaderCell = styled.div<StickyProps>`
    /* Div */
    position: relative;
    height: 100%;
    width: ${(props) => props.width || 'auto'};
    align-items: center;
    ${(props) => props.sticky && StyledSticky({ ...props, zIndex: '3' })};
    
    /* Inner Div */
    display: flex;
    background-color: #f7f7f7;
    overflow: visible;
`;

export const TableHeaderCellLabel = styled.span`
    padding: 0px 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

export const TableDataWrapper = styled.div<{ enableScroll?: boolean }>`
    /* Div */
    height: 100%;
    width: 100%;
    background-color: transaprent;
    ${({ enableScroll }) => enableScroll && `overflow: scroll;`};
`;

/**
 * Currently, the sticky property is not in use. In the future, 
 * if we want to make a row stick to the top, we can use it.
 */
export const TableDataCellWrapper = styled.div<StickyProps>`
    /* Div */
    height: 44px;
    min-width: max-content;
    background-color: transaprent;
    ${(props) => props.sticky && StyledSticky({ ...props, zIndex: '2' })};
    zIndex: 2;

    /* Inner Div */
    display: flex;
    flex-direction: row;
`;

interface TableDataCellProps extends StickyProps {
    scrolled?: boolean;
}

export const TableDataCell = styled.div<TableDataCellProps>`
    /* Div */
    height: 100%;
    width: ${(props) => props.width || 'auto'};
    background-color: #fff;
    border-bottom: 1px solid #E5E7EB;
    border-right: 1px solid #E5E7EB;
    ${(props) => props.sticky && StyledSticky({ ...props, zIndex: '3' })};
    ${(props) =>
        props.sticky && props.scrolled &&
        css`box-shadow: 12px 0px 16px -4px #0A0D1214;`
    }

    /* Inner Div */
    display: flex;
    align-items: center;
    gap: 12px;
`;
