import React, { memo, useRef, useState } from 'react';
import {
    TableHeaderCell,
    TableHeaderCellLabel,
} from './Table.style';
import { TableHeaderColumnProps, } from './Table.type';
import { ToolTipAnchor } from '../tooltip/ToolTipAnchor';

export const TableColumn: React.FC<TableHeaderColumnProps> = memo(({
    renderKey,
    column,
    columnIndex,
    stickyConfig,
    renderColumn,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const cellRef = useRef<HTMLDivElement>(null);
    const { width, renderDynamicColumn, showToolTip, } = column.uiConfig;
    const isToopTipReq = showToolTip && !renderDynamicColumn;
    return (
        <TableHeaderCell
            ref={cellRef}
            key={`${renderKey}-${column.key}`}
            width={width}
            sticky={stickyConfig?.column && columnIndex === 0}
            left={stickyConfig?.column && columnIndex === 0 ? '0px' : undefined}
            onMouseEnter={isToopTipReq ? () => setIsOpen(true) : undefined}
            onMouseLeave={isToopTipReq ? () => setIsOpen(false) : undefined}
        >
            {renderDynamicColumn ?
                (renderColumn ?
                    renderColumn({ column: column, columnIndex: columnIndex })
                    : null
                )
                : <TableHeaderCellLabel>{column.title}</TableHeaderCellLabel>
            }
            {isToopTipReq &&
                <ToolTipAnchor
                    isVisible={isOpen}
                    info={column.title}
                    anchorEl={cellRef.current}
                />
            }
        </TableHeaderCell>
    );
});