import { memo, forwardRef } from 'react';
import { TableDataWrapper } from './Table.style';
import { TableDataProps } from './Table.type';
import { TableRow } from './Table.Row';

export const TableData = memo(forwardRef<HTMLDivElement, TableDataProps>(({
    renderKey,
    columns,
    rows,
    rowLevel,
    isAllRowExpanded,
    stickyConfig,
    renderRow,
    enableScroll
}, ref) => {
    return (
        <TableDataWrapper ref={ref} enableScroll={enableScroll}>
            {rows.map((row, rowIndex) => (
                <TableRow
                    key={`${renderKey}-row-${rowIndex}-${row.key || rowIndex}`}
                    renderKey={renderKey}
                    columns={columns}
                    row={row}
                    rowIndex={rowIndex}
                    isAllRowExpanded={isAllRowExpanded}
                    stickyConfig={stickyConfig}
                    renderRow={renderRow}
                    rowLevel={rowLevel}
                />
            ))}
        </TableDataWrapper>
    );
}));

TableData.displayName = 'TableData'; 