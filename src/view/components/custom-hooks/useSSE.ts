import { useEffect, useRef, useState } from 'react';
import { ProjectDTOStatusEnum } from 'src/api';
import { DocProcessingJobStatus } from 'src/modules/utils/constant';

const useSSE = (projectId?: number, projectStatus?: ProjectDTOStatusEnum) => {
  const [status, setStatus] = useState<DocProcessingJobStatus | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    const closeEventSource = () => {
      if (eventSourceRef.current && eventSourceRef.current.readyState !== EventSource.CLOSED) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
    if (!projectId) {
      closeEventSource();
      return;
    }
    if (projectStatus === ProjectDTOStatusEnum.Failed) {
      setStatus(DocProcessingJobStatus.failed);
      closeEventSource();
      return;
    }

    if (projectStatus === ProjectDTOStatusEnum.Completed) {
      setStatus(DocProcessingJobStatus.completed);
      closeEventSource();
      return;
    }
    closeEventSource();
    const url = `${import.meta.env.VITE_API_BASE_URL}/api/projects/status/${projectId}`;

    try {
      const es = new EventSource(url);
      eventSourceRef.current = es;

      es.onopen = () => {
        // Connection opened successfully
      };

      es.onmessage = event => {
        try {
          const status = event.data;
          setStatus(status);

          if (
            status === DocProcessingJobStatus.completed ||
            status === DocProcessingJobStatus.failed ||
            status === DocProcessingJobStatus.noJob
          ) {
            es.close();
            eventSourceRef.current = null;
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      es.onerror = err => {
        console.error('SSE connection error:', err);
        es.close();
        eventSourceRef.current = null;
      };
    } catch (error) {
      console.error('Error creating EventSource:', error);
    }

    return closeEventSource;
  }, [projectId, projectStatus]);

  return status;
};

export { useSSE };
