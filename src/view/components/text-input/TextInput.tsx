import React from 'react';
import { IconType } from 'react-icons';
import { TextInputWrapper, } from './TextInput.style';

interface TextInputProps {
    type: "Search";
    placeholder?: string;
    value: string;
    onChangeValue: (text: string) => void;
    leftIcon?: IconType;
    rightIcon?: IconType;
    customStyle?: string;
};

export const TextInput: React.FC<TextInputProps> = ({
    type,
    placeholder,
    value,
    onChangeValue,
    leftIcon: LeftIcon,
    rightIcon: RightIcon,
    customStyle,
}) => {
    const TextInput = TextInputWrapper[type];
    if (!TextInput)
        return null;

    return (
        <TextInput
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChangeValue(e.target.value)}
            prefix={LeftIcon ? <LeftIcon /> : undefined}
            suffix={RightIcon ? <RightIcon /> : undefined}
            customStyle={customStyle}
        />
    );
};