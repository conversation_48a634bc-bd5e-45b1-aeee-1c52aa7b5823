import { Input } from "antd";
import styled from "styled-components";

/*
 * ## Search Variant
 */
interface TextInputWrapperProps {
  customStyle?: string;
};

interface TextInputSearchWrapperProps extends TextInputWrapperProps { };

export const TextInputSearchWrapper = styled(Input) <TextInputSearchWrapperProps>`
  /* Div */
  height: 36px;
  width: max-content;
  background-color: #FFFFFF;
  border-width: 1px;
  border-color: #D2D6DB;
  border-style: solid;
  border-radius: 10px;
  padding: 0px 12px;

  /* Font */
  font-size: 14px;
  color: #6C737F;

  /* Other */
  transition: background 0.2s ease, border-color 0.2s ease;

  &.ant-input-outlined:hover {
    border-color: #D2D6DB;
  };

  &.ant-input-outlined:focus-within {
    border-color: #FF6A34;
  };

  &.ant-input-affix-wrapper >input.ant-input {
    padding: 0px 6px;
  };

  /* Conditional */
  ${({ customStyle }) => customStyle && customStyle};
`;

/*
 * TextInputWrapper contains all Variants
 */
export const TextInputWrapper = {
  Search: TextInputSearchWrapper,
};