import { CheckOutlined, CloseOutlined, PaperClipOutlined, UploadOutlined } from '@ant-design/icons';
import Dragger from 'antd/es/upload/Dragger';
import styled from 'styled-components';

export const FlexContainer = styled.div`
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  border-radius: 20px;
  overflow: auto;
  margin: 2px;
`;

export const StyledSelectFileText = styled.p`
  color: #1976b9;
  font-size: 14px;
  font-family: Roboto;
  font-weight: 500;
  padding: 4px 2px;
`;

export const StyledSelectFileIcon = styled(UploadOutlined)`
  color: #1976b9;
  padding: 4px 8px;
`;

export const StyledSelectFileContainer = styled.div`
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  border-radius: 20px;
  border: solid #d9d9d9 2px;
  padding: 0px 8px 0px 2px;
`;

export const StyledDragDropText = styled.p`
  font-family: inter;
  font-size: 14px;
  font-weight: 400;
  color: #505050;
`;

export const StyledOrText = styled.p`
  font-family: Roboto;
  font-size: 12px;
  font-weight: 500;
  color: #9b9b9b;
`;

export const StyledcheckOutlined = styled(CheckOutlined)`
  color: #6dd92a;
  font-size: 20px;
  padding-bottom: 12px;
`;

export const StyledCloseOutlinedWrong = styled(CloseOutlined)`
  color: red;
  font-size: 20px;
  padding-bottom: 12px;
`;

export const StyledFileUploadedText = styled.p`
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  color: #3d3d3d;
  padding-bottom: 8px;
`;

export const StyledPaperClip = styled(PaperClipOutlined)`
  color: #363636;
  background-color: #f5f5f5;
  padding: 4px 0px 4px 15px;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 400;
`;

export const StyledFileName = styled.p`
  color: #363636;
  background-color: #f5f5f5;
  padding: 4px 5px;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 400;
`;

export const StyledCloseOutlined = styled(CloseOutlined)`
  color: #e85959;
  background-color: #fee6e6;
  padding: 4px 10px 4px 6px;
  font-family: Roboto;
  font-size: 10px;
  font-weight: 400;
`;

export const StyledTitle = styled.div`
  font-family: inter;
  color: #565656;
  font-weight: 400;
  font-size: 16px;
  line-height: 23.44px;
`;

export const StyledDragger = styled(Dragger)`
  .ant-upload-drag {
    border-radius: 4px;
    height: 170px;
    background-color: #ffffff;
    border: none;
  }
`;

export const StyledContainer = styled.div`
  border: 1px dashed #c7c7c7;
  border-radius: 4px;
`;

export const ImageAndTextWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
`;
