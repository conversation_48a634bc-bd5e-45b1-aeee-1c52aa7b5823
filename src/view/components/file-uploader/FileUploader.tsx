import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { Flex, Spin } from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/es/upload'; 
import uploadIcon from 'src/assets/images/uploadIcon.svg';
import { FlexContainer, StyledCloseOutlinedWrong, StyledContainer, StyledDragger, StyledFileUploadedText, StyledFileName, StyledPaperClip, StyledTitle, StyledcheckOutlined, StyledCloseOutlined, StyledDragDropText, ImageAndTextWrapper } from './FileUploader.style';

const fileUploader = (
  title: string,
  uploading: boolean,
  files: Array<UploadFile>,
  handleClose: (e: React.MouseEvent, name: string, index: number) => void,
  onChange: (info: UploadChangeParam<UploadFile<any>>, fieldName: string) => void,
  isFilesFormatSupported: boolean
) => {
  return (
    <Flex vertical={true} key={title} gap={8}>
      <StyledTitle>{title}</StyledTitle>
      <StyledContainer>
        <StyledDragger
          name={title}
          multiple={true}
          onChange={info => onChange(info, title)}
          showUploadList={false}
          fileList={files}
          beforeUpload={() => false}
          accept='.pdf'
        >
          {uploading && files.length !== 0 && (
            <Spin indicator={<LoadingOutlined spin />} size='small' />
          )}
          {!uploading && files.length !== 0 ? (
            <>
              {isFilesFormatSupported ? (
                <>
                  <StyledCloseOutlinedWrong />
                  <StyledFileUploadedText>File format not supported</StyledFileUploadedText>
                </>
              ) : (
                <>
                  <StyledcheckOutlined />
                  <StyledFileUploadedText>Files Uploaded</StyledFileUploadedText>
                </>
              )}
              {files.map((file, index) => (
                <FlexContainer>
                  <StyledPaperClip />
                  <StyledFileName>{file.name}</StyledFileName>
                  <StyledCloseOutlined onClick={e => handleClose(e, title, index)} />
                </FlexContainer>
              ))}
            </>
          ) : (
            files.length === 0 && (
              <ImageAndTextWrapper>
                <img src={uploadIcon} alt='upload icon' />
                <StyledDragDropText>
                  Click to upload or drag & drop your file here
                </StyledDragDropText>
              </ImageAndTextWrapper>
            )
          )}
        </StyledDragger>
      </StyledContainer>
    </Flex>
  );
};

export default fileUploader;
