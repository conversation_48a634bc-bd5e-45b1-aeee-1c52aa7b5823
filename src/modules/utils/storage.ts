const setItem = (key: string, value: any) => {
  localStorage.setItem(key, JSON.stringify(value));
};

const getItem = (key: string) => {
  const item = localStorage.getItem(key);
  return item ? JSON.parse(item) : null;
};

const removeItem = (key: string) => {
  localStorage.removeItem(key);
};

const clearStorage = () => {
  localStorage.clear();
};

export { clearStorage, getItem, removeItem, setItem };
