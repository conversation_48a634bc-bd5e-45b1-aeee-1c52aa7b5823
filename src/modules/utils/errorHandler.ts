const extractErrorMessage = (errors: any, defaultMessage: string = 'An error occurred'): string => {
  const error =
    errors?.response?.data?.message ??
    errors?.response?.data ??
    errors?.response?.message ??
    errors;

  if (!error) return defaultMessage;

  if (typeof error === 'string') return error;

  if (Array.isArray(error)) return error.join(' ').trim();

  if (typeof error.message === 'string') return error.message;

  if (error?.errors) {
    let errorMessage = '';
    for (const key in error.errors) {
      if (Array.isArray(error.errors[key])) {
        errorMessage += `${error.errors[key].join(' ')} `;
      }
    }
    return errorMessage.trim();
  }

  if (error.errors && typeof error.errors === 'object') {
    const errorKeys = Object.keys(error.errors);
    if (errorKeys.length > 0) {
      if (Array.isArray(error.errors[errorKeys[0]])) {
        return error.errors[errorKeys[0]].join(' ').trim();
      }
      const aggregatedErrors = errorKeys.reduce((acc: string, key) => {
        if (Array.isArray(error.errors[key])) {
          return `${acc} ${error.errors[key].join(' ')}`;
        }
        return acc;
      }, '');
      return aggregatedErrors.trim() || defaultMessage;
    }
  }

  // Default message for unknown error structures
  return defaultMessage;
};

export { extractErrorMessage };
