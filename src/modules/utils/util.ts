import dayjs from 'dayjs';

const getPathName = (path: string) => {
  let fetchedPath = '';
  fetchedPath = path.substring(
    path.indexOf('/') + 1,
    path.lastIndexOf('/') === 0 || path.lastIndexOf('/') === 1 ? path.length : path.lastIndexOf('/')
  );
  return fetchedPath;
};

const getCookie = (name: string) => {
  if (!name) return;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts?.pop()?.split(';').shift();
  return undefined;
};

const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '-';
  return dayjs(dateString).format('MM/DD/YYYY HH:mm');
};

export { getPathName, getCookie, formatDate };
