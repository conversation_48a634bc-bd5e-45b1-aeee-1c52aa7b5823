import { useMemo } from 'react';
import useGlobalStore from 'src/store/useGlobalStore';
import { MenuItem } from '../common/sidebar';

enum UserRole {
  superAdmin = 'Super Admin',
  companyAdmin = 'Company Admin',
  companyUser = 'Company User',
  externalUser = 'External User'
}

enum UserPermission {
  ASSIGN_ROLES = 'ASSIGN_ROLES',
  CREATE_COMPANY = 'CREATE_COMPANY',
  CREATE_USERS = 'CREATE_USERS',
  DELETE_COMPANY = 'DELETE_COMPANY',
  DELETE_USERS = 'DELETE_USERS',
  EDIT_COMPANY = 'EDIT_COMPANY',
  EDIT_USERS = 'EDIT_USERS',
  VIEW_ALL_COMPANY_USERS = 'VIEW_ALL_COMPANY_USERS',
  VIEW_COMPANY = 'VIEW_COMPANY',
  VIEW_USERS = 'VIEW_USERS',
  CREATE_PROJECT = 'CREATE_PROJECT',
  WYRE_AI_ADMIN_ACCESS = 'WYRE_AI_ADMIN_ACCESS'
}

enum UserProjectPermission {
  ASSIGN_PROJECT_ROLE = 'ASSIGN_PROJECT_ROLE',
  CREATE_BID_ITEM = 'CREATE_BID_ITEM',
  CREATE_PROJECT = 'CREATE_PROJECT',
  CREATE_SCOPE = 'CREATE_SCOPE',
  DELETE_BID_ITEM = 'DELETE_BID_ITEM',
  DELETE_PROJECT = 'DELETE_PROJECT',
  DELETE_SCOPE = 'DELETE_SCOPE',
  EDIT_BID_ITEM = 'EDIT_BID_ITEM',
  EDIT_PROJECT = 'EDIT_PROJECT',
  EDIT_SCOPE = 'EDIT_SCOPE',
  INVITE_PROJECT_USER = 'INVITE_PROJECT_USER',
  REMOVE_PROJECT_USER = 'REMOVE_PROJECT_USER',
  UPLOAD_PROJECT_DOCUMENT = 'UPLOAD_PROJECT_DOCUMENT',
  VIEW_BID_ITEM = 'VIEW_BID_ITEM',
  VIEW_PROJECT = 'VIEW_PROJECT',
  VIEW_PROJECT_DOCUMENT = 'VIEW_PROJECT_DOCUMENT',
  VIEW_SCOPE = 'VIEW_SCOPE',
  VIEW_PROJECT_USER = 'VIEW_PROJECT_USER',
  DEACTIVATE_PROJECT = 'DEACTIVATE_PROJECT',
  ACTIVATE_PROJECT = 'ACTIVATE_PROJECT'
}

const useUserPermissions = (requiredPermissions: UserPermission[]) => {
  const { currentUser } = useGlobalStore();
  let isPermitted = false;
  if (currentUser && currentUser.role && currentUser.role.permissions) {
    if (currentUser.role.name === UserRole.superAdmin) {
      isPermitted = true;
    } else {
      const userPermissions = currentUser.role.permissions.map(permission => permission.name) || [];
      const hasPermission = requiredPermissions.every(permission =>
        userPermissions.includes(permission)
      );
      isPermitted = hasPermission;
    }
  }
  return { isPermitted };
};

const useUserPermittedRoutes = (routes: MenuItem[]) => {
  const { currentUser } = useGlobalStore();

  const permittedRoutes = useMemo(() => {
    const userPermissions = currentUser?.role?.permissions?.map(p => p.name) ?? [];

    return routes.filter(
      route =>
        !route.requiredPermissions ||
        route.requiredPermissions.every(p => userPermissions.includes(p))
    );
  }, [routes, currentUser]);

  return permittedRoutes;
};

const useUserProjectPermissions = (requiredPermissions: UserProjectPermission[]) => {
  const { userProjectPermissions } = useGlobalStore();
  let isPermitted = false;
  if (userProjectPermissions.length > 0) {
    const hasPermission = requiredPermissions.every(permission =>
      userProjectPermissions.includes(permission)
    );
    isPermitted = hasPermission;
  }
  return { isPermitted };
};

// Check if user has Admin access (Company Admin & Super Admin)
const useAdminAccess = () => {
  const { currentUser } = useGlobalStore();

  if (!currentUser?.role) {
    return { hasAccess: false };
  }

  // Company Admin and Super Admin roles have access
  const hasAccess =
    currentUser.role.name === UserRole.superAdmin ||
    currentUser.role.name === UserRole.companyAdmin;

  return { hasAccess };
};

// Check if user has Wyre AI Admin access
const useWyreAIAdminAccess = () => {
  const { currentUser } = useGlobalStore();

  if (!currentUser?.role) {
    return { hasAccess: false };
  }

  // Super Admin and Company User roles have access
  const hasAccess =
    currentUser.role.name === UserRole.superAdmin || currentUser.role.name === UserRole.companyUser;

  return { hasAccess };
};

export {
  useUserPermissions,
  UserRole,
  UserPermission,
  UserProjectPermission,
  useUserPermittedRoutes,
  useUserProjectPermissions,
  useAdminAccess,
  useWyreAIAdminAccess
};
