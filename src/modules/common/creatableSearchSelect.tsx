import { Select, SelectProps } from 'antd';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

interface CreatableSearchSelectProps {
  initialOptions: string[];
  placeholder?: string;
  onSelectionChange?: (selectedValues: string[]) => void;
  tokenSeparators?: string[];
  defaultSelectedValues?: string[];
  width?: string;
  isCreateable?: boolean;
  isMultiple?: boolean;
  size?: 'small' | 'middle' | 'large';
}

const StyledSelect = styled(Select)<SelectProps<string | string[]> & { width?: string }>`
  width: ${({ width }) => width || '100%'};
`;

const AddOptionItem = styled.div`
  padding: 8px;
  cursor: pointer;
  &:hover {
    background-color: #f5f5f5;
  }
`;

const CreatableSearchSelect: React.FC<CreatableSearchSelectProps> = ({
  initialOptions,
  placeholder = 'Select or enter new item',
  onSelectionChange,
  width = '200px',
  tokenSeparators = [','],
  defaultSelectedValues = [],
  isCreateable = true,
  isMultiple = true,
  size = 'middle'
}) => {
  const [options, setOptions] = useState<string[]>(initialOptions);

  useEffect(() => {
    setOptions(initialOptions);
  }, [initialOptions]);

  return isMultiple ? (
    <MultiSelect
      options={options}
      defaultSelectedValues={defaultSelectedValues}
      placeholder={placeholder}
      tokenSeparators={tokenSeparators}
      width={width}
      onSelectionChange={onSelectionChange}
      isCreateable={isCreateable}
    />
  ) : (
    <SingleSelect
      size={size as 'small' | 'middle' | 'large'}
      options={options}
      defaultSelectedValue={defaultSelectedValues[0]}
      placeholder={placeholder}
      width={width}
      onSelectionChange={onSelectionChange}
      isCreateable={isCreateable}
      setOptions={setOptions}
    />
  );
};

interface MultiSelectProps {
  options: string[];
  defaultSelectedValues: string[];
  placeholder: string;
  tokenSeparators: string[];
  width: string;
  onSelectionChange?: (selectedValues: string[]) => void;
  isCreateable: boolean;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  defaultSelectedValues,
  placeholder,
  tokenSeparators,
  width,
  onSelectionChange,
  isCreateable
}) => {
  const [selectedValues, setSelectedValues] = useState<string[]>(defaultSelectedValues);

  // Update selected values when defaultSelectedValues changes
  useEffect(() => {
    setSelectedValues(defaultSelectedValues);
  }, [defaultSelectedValues]);

  const handleChange = (values: string | string[]) => {
    const valueArray = Array.isArray(values) ? values : [values];
    setSelectedValues(valueArray);
    if (onSelectionChange) {
      onSelectionChange(valueArray);
    }
  };

  return (
    <StyledSelect
      mode={isCreateable ? 'tags' : undefined}
      width={width}
      placeholder={placeholder}
      value={selectedValues}
      onChange={handleChange}
      tokenSeparators={tokenSeparators}
      showSearch
    >
      {options.map(option => (
        <Select.Option key={option} value={option}>
          {option}
        </Select.Option>
      ))}
    </StyledSelect>
  );
};

interface SingleSelectProps {
  options: string[];
  defaultSelectedValue?: string;
  placeholder: string;
  width: string;
  onSelectionChange?: (selectedValues: string[]) => void;
  isCreateable: boolean;
  setOptions: React.Dispatch<React.SetStateAction<string[]>>;
  size: 'small' | 'middle' | 'large';
}

const SingleSelect: React.FC<SingleSelectProps> = ({
  options,
  defaultSelectedValue,
  placeholder,
  width,
  onSelectionChange,
  isCreateable,
  setOptions,
  size
}) => {
  const [selectedValue, setSelectedValue] = useState<string | undefined>(defaultSelectedValue);
  const [searchText, setSearchText] = useState<string>('');

  // Update selected value when defaultSelectedValue changes
  useEffect(() => {
    if (defaultSelectedValue) {
      setSelectedValue(defaultSelectedValue);
    }
  }, [defaultSelectedValue]);

  // Reset search text when options change
  useEffect(() => {
    setSearchText('');
  }, [options]);

  const handleChange = (value: string | string[]) => {
    const singleValue = Array.isArray(value) ? value[0] : value;
    setSelectedValue(singleValue);
    if (onSelectionChange) {
      onSelectionChange([singleValue]);
    }
  };

  const handleAddOption = (newOption: string) => {
    if (!options.some(opt => opt.toLowerCase() === newOption.toLowerCase())) {
      setOptions(prevOptions => [...prevOptions, newOption]);
    }
    setSelectedValue(newOption);
    if (onSelectionChange) {
      onSelectionChange([newOption]);
    }
    setSearchText('');
  };

  return (
    <StyledSelect
      size={size}
      width={width}
      placeholder={placeholder}
      value={selectedValue}
      onChange={handleChange}
      onSearch={setSearchText}
      showSearch
      filterOption={(input, option) =>
        (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
      }
      dropdownRender={menu => (
        <>
          {isCreateable &&
            searchText &&
            !options.some(opt => opt.toLowerCase() === searchText.toLowerCase()) && (
              <AddOptionItem
                onMouseDown={e => e.preventDefault()}
                onClick={() => handleAddOption(searchText)}
              >
                {searchText}
              </AddOptionItem>
            )}
          {!options.some(opt => opt.toLowerCase() === searchText.toLowerCase()) && menu}
        </>
      )}
    >
      {options.map(option => (
        <Select.Option key={option} value={option}>
          {option}
        </Select.Option>
      ))}
    </StyledSelect>
  );
};

export default CreatableSearchSelect;
