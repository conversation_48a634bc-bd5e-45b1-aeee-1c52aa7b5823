import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { App, Dropdown, Flex, Form, Input, Layout, MenuProps, Modal, Popover } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { AiOutlineLogout } from 'react-icons/ai';
import { FaRegCircleUser } from 'react-icons/fa6';
import { IoIosHelpCircleOutline } from 'react-icons/io';
import { useLocation, useNavigate } from 'react-router-dom';
import { ProjectDTOStatusEnum, PasswordChangeDTO, ProjectDTO } from 'src/api';
import { documentAPI, projectAPI, userAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import CreatableSearchSelect from './creatableSearchSelect';
import SearchDropdown from './searchDropdown';
import SwitchCompanyModal from './SwitchCompanyModal';
import AddUser from '../user/addUser';
import {
  DocProcessingJobStatus,
  appRoutes,
  changePasswordInputText,
  excludedRoutes,
  queryKeys
} from '../utils/constant';
import { clearStorage } from '../utils/storage';
import { capitalizeWords } from '../utils/stringUtils';
import { getPathName } from '../utils/util';

const { Header } = Layout;

const StyledHeader = styled(Header)`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  height: 60px;
`;

const NavbarActions = styled(Flex)`
  width: 90%;
  align-items: center;
  justify-content: flex-end;
  gap: 5%;
`;

const CompanyName = styled.div`
  font-weight: 500;
  font-size: 18px;
`;

const StyledForm = styled(Form)`
  flex: 1;
`;

const VersionStyles = styled.div`
  font-size: 16px;
  white-space: nowrap;
`;

const HelpPopoverContainer = styled(Flex)`
  padding: 8px 4px;
  min-width: 280px;
`;

const HelpTitle = styled.div`
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 12px;
`;

const HelpText = styled.div`
  margin-bottom: 8px;
  font-size: 14px;
`;

const HelpContact = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;

  a {
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ProfileImage = styled.img`
  height: 30px;
  width: 30px;
  border-radius: 20px;
  cursor: pointer;
`;

const Navbar: React.FC = () => {
  const { useForm } = Form;
  const { notification } = App.useApp();
  const {
    selectedProjectId,
    setSelectedProjectId,
    currentUser,
    setUserProjectPermissions,
    setSelectedProjectName,
    setSelectedVersion,
    selectedVersion
  } = useGlobalStore();
  const location = useLocation();
  const navigate = useNavigate();

  const [eventSources, setEventSources] = useState<Map<number, EventSource>>(new Map());
  const { updateDocumentProcessingListeners } = useGlobalStore();
  const [passwordChangePopupOpen, setPasswordChangePopupOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [isSwitchCompanyOpen, setIsSwitchCompanyOpen] = useState(false);
  const [form] = useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();

  const { data: projectDetails } = useQuery({
    queryKey: [queryKeys.projectInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectById(Number(selectedProjectId)),
    enabled: !!selectedProjectId,
    select: res => res.data
  });

  const { data: projectsList } = useQuery({
    queryKey: [queryKeys.allProjects],
    queryFn: () =>
      // TODO: Need to Change
      projectAPI.getAllProjects(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        0,
        1000,
        ['createdAt', 'desc']
      ),
    select: res => res.data
  });

  const { data: versionList } = useQuery({
    queryKey: [queryKeys.versionList, selectedProjectId],
    queryFn: () =>
      documentAPI.getProjectDocumentVersions(Number(selectedProjectId), undefined, undefined, 200, [
        'id,desc'
      ]),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  const { mutate } = useMutation({
    mutationFn: (data: PasswordChangeDTO) => userAPI.changePassword(data),
    onSuccess: () => {
      closeHandler();
      notification.success({ message: 'Password changed successfully' });
    },
    onError: () => {
      notification.error({ message: 'Password change failed' });
    }
  });

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: [queryKeys.documentsFilters] });
    if (projectDetails) {
      setUserProjectPermissions(projectDetails?.currentUserProjectPermissions ?? []);
      if (projectDetails.name) {
        setSelectedProjectName(projectDetails.name);
      }
    }
  }, [projectDetails]);

  useEffect(() => {
    if (selectedVersion === null && versionList?.content?.[0]) {
      setSelectedVersion({
        version: versionList?.content[0]?.documentSetVersion || null,
        versionId: versionList?.content[0]?.id || null
      });
    }
  }, [versionList]);

  const isDocumentsScreen =
    window.location.pathname !==
    `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.documents}`;

  const isCreatedLessThanHourAgo = (createdAt?: string): boolean => {
    if (!createdAt) return false;
    const createdDate = new Date(createdAt);
    const now = new Date();

    const diffInMs = now.getTime() - createdDate.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);

    return diffInHours < 1;
  };

  const handleProjectStatus = (project: ProjectDTO) => {
    const {
      projectId,
      status: projectStatus,
      name: projectName,
      latestDocumentSetCreatedAt: createdAt
    } = project;
    if (!projectId || !projectStatus) return;
    if (eventSources.has(projectId)) {
      const existingEventSource = eventSources.get(projectId);
      if (existingEventSource && existingEventSource.readyState === EventSource.OPEN) {
        updateDocumentProcessingListeners({
          [projectId]: DocProcessingJobStatus.inProgress
        });
        return;
      }
      if (existingEventSource?.readyState !== EventSource.CLOSED) {
        existingEventSource?.close();
      }
    }

    if (projectStatus === ProjectDTOStatusEnum.Failed) {
      updateDocumentProcessingListeners({
        [projectId]: DocProcessingJobStatus.failed
      });
      return;
    }

    if (projectStatus === ProjectDTOStatusEnum.Completed) {
      updateDocumentProcessingListeners({
        [projectId]: DocProcessingJobStatus.completed
      });
      return;
    }
    if (projectStatus !== ProjectDTOStatusEnum.Processing) {
      return;
    }
    //attaching listeners only for projects created in last hour
    if (!isCreatedLessThanHourAgo(createdAt)) return;
    const url = `${import.meta.env.VITE_API_BASE_URL}/api/projects/status/${projectId}`;
    try {
      const es = new EventSource(url);
      updateDocumentProcessingListeners({ [projectId]: DocProcessingJobStatus.inProgress });
      es.onopen = () => {
        setEventSources(prevSources => new Map(prevSources).set(projectId, es));
      };

      es.onmessage = event => {
        try {
          const status = event.data;
          updateDocumentProcessingListeners({ [projectId]: status });

          if (
            status === DocProcessingJobStatus.completed ||
            status === DocProcessingJobStatus.failed ||
            status === DocProcessingJobStatus.noJob
          ) {
            if (status === DocProcessingJobStatus.completed)
              notification.success({
                message: `Document processing completed for project ${projectName}`
              });

            es.close();
            setEventSources(prevSources => {
              const newSources = new Map(prevSources);
              newSources.delete(projectId);
              return newSources;
            });
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      es.onerror = err => {
        console.error('SSE connection error:', err);
        es.close();
        setEventSources(prevSources => {
          const newSources = new Map(prevSources);
          newSources.delete(projectId);
          return newSources;
        });
      };
    } catch (error) {
      console.error('Error creating EventSource:', error);
    }
  };

  useEffect(() => {
    if (!projectsList || !projectsList.content || projectsList.content.length === 0) return;
    projectsList.content.forEach(project => handleProjectStatus(project));

    return () => {
      eventSources.forEach((es, projectId) => {
        es.close();
      });
    };
  }, [projectsList]);

  const { data } = useQuery({
    queryKey: [queryKeys.userInfo, currentUser?.id],
    queryFn: () => userAPI.getUserById(Number(currentUser?.id)),
    enabled: !!currentUser?.id,
    select: res => res.data
  });

  const projectChangeHandler = useCallback(
    (value: string) => {
      setSelectedProjectId(value);
      setSelectedVersion(null);
      const pathSegments = location.pathname.split('/');

      const projectIndex = pathSegments.indexOf('projects');
      if (projectIndex !== -1 && pathSegments.length > projectIndex + 1) {
        pathSegments[projectIndex + 1] = value;

        // Check if there's a section after the project ID
        if (pathSegments.length > projectIndex + 2) {
          const section = pathSegments[projectIndex + 2];

          // Keep only up to the section level (remove scope ID if present)
          if (section === appRoutes.scopes && pathSegments.length > projectIndex + 3) {
            pathSegments.length = projectIndex + 3;
          }
        }
      }

      const newPathname = pathSegments.join('/');
      navigate(newPathname + location.search);
    },
    [location.pathname, location.search, navigate, setSelectedProjectId, setSelectedVersion]
  );

  const shouldRenderDropdown =
    selectedProjectId &&
    projectsList?.content &&
    projectsList.content.length > 0 &&
    !excludedRoutes.includes(getPathName(location.pathname));

  const getCookie = (name: string) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts?.pop()?.split(';').shift();
    return null;
  };

  const logout = async () => {
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
    const token = getCookie('XSRF-TOKEN');
    try {
      const response = await fetch(`${apiBaseUrl}/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-XSRF-TOKEN': token as string
        }
      });

      const data = await response.json();
      clearStorage();
      if (data && data.logoutUrl) {
        window.location.href = data.logoutUrl;
      } else {
        window.location.href = '/';
      }
    } catch {
      window.location.href = '/';
    }
  };

  const closeHandler = () => {
    form.resetFields();
    setPasswordChangePopupOpen(false);
  };

  const updatePasswordHandler = () => {
    const data: PasswordChangeDTO = {
      currentPassword: formValues['currentPassword'],
      newPassword: formValues['newPassword'],
      confirmPassword: formValues['confirmPassword']
    };
    mutate(data);
  };

  const items: MenuProps['items'] = [
    {
      key: 'Company name',
      label: (
        <CompanyName>{capitalizeWords(currentUser?.companyName) || 'Company name'}</CompanyName>
      ),
      disabled: true
    },
    {
      key: 'username',
      label: capitalizeWords(currentUser?.name) || 'User',
      onClick: () => setIsEditModalOpen(true)
    },
    {
      key: 'role',
      label: currentUser?.role?.name || 'Role',
      disabled: true
    },
    {
      key: 'Change password',
      label: 'Change password',
      onClick: () => setPasswordChangePopupOpen(true)
    },
    {
      key: 'switch-company',
      label: 'Switch Company',
      onClick: () => setIsSwitchCompanyOpen(true)
    },
    {
      key: 'logout',
      label: 'Logout',
      onClick: logout,
      icon: <AiOutlineLogout />
    }
  ];

  return (
    <StyledHeader>
      <NavbarActions>
        {shouldRenderDropdown && (
          <>
            <SearchDropdown
              width='200px'
              label='Project'
              options={
                projectsList?.content
                  ? projectsList.content.map(project => ({
                      label: project.name || '',
                      value: String(project.projectId)
                    }))
                  : []
              }
              value={selectedProjectId}
              onChange={projectChangeHandler}
            />
            {isDocumentsScreen && (
              <Flex align='center' gap={10}>
                <VersionStyles>Doc Version</VersionStyles>
                <CreatableSearchSelect
                  initialOptions={
                    versionList?.content?.map(item => item.documentSetVersion || '') || []
                  }
                  placeholder='Versions'
                  isMultiple={false}
                  defaultSelectedValues={[String(selectedVersion?.version || '')]}
                  onSelectionChange={(selectedValues: string[]) => {
                    setSelectedVersion({
                      version: selectedValues[0] || '',
                      versionId:
                        (versionList?.content &&
                          versionList?.content?.filter(
                            value => value.documentSetVersion === selectedValues[0]
                          )[0]?.id) ||
                        null
                    });
                  }}
                />
              </Flex>
            )}
          </>
        )}
        <Flex gap={16} align='center' justify='flex-end'>
          <Popover
            placement='bottomLeft'
            title={<HelpTitle>Need Support?</HelpTitle>}
            trigger='click'
            content={
              <HelpPopoverContainer vertical>
                <HelpText>Having trouble or stuck somewhere?</HelpText>
                <HelpContact>
                  <span>Email Support -&nbsp;</span>
                  <a href='mailto:<EMAIL>'><EMAIL></a>
                </HelpContact>
                <HelpContact>
                  <span>Contact - </span>
                  <a href='tel:+12407932942'>******-793-2942</a>
                </HelpContact>
              </HelpPopoverContainer>
            }
          >
            <IoIosHelpCircleOutline size='30px' cursor='pointer' />
          </Popover>
          <Dropdown menu={{ items }} trigger={['hover']}>
            {currentUser?.profilePictureFileName ? (
              <ProfileImage
                src={`${import.meta.env.VITE_S3_BASE_URL}/${currentUser?.profilePictureFileName}`}
              />
            ) : (
              <FaRegCircleUser size='25px' cursor='pointer' />
            )}
          </Dropdown>
        </Flex>
      </NavbarActions>
      <Modal
        open={passwordChangePopupOpen}
        closeIcon={null}
        maskClosable={false}
        title='Change Password'
        onCancel={closeHandler}
        okText={'Update password'}
        onOk={updatePasswordHandler}
      >
        <StyledForm layout='vertical' form={form}>
          <Form.Item
            label='Current password'
            name='currentPassword'
            rules={[{ required: true, message: changePasswordInputText.currentPassword }]}
          >
            <Input.Password placeholder={changePasswordInputText.currentPasswordPlaceholder} />
          </Form.Item>
          <Form.Item
            label='New password'
            name='newPassword'
            rules={[{ required: true, message: changePasswordInputText.newPassword }]}
          >
            <Input.Password placeholder={changePasswordInputText.newPasswordPlaceholder} />
          </Form.Item>
          <Form.Item
            label='Confirm password'
            name='confirmPassword'
            rules={[{ required: true, message: changePasswordInputText.confirmPassword }]}
          >
            <Input.Password placeholder={changePasswordInputText.confirmPasswordPlaceholder} />
          </Form.Item>
        </StyledForm>
      </Modal>
      {isEditModalOpen && (
        <AddUser
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={true}
          selectedUser={data!}
        />
      )}
      {isSwitchCompanyOpen && (
        <SwitchCompanyModal
          open={isSwitchCompanyOpen}
          onClose={() => setIsSwitchCompanyOpen(false)}
        />
      )}
    </StyledHeader>
  );
};

export default Navbar;
