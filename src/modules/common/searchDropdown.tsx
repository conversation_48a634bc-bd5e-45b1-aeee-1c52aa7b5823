import { Select, Input, SelectProps } from 'antd';
import React, { useMemo, useState } from 'react';
import styled from 'styled-components';

interface Option {
  value: string;
  label: string;
}

interface SearchDropdownProps {
  label?: string;
  options: Option[];
  width?: string;
}

const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
`;

const StyledLabel = styled.label`
  font-weight: 400;
  font-size: 18px;
  line-height: 100%;
`;

const StyledInput = styled(Input)`
  margin: 8px;
  width: calc(100% - 16px);
`;

const StyledSelect = styled(Select)<{ width?: string }>`
  width: ${({ width }) => width || '100%'};
`;

const SearchDropdown: React.FC<SearchDropdownProps & SelectProps> = ({
  label,
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  width,
  ...props
}) => {
  const [searchText, setSearchText] = useState('');

  const filteredOptions = useMemo(
    () => options.filter(option => option.label.toLowerCase().includes(searchText.toLowerCase())),
    [options, searchText]
  );

  return (
    <Container>
      {label && <StyledLabel>{label}</StyledLabel>}
      <StyledSelect
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        width={width}
        dropdownRender={menu => (
          <>
            <StyledInput
              placeholder='Search...'
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
            />
            {menu}
          </>
        )}
        {...props}
      >
        {filteredOptions.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.label}
          </Select.Option>
        ))}
      </StyledSelect>
    </Container>
  );
};

export default SearchDropdown;
