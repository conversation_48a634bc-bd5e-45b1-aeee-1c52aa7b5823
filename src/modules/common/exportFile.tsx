import { FileExcelOutlined, FileTextOutlined, } from '@ant-design/icons';
import { Dropdown, MenuProps } from 'antd';
import { CSVLink } from 'react-csv';
import { IoMdLogOut } from 'react-icons/io';
import { themeTokens } from 'src/theme/tokens';
import { Button } from 'src/view/components';
import * as XLSX from 'xlsx-js-style';
import { useState } from 'react';

type ExportFileType = {
  data: any[];
  columns: { label: string; key: string }[];
  filename: string;
  projectName?: string;
  visibleItem?: any;
};

// Create a custom icon component for the export button
const ExportIcon = () => (
  <IoMdLogOut
    size={'18px'}
    color={themeTokens.whiteBg}
    style={{ rotate: '-90deg' }}
  />
);

const ExportFile: React.FC<ExportFileType> = ({
  data,
  columns,
  filename,
  projectName,
  visibleItem = { 'xlsx': 1, 'csv': 1, },
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleExcelExport = () => {
    const nameOfTheProject = `Project Name : ${projectName}`;

    const filteredData = data.map((row: Record<string, unknown>) =>
      columns.reduce(
        (obj, col) => {
          obj[col.key] = row[col.key];
          return obj;
        },
        {} as Record<string, unknown>
      )
    );

    let ws;
    if (projectName) {
      ws = XLSX.utils.aoa_to_sheet([
        [nameOfTheProject], // A1
        []
      ]);
    } else {
      ws = XLSX.utils.aoa_to_sheet([]);
    }

    const headerKeys = columns.map(col => col.key);
    const headerLabels = columns.map(col => col.label);

    const lastColIndex = headerKeys.length - 1;

    const styledHeader = headerLabels.map(header => ({
      v: header,
      s: {
        fill: { fgColor: { rgb: themeTokens.yellow } },
        font: { bold: true }
      }
    }));

    ws['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: lastColIndex } },
      { s: { r: 1, c: 0 }, e: { r: 1, c: lastColIndex } }
    ];
    if (projectName) {
      ws['A1'].s = { font: { bold: true }, fill: { fgColor: { rgb: themeTokens.yellow } } };
    }
    const worksheet = XLSX.utils.sheet_add_aoa(ws, [styledHeader], { origin: { r: 3, c: 0 } });
    worksheet['!cols'] = columns?.map(item => (item.key === 'itemNo' ? { wch: 10 } : { wch: 44 }));

    XLSX.utils.sheet_add_json(ws, filteredData, {
      origin: { r: 4, c: 0 },
      skipHeader: true,
      header: headerKeys
    });

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    XLSX.writeFile(workbook, `${filename}.xlsx`);
  };

  // For CSV, styling is not supported in plain text files
  let structuredCSVData: any[];
  if (projectName) {
    structuredCSVData = [
      ['Project Name:', projectName || ''],
      [],
      columns.map(c => c?.label),
      ...data.map(row => columns?.map(c => `\t${String(row[c.key] ?? '')}`))
    ];
  } else {
    structuredCSVData = [
      columns.map(c => c?.label),
      ...data.map(row => columns?.map(c => `\t${String(row[c.key] ?? '')}`))
    ];
  }
  const items: MenuProps['items'] = [
    {
      key: 'csv',
      label: (
        <CSVLink data={structuredCSVData} filename={`${filename}.csv`}>
          CSV
        </CSVLink>
      ),
      icon: <FileTextOutlined />,
      onClick: () => setDropdownOpen(false)
    },
    {
      key: 'xlsx',
      label: 'XLSX',
      icon: <FileExcelOutlined />,
      onClick: () => {
        handleExcelExport();
        setDropdownOpen(false);
      }
    }
  ].filter((e) => visibleItem[e.key]);

  return (
    <Dropdown
      menu={{ items }}
      trigger={['click']}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <div style={{ display: 'inline-block' }}>
        <Button
          type={'Primary'}
          text={"Export"}
          leftIcon={ExportIcon}
          isDisabled={false}
          isActive={true}
          isLoading={false}
          onClick={() => setDropdownOpen(!dropdownOpen)}
        />
      </div>
    </Dropdown>
  );
};

export default ExportFile;
