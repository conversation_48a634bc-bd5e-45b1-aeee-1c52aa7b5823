import { FileExcelOutlined, FileTextOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Button, MenuProps } from 'antd';
import { CSVLink } from 'react-csv';
import { themeTokens } from 'src/theme/tokens';
import * as XLSX from 'xlsx-js-style';

type ExportFileType = {
  data: any[];
  columns: { label: string; key: string }[];
  filename: string;
  projectName?: string;
  scopeName?: string;
};

const ExportFile: React.FC<ExportFileType> = ({
  data,
  columns,
  filename,
  projectName,
  scopeName
}) => {
  const handleExcelExport = () => {
    const nameOfTheProject = `Project Name : ${projectName}`;
    const nameOfTheScope = `Scope Name   : ${scopeName}`;

    const filteredData = data.map((row: Record<string, unknown>) =>
      columns.reduce(
        (obj, col) => {
          obj[col.key] = row[col.key];
          return obj;
        },
        {} as Record<string, unknown>
      )
    );

    let ws;
    if (projectName && scopeName) {
      ws = XLSX.utils.aoa_to_sheet([
        [nameOfTheProject], // A1
        [nameOfTheScope], // A2
        []
      ]);
    } else {
      ws = XLSX.utils.aoa_to_sheet([]);
    }

    const headerKeys = columns.map(col => col.key);
    const headerLabels = columns.map(col => col.label);

    const lastColIndex = headerKeys.length - 1;

    const styledHeader = headerLabels.map(header => ({
      v: header,
      s: {
        fill: { fgColor: { rgb: themeTokens.yellow } },
        font: { bold: true }
      }
    }));

    ws['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: lastColIndex } },
      { s: { r: 1, c: 0 }, e: { r: 1, c: lastColIndex } }
    ];
    if (projectName && scopeName) {
      ws['A1'].s = { font: { bold: true }, fill: { fgColor: { rgb: themeTokens.yellow } } };
      ws['A2'].s = { font: { bold: true }, fill: { fgColor: { rgb: themeTokens.yellow } } };
    }
    const worksheet = XLSX.utils.sheet_add_aoa(ws, [styledHeader], { origin: { r: 3, c: 0 } });
    worksheet['!cols'] = columns?.map(item => (item.key === 'itemNo' ? { wch: 10 } : { wch: 44 }));

    XLSX.utils.sheet_add_json(ws, filteredData, {
      origin: { r: 4, c: 0 },
      skipHeader: true,
      header: headerKeys
    });

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    XLSX.writeFile(workbook, `${filename}.xlsx`);
  };

  // For CSV, styling is not supported in plain text files
  let structuredCSVData: any[];
  if (projectName && scopeName) {
    structuredCSVData = [
      ['Project Name:', projectName || ''],
      ['Scope Name:', scopeName || ''],
      [],
      columns.map(c => c?.label),
      ...data.map(row => columns?.map(c => `\t${String(row[c.key] ?? '')}`))
    ];
  } else {
    structuredCSVData = [
      columns.map(c => c?.label),
      ...data.map(row => columns?.map(c => `\t${String(row[c.key] ?? '')}`))
    ];
  }
  const items: MenuProps['items'] = [
    {
      key: 'csv',
      label: (
        <CSVLink data={structuredCSVData} filename={`${filename}.csv`}>
          CSV
        </CSVLink>
      ),
      icon: <FileTextOutlined />
    },
    {
      key: 'xlsx',
      label: 'XLSX',
      icon: <FileExcelOutlined />,
      onClick: handleExcelExport
    }
  ];

  return (
    <Dropdown menu={{ items }} trigger={['click']}>
      <Button type='primary' icon={<DownOutlined />} iconPosition='end'>
        Export
      </Button>
    </Dropdown>
  );
};

export default ExportFile;
