import { FilterFilled, SearchOutlined } from '@ant-design/icons';
import { Input, Button, Space, InputRef, Checkbox, CheckboxChangeEvent, Flex } from 'antd';
import { ColumnType } from 'antd/es/table';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

interface ColumnFilterDropdownProps {
  dataIndex: string;
  setSelectedKeys: (keys: React.Key[]) => void;
  selectedKeys: React.Key[];
  confirm: () => void;
  clearFilters?: () => void;
  close: () => void;
  documentFilters: string[] | undefined;
}

const DropdownContainer = styled.div`
  padding: 8px;
`;

const StyledSearchInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const StyledButton = styled(Button)`
  width: 90px;
`;

const CheckboxStyles = styled(Checkbox)<{ isVersion: boolean }>`
  & .ant-checkbox + span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 250px;
    padding: 5px;
  }
  text-transform: ${({ isVersion }) => (isVersion ? 'capitalize' : 'unset')};
`;

const ListWrapper = styled.div`
  max-height: 30vh;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  scrollbar-width: none;
  padding-bottom: 10px;
`;

const SpaceWrapper = styled(Space)<{ documentFilters: boolean }>`
  margin-top: ${({ documentFilters }) => (documentFilters ? '10px' : 0)};
`;

const ColumnFilterDropdown: React.FC<ColumnFilterDropdownProps> = ({
  dataIndex,
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  close,
  documentFilters
}) => {
  const inputRef = useRef<InputRef>(null);
  const [searchText, setSearchText] = useState('');

  const filtered = useMemo(() => {
    return (
      documentFilters?.filter(list => list.toLowerCase().includes(searchText.toLowerCase())) || []
    );
  }, [documentFilters, searchText]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  }, []);

  const OnCheckboxClickHandler = (event: CheckboxChangeEvent) => {
    const { checked, value } = event.target;
    const newSelection = checked
      ? [...selectedKeys, value]
      : selectedKeys.filter(item => item !== value);
    setSelectedKeys(newSelection);
  };

  return (
    <DropdownContainer>
      {documentFilters ? (
        <Flex vertical gap={10}>
          <Input
            placeholder='Search titles'
            value={searchText}
            onChange={event => setSearchText(event.target.value)}
          />
          <ListWrapper>
            {filtered.map((item, index) => {
              return (
                <CheckboxStyles
                  isVersion={dataIndex === 'inputDocumentType'}
                  key={index}
                  value={item}
                  checked={selectedKeys.includes(item)}
                  onChange={OnCheckboxClickHandler}
                >
                  {dataIndex === 'inputDocumentType' ? `${item.toLowerCase()}s` : item}
                </CheckboxStyles>
              );
            })}
          </ListWrapper>
        </Flex>
      ) : (
        <StyledSearchInput
          ref={inputRef}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={confirm}
        />
      )}
      <SpaceWrapper documentFilters={!!documentFilters}>
        <StyledButton
          type='primary'
          onClick={() => {
            confirm();
            close();
          }}
          icon={<SearchOutlined />}
          size='small'
        >
          Search
        </StyledButton>
        <StyledButton
          onClick={() => {
            setSelectedKeys([]);
            clearFilters?.();
            confirm();
          }}
          size='small'
        >
          Reset
        </StyledButton>
      </SpaceWrapper>
    </DropdownContainer>
  );
};

const ColumnFilter = (dataIndex: any, documentFilters?: string[]): ColumnType<any> => ({
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
    <ColumnFilterDropdown
      dataIndex={String(dataIndex)}
      setSelectedKeys={setSelectedKeys}
      selectedKeys={selectedKeys}
      confirm={confirm}
      clearFilters={clearFilters}
      close={close}
      documentFilters={documentFilters}
    />
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  ...(documentFilters === undefined && {
    onFilter: (value, record) => {
      const recordValue = record[dataIndex];
      return (
        recordValue &&
        recordValue
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase())
      );
    },
    onFilterDropdownOpenChange: visible => {
      if (visible) {
        setTimeout(() => {}, 100);
      }
    }
  })
});

export default ColumnFilter;
