import { useQuery } from '@tanstack/react-query';
import { Modal, Select, Flex } from 'antd';
import { useEffect, useState } from 'react';
import { PageCompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import { CompanyDTO } from 'src/api/models/company-dto';
import useGlobalStore from 'src/store/useGlobalStore';
import { queryKeys } from '../utils/constant';

interface SwitchCompanyModalProps {
  open: boolean;
  onClose: () => void;
}

const SwitchCompanyModal: React.FC<SwitchCompanyModalProps> = ({ open, onClose }) => {
  const { activeCompany, setActiveCompany } = useGlobalStore();
  const [selected, setSelected] = useState<number | null>(activeCompany?.id || null);

  const { data: companiesList, isLoading } = useQuery({
    queryKey: [queryKeys.companiesList],
    queryFn: () => companyAPI.getCompanies(0, 1000, ['createdAt', 'desc']),
    select: res => {
      if (res.data && typeof res.data === 'object' && 'content' in res.data) {
        return res.data as PageCompanyDTO;
      }
      return { content: [], totalElements: 0 } as PageCompanyDTO;
    },
    staleTime: 30000 // 30 seconds
  });

  useEffect(() => {
    setSelected(activeCompany?.id || null);
  }, [activeCompany]);

  const handleConfirm = () => {
    const company = companiesList?.content?.find((c: CompanyDTO) => c.id === selected);
    if (company) {
      setActiveCompany({ id: company.id!, name: company.name! });
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      title='Switch Company'
      okText='Confirm'
      cancelText='Cancel'
      onOk={handleConfirm}
      okButtonProps={{ disabled: !selected }}
    >
      <Flex vertical gap={24}>
        <Select
          loading={isLoading}
          value={selected}
          onChange={setSelected}
          style={{ width: '100%' }}
          placeholder='Select a company'
        >
          {companiesList?.content?.map((company: CompanyDTO) => (
            <Select.Option key={company.id} value={company.id}>
              {company.name}
            </Select.Option>
          ))}
        </Select>
      </Flex>
    </Modal>
  );
};

export default SwitchCompanyModal;
