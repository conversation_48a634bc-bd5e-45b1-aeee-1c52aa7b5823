import { Layout } from 'antd';
import { ReactNode } from 'react';
import { Outlet } from 'react-router-dom';
import styled from 'styled-components';
import Navbar from './navbar';
import Sidebar from './sidebar';

const { Content } = Layout;

type LayoutComponentProps = {
  children?: ReactNode;
};

const LayoutWrapper = styled(Layout)`
  min-height: 100vh;
`;

const StyledContent = styled(Content)`
  max-height: calc(100vh - 60px);
  overflow: auto;
`;

const LayoutComponent: React.FC<LayoutComponentProps> = ({ children }) => {
  return (
    <LayoutWrapper>
      <Sidebar />
      <Layout>
        <Navbar />
        <StyledContent>{children}</StyledContent>
      </Layout>
    </LayoutWrapper>
  );
};

const MasterLayout = () => {
  return (
    <LayoutComponent>
      <Outlet />
    </LayoutComponent>
  );
};

export default MasterLayout;
