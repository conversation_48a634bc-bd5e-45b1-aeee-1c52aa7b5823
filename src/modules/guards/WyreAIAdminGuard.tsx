import { Result, Button } from 'antd';
import React from 'react';
import { appRoutes } from '../utils/constant';
import { useWyreAIAdminAccess } from '../utils/permissions';

interface WyreAIAdminGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const WyreAIAdminGuard: React.FC<WyreAIAdminGuardProps> = ({
  children,
  redirectTo = `/${appRoutes.projects}`
}) => {
  const { hasAccess } = useWyreAIAdminAccess();

  if (!hasAccess) {
    return (
      <Result
        status='403'
        title='403'
        subTitle='Sorry, you are not authorized to access this page. Only Wyre AI employees with Super Admin or Company User roles can access the Wyre AI Admin section.'
        extra={
          <Button type='primary' onClick={() => (window.location.href = redirectTo)}>
            Back Home
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default WyreAIAdminGuard;
