import { Result, Button } from 'antd';
import React from 'react';
import { appRoutes } from '../utils/constant';
import { useAdminAccess } from '../utils/permissions';

interface AdminGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const AdminGuard: React.FC<AdminGuardProps> = ({
  children,
  redirectTo = `/${appRoutes.projects}`
}) => {
  const { hasAccess } = useAdminAccess();

  if (!hasAccess) {
    return (
      <Result
        status='403'
        title='403'
        subTitle='Sorry, you are not authorized to access this page. Only users with Company Admin or Super Admin roles can access the Admin section.'
        extra={
          <Button type='primary' onClick={() => (window.location.href = redirectTo)}>
            Back Home
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default AdminGuard;
