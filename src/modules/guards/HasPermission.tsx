import { FC, ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { UserPermission, useUserPermissions } from '../utils/permissions';

type HasPermissionProps = {
  requiredPermissions: UserPermission[];
  redirectTo?: string;
  children?: ReactNode;
};

const HasPermission: FC<HasPermissionProps> = ({ requiredPermissions, redirectTo, children }) => {
  const { isPermitted } = useUserPermissions(requiredPermissions);

  if (!isPermitted) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }

    return null;
  }

  return <>{children}</>;
};

export default HasPermission;
