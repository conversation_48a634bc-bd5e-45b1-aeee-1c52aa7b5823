import { FC, ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { UserProjectPermission, useUserProjectPermissions } from '../utils/permissions';

type HasPermissionProps = {
  requiredPermissions: UserProjectPermission[];
  redirectTo?: string;
  children?: ReactNode;
};

const HasProjectPermission: FC<HasPermissionProps> = ({
  requiredPermissions,
  redirectTo,
  children
}) => {
  const { isPermitted } = useUserProjectPermissions(requiredPermissions);

  if (!isPermitted) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }

    return null;
  }

  return <>{children}</>;
};

export default HasProjectPermission;
