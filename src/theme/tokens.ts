const themeTokens = {
  // Primary colors
  primaryColor: '#FF6A34',
  primaryDark: '#003249',
  primaryLight: '#084967',

  // Text colors
  textLight: '#fff',
  textDark: '#201A22',
  textGray: '#565656',
  textLightGray: '#bebcbc',
  textDarkGray: '#4E4E4E',
  textBlack: '#0D0D0D',

  // UI colors
  siderBg: '#003249',
  subSiderBg: '#083f58',
  menuItemSelectedBg: '#084967',
  menuItemHoverBg: '#084967',
  menuSubMenuItemSelectedBg: '#2c6d8b',
  menuSubMenuItemHoverBg: '#2c6d8b',
  tableHeaderBg: '#003249',
  pageBg: '#f6f5f5',
  collapseIconBg: '#2C6D8A',
  collapseIconBorder: '#042433',

  // Form colors
  inputBorder: '#CACACA',
  inputBorderDark: '#656565',

  // Button colors
  buttonDark: '#363636',
  buttonBorder: '#000000',

  // Modal colors
  modalBg: '#D9D9D9',
  whiteBg: '#fff',

  // Link colors
  linkBlue: '#4B5A92',

  // Tag colors
  tagBlue: '#D2DBFC',

  // Checkbox active color
  activeBlue: '#4096ff',

  // Table filter colors
  lightBlue: '#1890ff',

  //xlsx header color
  yellow: 'FFFF00',

  //document scope highlight color
  lightGreen: '#9de0af',

  // Metric section colors
  successGreen: '#52c41a',
  infoBlue: '#1890ff',
  warningOrange: '#fa8c16',
  dangerRed: '#ff4d4f'
};

export { themeTokens };
