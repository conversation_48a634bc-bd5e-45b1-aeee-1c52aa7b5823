import path, { resolve } from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  logLevel: 'info',
  resolve: {
    alias: {
      src: resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true,
    hmr: {
      overlay: true
    }
  },
  build: {
    outDir: 'dist'
  },
  envDir: path.resolve(__dirname, 'environments')
});
